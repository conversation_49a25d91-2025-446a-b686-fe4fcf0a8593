# SugarPay Implementation Guide

## 📋 ขั้นตอนการติดตั้งระบบ

### 1. Database Setup
```bash
# สร้าง Database
mysql -u root -p
CREATE DATABASE sugarpay_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE sugarpay_db;

# รัน Schema
source sugarpay_complete_database_schema.sql;

# รัน Sample Data (ถ้าต้องการ)
source sample_data_and_usage.sql;
```

### 2. การตรวจสอบการติดตั้ง
```sql
-- ตรวจสอบตารางที่สร้าง
SHOW TABLES;

-- ตรวจสอบ Views
SHOW FULL TABLES WHERE Table_type = 'VIEW';

-- ตรวจสอบ Stored Procedures
SHOW PROCEDURE STATUS WHERE Db = 'sugarpay_db';

-- ตรวจสอบ Default MDR Fees
SELECT * FROM mdr_fee_configs WHERE is_active = 1;
```

## 🏗️ โครงสร้างระบบ

### Core Components

#### 1. **Merchant Management**
- ✅ รองรับ 4 ประเภทยอดเงิน: Deposit, Withdraw, Frozen, Wait_Confirm
- ✅ API Integration: Bearer token, x-api-key, endpoint, IP whitelist
- ✅ Auto-create balance เมื่อสร้าง merchant ใหม่

#### 2. **Bank Integration**
- ✅ 3 ประเภทบัญชี: DEPOSIT, WITHDRAW, SAVINGS
- ✅ รองรับ 10 บัญชีต่อประเภท
- ✅ Priority system สำหรับเลือกบัญชี
- ✅ API configs แยกต่างหาก

#### 3. **Transaction Processing**
- ✅ 5 ประเภทธุรกรรม: deposit, withdraw, topup, transfer, settlement
- ✅ MDR fee calculation อัตโนมัติ
- ✅ Double entry accounting
- ✅ Audit trail ครบถ้วน

#### 4. **Fee Management**
- ✅ Flexible fee configuration (percentage/fixed)
- ✅ Time-based effective periods
- ✅ Merchant/Agent specific rates
- ✅ Min/Max fee limits

## 💰 MDR Fee Implementation

### Default Fee Structure
```sql
-- Deposit: 1.5%
INSERT INTO mdr_fee_configs (transaction_type, fee_type, fee_value) 
VALUES ('deposit', 'percentage', 1.5000);

-- Withdraw: 10 THB (ไม่หักจากยอดถอน)
INSERT INTO mdr_fee_configs (transaction_type, fee_type, fee_value) 
VALUES ('withdraw', 'fixed', 10.0000);

-- TopUp: 1.5%
INSERT INTO mdr_fee_configs (transaction_type, fee_type, fee_value) 
VALUES ('topup', 'percentage', 1.5000);

-- Transfer: ฟรี
INSERT INTO mdr_fee_configs (transaction_type, fee_type, fee_value) 
VALUES ('transfer', 'fixed', 0.0000);

-- Settlement: 10 THB (เรียกเก็บแยก)
INSERT INTO mdr_fee_configs (transaction_type, fee_type, fee_value) 
VALUES ('settlement', 'fixed', 10.0000);
```

### Fee Calculation Logic
```sql
-- หา fee config ที่เหมาะสม (Priority: merchant > agent > global)
SELECT fee_type, fee_value, min_fee, max_fee
FROM mdr_fee_configs
WHERE (merchant_id = ? OR merchant_id IS NULL)
  AND (agent_id = ? OR agent_id IS NULL)
  AND transaction_type = ?
  AND is_active = 1
  AND NOW() BETWEEN effective_from AND COALESCE(effective_to, '2099-12-31')
ORDER BY merchant_id DESC, agent_id DESC
LIMIT 1;
```

## 🔄 Transaction Flow

### 1. Deposit Transaction
```sql
-- 1. สร้าง transaction
INSERT INTO transactions (merchant_id, transaction_type, txn_amount, mdr_amount, txn_status)
VALUES (1, 'deposit', 1000.00, 15.00, 'SUCCESS');

-- 2. อัพเดท deposit balance
CALL sp_update_merchant_balance(1, LAST_INSERT_ID(), 'deposit', 'credit', 985.00, 'Deposit after MDR', 1);

-- 3. บันทึก Double Entry
INSERT INTO double_entry_ledger (transaction_id, account_type, account_name, debit_amount, credit_amount)
VALUES 
(LAST_INSERT_ID(), 'asset', 'Deposit_Balance', 985.00, 0.00),
(LAST_INSERT_ID(), 'revenue', 'MDR_Fee_Revenue', 15.00, 0.00),
(LAST_INSERT_ID(), 'asset', 'Cash_Received', 0.00, 1000.00);
```

### 2. Withdraw Transaction
```sql
-- 1. สร้าง transaction (fee ไม่หักจากยอดถอน)
INSERT INTO transactions (merchant_id, transaction_type, txn_amount, withdraw_fee_amount, txn_status)
VALUES (1, 'withdraw', 5000.00, 10.00, 'SUCCESS');

-- 2. หัก withdraw balance
CALL sp_update_merchant_balance(1, LAST_INSERT_ID(), 'withdraw', 'debit', 5000.00, 'Withdraw to bank', 1);

-- 3. เรียกเก็บค่าธรรมเนียมแยก (จาก deposit balance หรือวิธีอื่น)
```

### 3. Transfer Transaction (ฟรี)
```sql
-- 1. สร้าง transaction
INSERT INTO transactions (merchant_id, transaction_type, txn_amount, mdr_amount, txn_status)
VALUES (1, 'transfer', 2000.00, 0.00, 'SUCCESS');

-- 2. หัก deposit balance
CALL sp_update_merchant_balance(1, LAST_INSERT_ID(), 'deposit', 'transfer_out', 2000.00, 'Transfer to withdraw', 1);

-- 3. เพิ่ม withdraw balance
CALL sp_update_merchant_balance(1, LAST_INSERT_ID(), 'withdraw', 'transfer_in', 2000.00, 'Transfer from deposit', 1);
```

## 🔐 Security Implementation

### 1. API Authentication
```php
// ตรวจสอบ Bearer Token
$headers = getallheaders();
$token = str_replace('Bearer ', '', $headers['Authorization'] ?? '');

// ตรวจสอบ x-api-key
$api_key = $headers['x-api-key'] ?? '';

// ตรวจสอบ IP Whitelist
$client_ip = $_SERVER['REMOTE_ADDR'];
$allowed_ips = explode(',', $merchant['ip_whitelist']);
```

### 2. 2FA Implementation
```php
// Google 2FA
if ($user['is_google2fa'] == 1) {
    $google2fa = new Google2FA();
    $valid = $google2fa->verifyKey($user['google2fa_secret'], $input_code);
}

// Amount-based 2FA
if ($transaction_amount >= $merchant['minimum_2fa']) {
    // Require additional verification
}
```

## 📊 Reporting Queries

### 1. Daily Settlement Report
```sql
SELECT 
    DATE(settlement_date) as settle_date,
    COUNT(*) as txn_count,
    SUM(txn_amount) as total_amount,
    SUM(mdr_amount + withdraw_fee_amount) as total_fees,
    SUM(net_amount) as net_settlement
FROM transactions
WHERE settlement_date IS NOT NULL
  AND settlement_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(settlement_date)
ORDER BY settle_date DESC;
```

### 2. Merchant Balance Report
```sql
SELECT 
    m.merchant_code,
    m.merchant_name,
    mb.deposit_balance,
    mb.withdraw_balance,
    mb.frozen_balance,
    mb.wait_confirm_amount,
    (mb.deposit_balance + mb.withdraw_balance) as available_balance,
    mb.last_updated
FROM merchants m
JOIN merchant_balances mb ON m.merchant_id = mb.merchant_id
WHERE m.status = 'active'
ORDER BY m.merchant_code;
```

### 3. Fee Revenue Report
```sql
SELECT 
    DATE(t.txn_date) as revenue_date,
    t.transaction_type,
    COUNT(*) as txn_count,
    SUM(t.txn_amount) as gross_amount,
    SUM(t.mdr_amount) as mdr_revenue,
    SUM(t.withdraw_fee_amount) as withdraw_fee_revenue,
    SUM(t.mdr_amount + t.withdraw_fee_amount) as total_revenue
FROM transactions t
WHERE t.txn_status = 'SUCCESS'
  AND t.txn_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY DATE(t.txn_date), t.transaction_type
ORDER BY revenue_date DESC, t.transaction_type;
```

## 🚀 Performance Optimization

### 1. Database Indexes
```sql
-- Transaction queries
CREATE INDEX idx_txn_merchant_type_date ON transactions (merchant_id, transaction_type, txn_date);
CREATE INDEX idx_txn_status_date ON transactions (txn_status, txn_date);

-- Balance logs
CREATE INDEX idx_balance_merchant_type_date ON balance_logs (merchant_id, balance_type, created_date);

-- Fee configs
CREATE INDEX idx_fee_lookup ON mdr_fee_configs (transaction_type, is_active, effective_from, effective_to);
```

### 2. Query Optimization
```sql
-- ใช้ LIMIT สำหรับ pagination
SELECT * FROM v_transaction_details 
WHERE merchant_id = ? 
ORDER BY txn_date DESC 
LIMIT 50 OFFSET 0;

-- ใช้ date range
SELECT * FROM transactions 
WHERE txn_date BETWEEN '2024-01-01' AND '2024-01-31'
  AND merchant_id = ?;
```

## 🔧 Maintenance Tasks

### 1. Daily Tasks
```sql
-- ตรวจสอบ balance consistency
SELECT merchant_id, 
       (SELECT SUM(amount_after) FROM balance_logs WHERE merchant_id = mb.merchant_id AND balance_type = 'deposit' ORDER BY log_id DESC LIMIT 1) as calc_deposit,
       deposit_balance
FROM merchant_balances mb
HAVING calc_deposit != deposit_balance;

-- Archive old transactions (เก็บ 7 ปี)
DELETE FROM transactions 
WHERE txn_date < DATE_SUB(NOW(), INTERVAL 7 YEAR);
```

### 2. Weekly Tasks
```sql
-- ตรวจสอบ failed transactions
SELECT COUNT(*) as failed_count, DATE(created_date) as fail_date
FROM transactions 
WHERE txn_status = 'FAILED'
  AND created_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY DATE(created_date);
```

## 📝 API Integration Examples

### 1. Webhook Callback
```php
// รับ callback จาก bank
$payload = json_decode(file_get_contents('php://input'), true);

// ตรวจสอบ signature
$signature = hash_hmac('sha256', json_encode($payload), $webhook_secret);

// อัพเดท transaction status
if ($payload['status'] == 'success') {
    $sql = "UPDATE transactions SET txn_status = 'SUCCESS', txn_date = NOW() WHERE txn_hash = ?";
    // Execute และอัพเดท balance
}
```

### 2. Merchant API Call
```php
// ส่งข้อมูลไปยัง merchant
$data = [
    'order_id' => $transaction['order_id'],
    'status' => $transaction['txn_status'],
    'amount' => $transaction['txn_amount'],
    'net_amount' => $transaction['net_amount']
];

$ch = curl_init($merchant['api_endpoint']);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: ' . $merchant['bearer_token'],
    'x-api-key: ' . $merchant['x_api_key'],
    'Content-Type: application/json'
]);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
$response = curl_exec($ch);
```
