<!doctype html>
<html lang="en" data-layout="vertical" data-topbar="light" data-sidebar="dark" data-sidebar-size="lg" data-sidebar-image="none" data-preloader="disable">

<head>

    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="sugarpay66 payment">
    <meta name="keywords" content="sugarpay66 payment">
    <meta name="author" content="member.sugarpay66.io">
    <meta property="og:title" content="Merchant sugarpay66 payment" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="/" />
    <meta property="og:image" content="/public/assets//images/logo/android-chrome-512x512.png" />
    <title>ร้าน tiger-001 - Merchant sugarpay66 payment</title>


    <style>
        :root {
            --theme-bg_color_login: linear-gradient(to right, #bfa2a8, #6e5c60);
            --theme-color_primary:#6e5c60;
        }
    </style>
    
    <!-- For IE6+ -->
    <link rel="shortcut icon" href="public/assets/images/logo/favicon.ico" type="image/x-icon">

    <!-- For all other browsers -->
    <link rel="icon" href="public/assets/images/logo/favicon.ico"/>

    <!-- Different sizes -->
    <link rel="icon" href="public/assets/images/logo/favicon-16x16.png" sizes="16x16">
    <link rel="icon" href="public/assets/images/logo/favicon-32x32.png" sizes="32x32">

    <!-- For Modern Browsers with PNG Support -->
    <link rel="icon" type="image/png" href="public/assets/images/logo/apple-touch-icon.png">

    <!-- Works in Firefox, Opera, Chrome and Safari -->
    <link rel="icon" href="public/assets/images/logo/apple-touch-icon.png">

    <!-- For rounded corners and reflective shine in Apple devices -->
    <link rel="apple-touch-icon" href="public/assets/images/logo/apple-touch-icon.png" />

    <!-- Favicon without reflective shine -->
    <link rel="apple-touch-icon-precomposed" href="public/assets/images/logo/apple-touch-icon.png">

    <!-- jsvectormap css -->
    <link href="public/assets/libs/jsvectormap/css/jsvectormap.min.css" rel="stylesheet" type="text/css" />

    <!--Swiper slider css-->
    <link href="public/assets/libs/swiper/swiper-bundle.min.css" rel="stylesheet" type="text/css" />


    <!-- Sweet Alert css-->
    <link href="public/assets/libs/sweetalert2/sweetalert2.min.css" rel="stylesheet" type="text/css" />

    <link href="public/assets/libs/flatpickr/flatpickr.min.css" rel="stylesheet" type="text/css" />

    <!-- Layout config Js -->
    <script src="public/assets/js/layout.js"></script>
    <!-- Bootstrap Css -->
    <link href="public/assets/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <!-- Icons Css -->
    <link href="public/assets/css/icons.min.css" rel="stylesheet" type="text/css" />
    <!-- App Css-->
    <link href="public/assets/css/app.css?t=1746271760" rel="stylesheet" type="text/css" />

    <!-- custom Css-->
    <link href="public/assets/css/custom.css?t=1746271760" rel="stylesheet" type="text/css" />


    <!--datatable css-->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" />
    <!--datatable responsive css-->
    <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.dataTables.min.css">
    <style type="text/css">
        .text-bold{
            font-weight: bold;
        }
        .text-right{
            text-align: right;
        }
        .modal-xl {
            max-width: 1300px !important;
        }
        #modalAnnouncement img{
            width :100%;
        }
        #modalAnnouncement  p {
            margin-top: 0;
            margin-bottom: 0.2rem;
        }
    </style>
    <style></style></head>

<body>

<!-- Begin page -->
<div id="layout-wrapper">

    <header id="page-topbar">
        <div class="layout-width">
            <div class="navbar-header">
                <div class="d-flex">
                    <!-- LOGO -->
                    <div class="navbar-brand-box horizontal-logo">
                        <a href="" class="logo logo-dark">
                                <span class="logo-sm">
                                    <img src="public/assets/images/logo/logo-sm.png" alt="" height="22">
                                </span>
                            <span class="logo-lg">
                                    <img src="public/assets/images/logo/logo.png" alt="" height="48">
                                </span>
                        </a>

                        <a href="index.html" class="logo logo-light">
                                <span class="logo-sm">
                                    <img src="public/assets/images/logo/logo-sm.png" alt="" height="22">
                                </span>
                            <span class="logo-lg">
                                    <img src="public/assets/images/logo/logo.png" alt="" height="48">
                                </span>
                        </a>
                    </div>

                    <button type="button" class="btn btn-sm px-3 fs-16 header-item vertical-menu-btn topnav-hamburger"
                            id="topnav-hamburger-icon">
                            <span class="hamburger-icon">
                                <span></span>
                                <span></span>
                                <span></span>
                            </span>
                    </button>
                </div>

                <div class="d-flex align-items-center">
                                        <div class="ms-1 header-item d-none d-sm-flex">
                        <button type="button" class="btn btn-icon btn-topbar btn-ghost-secondary rounded-circle"
                                data-toggle="fullscreen">
                            <i class='bx bx-fullscreen fs-22'></i>
                        </button>
                    </div>
                    <div class="dropdown ms-sm-3 header-item topbar-user">
                        <button type="button" class="btn" id="page-header-user-dropdown" data-bs-toggle="dropdown"
                                aria-haspopup="true" aria-expanded="false">
                                <span class="d-flex align-items-center">
                                    <img class="rounded-circle header-profile-user" src="public/assets/images/logo/logo-sm.png"
                                         alt="Header Avatar">
                                    <span class="text-start ms-xl-2">
                                        <span class="d-none d-xl-inline-block ms-1 fw-medium user-name-text">tiger-001</span>
                                        <span class="d-none d-xl-block ms-1 fs-12 text-muted user-name-sub-text"></span>
                                    </span>
                                </span>
                        </button>
                        <div class="dropdown-menu dropdown-menu-end">
                            <!-- item-->
                            <h6 class="dropdown-header">ยินดีต้อนรับ tiger-001</h6>
                                                        <a class="dropdown-item" href="login/logout"><i
                                        class="mdi mdi-logout text-muted fs-16 align-middle me-1"></i> <span
                                        class="align-middle" data-key="t-logout">ออกจากระบบ</span></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- ========== App Menu ========== -->
    <div class="app-menu navbar-menu">
        <!-- LOGO -->
        <div class="navbar-brand-box">
            <!-- Dark Logo-->
            <a href="" class="logo logo-dark">
                    <span class="logo-sm">
                        <img src="public/assets/images/logo/logo-sm.png" alt="" height="22">
                    </span>
                <span class="logo-lg">
                        <img src="public/assets/images/logo/logo.png" alt="" height="48">
                    </span>
            </a>
            <!-- Light Logo-->
            <a href="" class="logo logo-light">
                    <span class="logo-sm">
                        <img src="public/assets/images/logo/logo-sm.png" alt="" height="22">
                    </span>
                <span class="logo-lg">
                        <img src="public/assets/images/logo/logo.png" alt="" height="48">
                    </span>
            </a>
            <button type="button" class="btn btn-sm p-0 fs-20 header-item float-end btn-vertical-sm-hover" id="vertical-hover">
                <i class="ri-record-circle-line"></i>
            </button>
        </div>

        <div id="scrollbar">
            <div class="container-fluid">

                <div id="two-column-menu">
                </div>
                <ul class="navbar-nav mt-3" id="navbar-nav">
                                            <li class="nav-item">
                            <a class="nav-link menu-link " href="dashboard.html" >
                                <i class="ri-article-line"></i> <span data-key="t-users">Dashboard</span>
                            </a>
                        </li>
                                        
                                        <li class="nav-item">
                        <a class="nav-link menu-link " href="transaction.html" >
                            <i class="ri-article-line"></i> <span data-key="t-users">Transaction</span>
                        </a>
                    </li>
                                                                <li class="nav-item">
                            <a class="nav-link menu-link " href="withdraw_approve.html" >
                                <i class="ri-article-line"></i> <span data-key="t-users">Withdraw Approve</span>
                            </a>
                        </li>
                                                            <li class="nav-item">
                        <a class="nav-link menu-link " href="bank_statement.html" >
                            <i class="ri-bank-line"></i> <span data-key="t-users">Statement</span>
                        </a>
                    </li>
                                                            <li class="nav-item">
                        <a class="nav-link menu-link " href="summary_transfer.html" >
                            <i class="ri-file-transfer-line"></i> <span data-key="t-users">Settlement</span>
                        </a>
                    </li>
                    
                                        <a class="nav-link menu-link " href="#sidebarReport" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarReport">
                        <i class="ri-article-line"></i> <span data-key="t-report">Report</span>
                    </a>
                    <div class="collapse menu-dropdown" id="sidebarReport">
                        <ul class="nav nav-sm flex-column">

                                                        <li class="nav-item">
                                <a href="report_agent_daily_realtime.html" class="nav-link" data-key="t-report">Daily Summary Report</a>
                            </li>
                             

                                                            <li class="nav-item">
                                    <a href="report_withdraw_channel.html" class="nav-link " data-key="t-users">Withdraw Channel Report</a>
                                </li>
                            
                                                        <li class="nav-item">
                                <a href="report_withdraw.html" class="nav-link" data-key="t-report">Withdraw Activity Report</a>
                            </li>
                             

                                                        <li class="nav-item">
                                <a href="report_deposit.html" class="nav-link" data-key="t-report">Deposit Activity Report</a>
                            </li>
                             
                            
                            <!--                            <li class="nav-item">-->
<!--                                <a href="--><!--" class="nav-link" data-key="t-report"> Withdraw Slip</a>-->
<!--                            </li>-->
                                                    </ul>
                    </div>
                    
                  

                                        <li class="nav-item">
                        <a class="nav-link menu-link " href="withdraw_fund.html" >
                            <i class="ri-file-transfer-line"></i> <span data-key="t-users">Withdraw Fund</span>
                        </a>
                    </li>
                                                            <li class="nav-item">
                        <a class="nav-link menu-link " href="scanslip-step-import.html" >
                            <i class=" ri-qr-code-fill"></i> <span data-key="t-users">STEP 1 Import Slip</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link menu-link " href="scanslip-step-verify.html" >
                            <i class=" ri-qr-code-fill"></i> <span data-key="t-users">STEP 2 Verify Slip</span>
                        </a>
                    </li>
                    

                                        <a class="nav-link menu-link " href="#sidebarTools" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarTools">
                        <i class="ri-article-line"></i> <span data-key="t-report">Tools</span>
                    </a>
                    <div class="collapse menu-dropdown" id="sidebarTools">
                        <ul class="nav nav-sm flex-column">
                             
                                                        <li class="nav-item">
                                <a href="black_list.html" class="nav-link" data-key="t-report">Blacklist Manage</a>
                            </li>
                             
 
                        </ul>
                    </div>
                    

                    <li class="nav-item">
                        <a class="nav-link menu-link " href="profile.html" >
                            <i class="ri-folder-user-line"></i> <span data-key="t-users">Profile</span>
                        </a>
                    </li>
                                            <a class="nav-link menu-link " href="#sidebarSetupUser" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarSetupUser">
                            <i class="ri-folder-user-line"></i> <span data-key="t-setup-user">Settings</span>
                        </a>
                                                <div class="collapse menu-dropdown " id="sidebarSetupUser">
                            <ul class="nav nav-sm flex-column">
                                <li class="nav-item">
                                    <a href="user_group.html" class="nav-link " data-key="t-user-group"> User Groups </a>
                                </li>
                                <li class="nav-item">
                                    <a href="user_permission.html" class="nav-link " data-key="t-permission"> Group Permission </a>
                                </li>
                                <li class="nav-item">
                                    <a href="user.html" class="nav-link " data-key="t-users"> Sub Users </a>
                                </li>
                            </ul>
                        </div>
                                        <li class="nav-item">
                        <a class="nav-link menu-link " href="login/logout" >
                            <i class="ri-logout-box-r-line"></i> <span data-key="t-users">Logout</span>
                        </a>
                    </li>
<!--                    <li class="nav-item">-->
<!--                        <a class="nav-link menu-link " href="https://vizpay.supportnow.me/" target="_blank">-->
<!--                            <img src="--><!--images/ticket_now.jpg" alt="" height="60">-->
<!--                        </a>-->
<!--                    </li>-->
                </ul>
            </div>
            <!-- Sidebar -->
        </div>

        <div class="sidebar-background"></div>
    </div>
    <!-- Left Sidebar End -->
    <!-- Vertical Overlay-->
    <div class="vertical-overlay"></div>

    <!-- ============================================================== -->
    <!-- Start right Content here -->
    <!-- ============================================================== -->
    <div class="main-content">

<div class="page-content">
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                    <h4 class="mb-sm-0">Deposit Activity Report </h4>

                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item active"></li>
                        </ol>
                    </div>

                </div>
            </div>
        </div>
        <!-- end page title -->

        <div class="row">
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header align-items-center d-flex p-2">
                        <div class="float-end align-items-end text-right" style="width:100%">
                            <button type="button" onClick="setDateAndSearchData('YESTERDAY',this)" class="btn btn-filter-date btn-sm btn-dark w-xs">เมื่อวาน</button>
                            <button type="button" onClick="setDateAndSearchData('LAST7DAY',this)" class="btn btn-filter-date btn-sm btn-dark w-xs">7 วันที่ผ่านมา</button>
                            <button type="button" onClick="setDateAndSearchData('THISWEEK',this)" class="btn btn-filter-date btn-sm btn-dark w-xs">สัปดาห์นี้</button>
                            <button type="button" onClick="setDateAndSearchData('THISMONTH',this)" class="btn btn-filter-date btn-sm btn-secondary w-xs">เดือนนี้</button>
                            <button type="button" onClick="setDateAndSearchData('LASTMONTH',this)" class="btn btn-filter-date btn-sm btn-dark w-xs">เดือนที่แล้ว</button>
                            <button type="button" onClick="setDateAndSearchData('ALL',this)" class="btn btn-filter-date btn-sm btn-dark w-xs">ทั้งหมด</button>
                        </div>
                    </div><!-- end card header -->
                    <div class="card-body p-2">
                        <div class="live-preview mb-2">
                            <form action="#" id="form_search" method="post">
                                <div class="row g-3">

                                    <div class="col-md-6 mt-2">
                                        <label for="field1" class="form-label">Start Date</label>
                                        <input type="text" class="form-control" id="start_date" value="2025-04-19" >
                                    </div>
                                    <div class="col-md-6 mt-2">
                                        <label for="field1" class="form-label">End Date</label>
                                        <input type="text" class="form-control" id="end_date" value="2025-05-03">
                                    </div>
                                  
                                 
                                    <div class="col-md-6 mt-1">
                                        <label for="field1" class="form-label">Transaction Type</label>
                                        <select class="form-select"  name="transaction_type" id="transaction_type" required>
                                            <option value="ALL">All</option>
                                            <option value="SETTLED">settled</option>
                                            <option value="MOVE">Move</option>
                                            <option value="DEPOSIT">Deposit</option>
                                        </select>
                                    </div> 
                                  
                                </div>
                                <div>
                                    <div class="row g-3 mt-1">
                                        <div class="col-12 text-center">
                                            <button type="button" class="btn btn-success  waves-effect waves-light" onclick="get_datatable_list();"><i class="mdi mdi-filter-plus-outline"></i> Search</button>
                                            <button type="reset" class="btn btn-soft-dark waves-effect waves-light" onclick="setTimeout(get_datatable_list,300)"><i class="mdi mdi-filter-remove-outline"></i> Clear</button>
                                            <button type="button" class="btn btn-primary" onClick="exportData()">Export CSV</button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header align-items-center d-flex">
                            <h4 class="card-title mb-0 flex-grow-1">สรุปรายการบัญชีทั้งหมดของร้านค้า </h4>
                        </div>
                        <div class="card-body">
                            <div class="row"> 
                                <div class="table-responsive">
                                    <table  class="table nowrap dt-responsive align-middle " style="width:100%">
                                        <tbody>

                                            <tr >
                                                <td class="text-left" style="font-size:16px; width:50%" >ยอดฝากเข้า</td>
                                                <td class="text-right"  style="font-size:16px; color:blue; ">
                                                    <span id="result_total_deposit" >
                                                    </span>
                                                </td>
                                            </tr>
                                            <tr >
                                                <td class="text-left" style="font-size:16px; width:50%" >ยอดโยกเข้าบัญชีถอน</td>
                                                <td class="text-right"  style="font-size:16px; color:red; ">
                                                    <span id="result_total_move" style="color: red;">
                                                    </span>
                                                </td>
                                            </tr>
                                            <tr >
                                                <td class="text-left" style="font-size:16px; width:50%" >ยอดการ Settlement</td>
                                                <td class="text-right" style="font-size:16px; color:red; ">
                                                    <span id="result_total_settled" style="color: red;border-bottom: 1px solid red;"></span>
                                                </td>
                                            </tr>
                                           
                                            <tr >
                                                <td class="text-left" colspan="2" style="font-size:16px; ">
                                                    &nbsp;
                                                </td>
                                            </tr>
                                            
                                            <tr >
                                                <td class="text-left" style="font-size:16px; width:50%" >
                                                บัญชีฝาก ยอดคงเหลือ
                                                </td>
                                                <td class="text-right"  style="font-size:16px; color:blue; ">
                                                    <span id="result_total_balance" style="color: blue;border-bottom: 3px double black;font-weight: bolder;">
                                                    </span>
                                                </td>
                                            </tr>

                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row h-100">

            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">

                            <div class="col-md-12">
                                <div class="d-flex align-items-center">
                                    <div class="flex-grow-1 ms-3">
                                        <p class="text-uppercase fw-semibold fs-16 mb-1 text-center">สรุปยอดตาม Transaction (ตรงกับข้อมูลตารางข้างล่าง)</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="d-flex align-items-center">
                                    <div class="flex-grow-1 ms-3">
                                        <p class="text-uppercase fw-semibold fs-12 text-muted mb-1">Total Amount</p>
                                        <h4 class=" mb-0"><span class="counter-value fs-17" id="total_deposit_amount">-</span></h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="d-flex align-items-center">
                                    <div class="flex-grow-1 ms-3">
                                        <p class="text-uppercase fw-semibold fs-12 text-muted mb-1">Total MDR</p>
                                        <h4 class=" mb-0"><span class="counter-value fs-17" id="total_deposit_mdr">-</span></h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="d-flex align-items-center">
                                    <div class="flex-grow-1 ms-3">
                                        <p class="text-uppercase fw-semibold fs-12 text-muted mb-1">Total Transfer</p>
                                        <h4 class=" mb-0"><span class="counter-value fs-17" id="total_deposit_transfer">-</span></h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="d-flex align-items-center">
                                    <div class="flex-grow-1 ms-3">
                                        <p class="text-uppercase fw-semibold fs-12 text-muted mb-1">Total Net</p>
                                        <h4 class=" mb-0"><span class="counter-value fs-17" id="total_deposit_net">-</span></h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div><!-- end card body -->
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="d-flex align-items-center">
                                    <div class="flex-grow-1 ms-3">
                                        <p class="text-uppercase fw-semibold fs-16 mb-1 text-center">สรุปยอดตาม Settlement ให้ร้านค้า</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-flex align-items-center">
                                    <div class="flex-grow-1 ms-3">
                                        <p class="text-uppercase fw-semibold fs-12 text-muted mb-1">Total Amount</p>
                                        <h4 class=" mb-0"><span class="counter-value fs-17" id="total_settled_amount">-</span></h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-flex align-items-center">
                                    <div class="flex-grow-1 ms-3">
                                        <p class="text-uppercase fw-semibold fs-12 text-muted mb-1">Total MDR</p>
                                        <h4 class=" mb-0"><span class="counter-value fs-17" id="total_settled_mdr">-</span></h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-flex align-items-center">
                                    <div class="flex-grow-1 ms-3">
                                        <p class="text-uppercase fw-semibold fs-12 text-muted mb-1">Total Net</p>
                                        <h4 class=" mb-0"><span class="counter-value fs-17" id="total_settled_net">-</span></h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div><!-- end card body -->
                </div>
            </div>
        </div>
        

     
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header align-items-center d-flex">
                        <h4 class="card-title mb-0 flex-grow-1">บัญชีฝาก ยอดคงเหลือ</h4>
                       
                    </div><!-- end card header -->
                    <div class="card-body">
                         
                        <!-- Datatable -->
                        <div class="table-responsive">
                            <table id="datatable-list" class="table nowrap dt-responsive align-middle table-hover table-bordered" style="width:100%">
                                <thead>
                                <tr>
                                    <th class="" rowspan="2">Merchant Name</th>
                                    <th class="text-center" colspan="6">Transaction</th>
                                    <th class="text-center" colspan="2">Bank Infomation</th>
                                    <th class="text-center" rowspan="2">Reference</th>
                                </tr>
                                <tr>
                                    <th class="text-center" >Type</th>
                                    <th class="text-center" >Date</th>
                                    <th class="text-center" >Amount</th>
                                    <th class="text-center" >MDR Amount</th>
                                    <th class="text-center" >NET</th>
                                    <th class="text-center" >Balance</th>

                                    <th class="text-center" >Bank Name</th>
                                    <th class="text-center" >Account No</th>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!--end col-->
        </div>
        <!--end row-->

    </div>


    <div id="modalSlip" class="modal fade" tabindex="-1" aria-labelledby="modalSlipLabel" aria-hidden="true" style="display: none;">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalSlipLabel">Slip Transfer</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"> </button>
                </div>
                <div class="modal-body">
                    <div id="htmlEmail"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

</div>


<footer class="footer">
    <div class="container-fluid">
        <div class="row">
            <div class="col-sm-6">
                Copyright <script>document.write(new Date().getFullYear())</script> © member.sugarpay66.io            </div>
            <div class="col-sm-6">
                
            </div>
        </div>
    </div>
</footer>
</div>
<!-- end main content-->

</div>
<!-- END layout-wrapper -->

<!--start back-to-top-->
<button onclick="topFunction()" class="btn btn-primary btn-icon" id="back-to-top">
    <i class="ri-arrow-up-line"></i>
</button>
<!--end back-to-top-->

<!--preloader-->
<div id="preloader">
    <div id="status">
        <div class="spinner-border text-primary avatar-sm" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
</div>

<div class="modal fade" id="modelPreviewImage" tabindex="-1" aria-labelledby="modelPreviewImage" aria-modal="true">
    <div class="modal-dialog ">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalgridLabel">Preview Image</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <img src="" id="imgPreviewImage" style="width: 100%;" alt="">
            </div>
        </div>
    </div>
</div>



<!-- JAVASCRIPT -->
<script src="public/assets/libs/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="public/assets/libs/simplebar/simplebar.min.js"></script>
<script src="public/assets/libs/node-waves/waves.min.js"></script>
<script src="public/assets/libs/feather-icons/feather.min.js"></script>
<script src="public/assets/js/pages/plugins/lord-icon-2.1.0.js"></script>
<script src="public/assets/js/plugins.js?date=202403042044"></script>

<script src="public/assets/js/pages/notifications.init.js"></script>

<script src="https://code.jquery.com/jquery-3.6.0.min.js" integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>

<!--datatable js-->
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<!-- <script src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script> -->
<script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>

<!-- Sweet Alerts js -->
<script src="public/assets/libs/sweetalert2/sweetalert2.min.js"></script>

<script src="public/assets/libs/flatpickr/flatpickr.min.js"></script>


<!-- Sweet alert init js-->
<script src="public/assets/js/pages/sweetalerts.init.js"></script>

<!-- Moment js -->
<script src="public/assets/libs/moment/moment.js"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.blockUI/2.70/jquery.blockUI.min.js" integrity="sha512-eYSzo+20ajZMRsjxB6L7eyqo5kuXuS2+wEbbOkpaur+sA2shQameiJiWEzCIDwJqaB0a4a6tCuEvCOBHUg3Skg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.blockUI/2.70/jquery.blockUI.js" integrity="sha512-QSb5le+VXUEVEQbfljCv8vPnfSbVoBF/iE+c6MqDDqvmzqnr4KL04qdQMCm0fJvC3gCWMpoYhmvKBFqm1Z4c9A==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<!-- <script src="public/assets/js/pages/datatables.init.js"></script> -->

<!-- dropzone min -->
<script src="public/assets/libs/dropzone/dropzone-min.js"></script>
<!-- filepond js -->
<script src="public/assets/libs/filepond/filepond.min.js"></script>
<script src="public/assets/libs/filepond-plugin-image-preview/filepond-plugin-image-preview.min.js"></script>
<script src="public/assets/libs/filepond-plugin-file-validate-size/filepond-plugin-file-validate-size.min.js"></script>
<script src="public/assets/libs/filepond-plugin-image-exif-orientation/filepond-plugin-image-exif-orientation.min.js"></script>
<script src="public/assets/libs/filepond-plugin-file-encode/filepond-plugin-file-encode.min.js"></script>

<!-- App js -->
<script src="public/assets/js/app.js?t=1746205200"></script>
<script>

 
    var tableDeposit =  { table : '',tableDrawStatus :true, tooltipTriggerList : '',tooltipList : ''};
    document.addEventListener("DOMContentLoaded",function(){
        new DataTable(".alternative-pagination",{
        })
        
    })
    $(document).ready(function () {
                        
                
    });

    function unshowAnnouncement(id){
        setCookie("cookie_announcement", id, 7); 
        $("#modalAnnouncement").modal('hide')
    }

    function setCookie(name, value, days) {
        var expires = "";
        if (days) {
            var date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + (value || "") + expires + "; path=/";
    }
    function getCookie(name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(';');
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    }

    $(document).on('click','img.previewImage',function(){
        let src = $(this).attr('src');
        $("#imgPreviewImage").attr('src', src);

        $("#modelPreviewImage").modal("show");
    });

    function formatNumber(value,digit = 2){
        var val = isNaN(value) ? 0 : value;
        var number = parseFloat(val).toFixed(digit).toLocaleString(undefined, {
            maximumFractionDigits: digit
        });
        return number.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
    function preview_image(event, obj) {
        var output = document.getElementById('show_' + obj.id);
        output.src = URL.createObjectURL(event.target.files[0]);
    }

   
    function copyButton(elm, copyText, afterTextButton){
        copyToClipboard(copyText);
        $(elm).html(afterTextButton);
    }
    function copyToClipboard(text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(
                function () { 
                    console.log('Text copied to clipboard'); 
                    Swal.fire({
                        position: 'top-end',
                        icon: 'success',
                        title: 'Text copied to clipboard',
                        showConfirmButton: false,
                        timer: 1500,
                        
                    })
                },
                function (err) { console.error('Could not copy text: ', err); }
            );
        } else {
            let input = document.createElement('textarea');
            input.style.position = 'fixed';
            input.style.zIndex = 9999;
            input.value = text;
            document.body.appendChild(input);
            input.select();
            input.focus();
            document.execCommand('copy');
            document.body.removeChild(input);
        }

    }


</script>

<script lang="javascript" src="https://cdn.sheetjs.com/xlsx-latest/package/dist/xlsx.full.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/vue/2.3.4/vue.min.js"></script>
<script>
    var url_ajax = '/audit_deposit_activity_history/report/';
   
    // var yearSelect = $("#filter_year option:selected").val();
    // var monthSelect = $("#filter_month option:selected").val();

    // var month = monthSelect.padStart(2, "0");
    // var start_date = moment().startOf('isoWeek').format('YYYY-MM-DD').toString();
    // var end_date = moment().endOf('isoWeek').format('YYYY-MM-DD').toString();
    var start_date = moment().startOf('month').format('YYYY-MM-DD').toString();
    var end_date = moment().endOf('month').format('YYYY-MM-DD').toString();

    $("#start_date").val(start_date)
    $("#end_date").val(end_date) 
 
    
    let table =  '';
    let tableDrawStatus =  true;
    let tooltipTriggerList ='';
    let tooltipList ='';
    $(document).ready(function(){
        $("#start_date").flatpickr({
            dateFormat: "Y-m-d",
            defaultDate: [start_date],
            onChange: function(selectedDates, dateStr, instance) {
                start_date = dateStr;
            },
        });
        $("#end_date").flatpickr({
            dateFormat: "Y-m-d",
            defaultDate: [end_date],
            onChange: function(selectedDates, dateStr, instance) {
                end_date = dateStr;
            },
        });
        get_datatable_list();
      
    });

    

    function setYearAndSearchData(elm){
        var yearSelect = $(elm).val();
        var monthSelect = $("#filter_month option:selected").val();

        var month = monthSelect.padStart(2, "0");
        start_date = moment([yearSelect, parseInt(month) - 1]).format('YYYY-MM-DD');
        end_date = moment(start_date).endOf('month').format('YYYY-MM-DD');
    }

    function setMonthAndSearchData(elm){
        var monthSelect = $(elm).val();
        var yearSelect = $("#filter_year option:selected").val();

        var month = monthSelect.padStart(2, "0");
        start_date = moment([yearSelect, parseInt(month) - 1]).format('YYYY-MM-DD');
        end_date = moment(start_date).endOf('month').format('YYYY-MM-DD');
    }

    function setDateAndSearchData(amountDay,elm){
        if(amountDay == 'TODAY'){
            start_date = moment().format('YYYY-MM-DD').toString();;
            end_date = moment().format('YYYY-MM-DD').toString();;
        }else if(amountDay == 'YESTERDAY'){
            start_date = moment().subtract(1,'day').format('YYYY-MM-DD').toString();
            end_date = moment().subtract(1,'day').format('YYYY-MM-DD').toString();
        }else if(amountDay == 'LAST7DAY'){
            start_date = moment().subtract(7,'day').format('YYYY-MM-DD').toString();
            end_date = moment().format('YYYY-MM-DD').toString();
        }else if(amountDay == 'THISWEEK'){
            start_date = moment().startOf('isoWeek').format('YYYY-MM-DD').toString();
            end_date = moment().endOf('isoWeek').format('YYYY-MM-DD').toString();
        }else if(amountDay == 'LASTWEEK'){
            start_date = moment().subtract(1,'weeks').startOf('isoWeek').format('YYYY-MM-DD').toString();
            end_date = moment().subtract(1,'weeks').endOf('isoWeek').format('YYYY-MM-DD').toString();
        }else if(amountDay == 'THISMONTH'){
            start_date = moment().startOf('month').format('YYYY-MM-DD').toString();
            end_date = moment().endOf('month').format('YYYY-MM-DD').toString();
        }else if(amountDay == 'LASTMONTH'){
            start_date = moment().subtract(1,'months').startOf('month').format('YYYY-MM-DD').toString();
            end_date = moment().subtract(1,'months').endOf('month').format('YYYY-MM-DD').toString();
        }else if(amountDay == 'ALL'){
            start_date = moment('2023-03-01').format('YYYY-MM-DD').toString();
            end_date = moment().format('YYYY-MM-DD').toString();;
        }else {
            start_date = moment().format('YYYY-MM-DD').toString();
            end_date = moment().format('YYYY-MM-DD').toString();
        }
        $("#start_date").val(start_date)
        $("#end_date").val(end_date)

        $(".btn-filter-date").removeClass('btn-secondary')
        $(".btn-filter-date").addClass('btn-dark')
        $(elm).addClass('btn-secondary')
        $(elm).removeClass('btn-dark')

        get_datatable_list()

    }

    async function get_datatable_list(){

        await get_summary()
        await get_summaryAll()
        // var text_search = $("#text_search").val().trim();
        var transaction_type = $("#transaction_type option:selected").val().trim();
        $.fn.dataTable.ext.errMode = 'none';
        table = $('#datatable-list').DataTable({
            destroy: true,
            processing: true,
            serverSide: true,
            sorting:false,
            ordering:false,
            searchDelay: 500,
            pageLength: 100,
            ajax : {
                url : url_ajax + 'getDatatableList',
                type : "POST",
                data : {
                    // text_search: text_search,
                    transaction_type : transaction_type,
                    start_date: start_date,
                    end_date: end_date
                },
            },
            columnDefs: [
                {
                    targets: [0,1],
                    className: 'text-left'
                } ,
                {
                    targets: [7,8,9],
                    className: 'text-left'
                } ,
                {
                    targets: [3,4,5,6],
                    className: 'text-right'
                } 
            ],
            rowCallback: function(row, data) {
                var transaction_type = data['transaction_type']; // Replace 'column_name' with the actual column name
                if (transaction_type === 'SETTLED') {
                    $(row).addClass('bg-warning'); // Add a custom CSS class to highlight the row
                }
            },
            columns : [
                {data: function(data, type, dataToSet) { return data.merchant_name }},

                {data: function(data, type, dataToSet) { return data.transaction_type }},
                {data: function(data, type, dataToSet) { return moment(data.txn_date).format("YYYY-MM-DD HH:mm:ss"); }},
                {data: function(data, type, dataToSet) { 
                    return data.txn_amount < 0 ? `<span class="text-danger">${formatNumber(data.txn_amount,2)}<span>` : formatNumber(data.txn_amount,2); 
                }},
                {data: function(data, type, dataToSet) { 
                    return data.net_amount < 0 ? `<span class="text-danger">${formatNumber(data.mdr_amount,2)}<span>` : formatNumber(data.mdr_amount,2); 
                }},
                {data: function(data, type, dataToSet) { 
                    if(data.transaction_type=="MOVE" || data.transaction_type=="SETTLED"){
                        return  `<span class="text-danger"> - ${formatNumber(data.net_amount,2)}<span>`
                    }else {
                        return data.net_amount < 0 ? `<span class="text-danger">${formatNumber(data.net_amount,2)}<span>` : formatNumber(data.net_amount,2); 
                    }
                }},
                {data: function(data, type, dataToSet) { 
                    return data.balance < 0 ? `<span class="text-danger">${formatNumber(data.balance,2)}<span>` : formatNumber(data.balance,2); 
                }},


                {data: function(data, type, dataToSet) {  
                    return data.txn_bank_name
                }},
                {data: function(data, type, dataToSet) {  
                    return data.txn_acc_4last
                }},

                {data: function(data, type, dataToSet) {  
                    return data.order_id
                }},
                 

            ],
            order: [[0, 'desc']],
        });

        tableDraw();
    }

    async function showModalSlip(instructionRefNo){
        $.blockUI({ css: { backgroundColor: '#fff', color: '#000' , borderColor : '#fff'  } , message : 'กำลังโหลดข้อมูล' });
            var formdata = new FormData();
            formdata.append('instructionRefNo',instructionRefNo)
            var requestOptions = {
                method: 'POST',
                body: formdata,
                redirect: 'follow'
            };
            var res = await fetch( url_ajax + 'getWithdrawSlip', requestOptions)
            .then(response => response.json()) 

            if(res.error==0){
                $("#modalSlip").modal('show')

                if(res.result.from =="EMAIL"){
                    $("#htmlEmail").html(res.result.data.message)
                }else{
                    $("#htmlEmail").text(res.result.data.message)
                }
                $.unblockUI();
            }else{
                $.unblockUI();
                alert('ไม่พบข้อมูล')
            }
            

           
        return res;
    }
    

    function get_summary(){
        var transaction_type = $("#transaction_type option:selected").val().trim();
        $.ajax({
            url: url_ajax + 'getSummary',
            method: 'POST',
            data: {
                transaction_type : transaction_type,
                start_date: start_date,
                end_date: end_date
            },
            async: false,
            dataType: "json",
            success: function (response) {
                console.log(response)
                if(response.error == 0){
                    // $("#last_updatedata").text(moment(response.result.last_updatedata).format('DD/MM/YYYY HH:mm:ss'))
                    let deposit_txn_amount = parseFloat((response.result_deposit.deposit_txn_amount!=undefined)?response.result_deposit.deposit_txn_amount:0);
                    let deposit_mdr_amount = parseFloat((response.result_deposit.deposit_mdr_amount!=undefined)?response.result_deposit.deposit_mdr_amount:0);
                    let deposit_net_amount = parseFloat((response.result_deposit.deposit_net_amount!=undefined)?response.result_deposit.deposit_net_amount:0);
                    let moveout_net_amount = parseFloat((response.result_deposit.moveout_net_amount!=undefined)?response.result_deposit.moveout_net_amount:0);
                    let txn_amount = parseFloat((response.result_settled.txn_amount!=undefined)?response.result_settled.txn_amount:0);
                    let mdr_amount = parseFloat((response.result_settled.mdr_amount!=undefined)?response.result_settled.mdr_amount:0);
                    let net_amount_adjust = parseFloat((response.result_settled.net_amount_adjust!=undefined)?response.result_settled.net_amount_adjust:0);

                    $("#total_deposit_amount").html( formatNumber( deposit_txn_amount ) )
                    $("#total_deposit_mdr").html( formatNumber( deposit_mdr_amount ) )
                    $("#total_deposit_transfer").html( formatNumber( moveout_net_amount ) )
                    $("#total_deposit_net").html( formatNumber( deposit_net_amount ) )

                    $("#total_settled_amount").text(formatNumber(txn_amount,2));
                    $("#total_settled_mdr").text(formatNumber(mdr_amount,2));
                    $("#total_settled_net").text(formatNumber(net_amount_adjust,2));
                    
                    var balance = parseFloat(response.result.sum_deposit)-parseFloat(response.result.sum_move)-parseFloat(response.result.sum_settled);
                    if(balance < 0){
                        $("#total_balance").html(`<span class="text-danger">${formatNumber(balance,2)}</span>`);
                    }else{
                        $("#total_balance").text(formatNumber(balance,2));
                    }
                }else{
                    $("#total_settled").text(`0`);
                    $("#total_move").text(`0`);
                    $("#total_deposit").text(`0`);
                    $("#total_balance").text(`0`);
                }
               
               
 

 
            }
        })
    }

    function get_summaryAll(){
        var transaction_type = 'ALL'
        $.ajax({
            url: url_ajax + 'getSummary',
            method: 'POST',
            data: {
                transaction_type : transaction_type,
                start_date: '2023-03-01',
                end_date: moment().format("YYY-MM-DD")
            },
            async: false,
            dataType: "json",
            success: function (response) {
                console.log(response)
                if(response.error == 0){
                    // $("#last_updatedata2").text(moment(response.result.last_updatedata).format('DD/MM/YYYY HH:mm:ss'))
                    

                    $("#result_total_settled").text(`- ${formatNumber(response.result.sum_settled,2)}`);
                    $("#result_total_move").text(`- ${formatNumber(response.result.sum_move,2)}`);
                    $("#result_total_deposit").text(`${formatNumber(response.result.sum_deposit,2)}`);

                    // $("#total_balance").text(formatNumber(response.result.agent_deposit_balance,2));
                    if(response.result.agent_deposit_balance < 0){
                        $("#result_total_balance").html(`<span style="color:red">${formatNumber(response.result.agent_deposit_balance,2)}</span>`);
                    }else{
                        $("#result_total_balance").text(formatNumber(response.result.agent_deposit_balance,2));
                    }
                }else{
                    $("#result_total_settled").text(`0`);
                    $("#result_total_move").text(`0`);
                    $("#result_total_deposit").text(`0`);
                    $("#result_total_balance").text(`0`);
                }
               
 

 
            }
        })
    }

  
    function tableDraw(){
        if(tableDrawStatus){
            table.on( 'draw', function () {
                tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]')
                tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl))
            } );    
            tableDrawStatus = false;
        }
        
    }
 

    function exportData(){
        Swal.fire({
            title: 'Processing data to export',
            allowOutsideClick: false,
            showConfirmButton: false,
            onBeforeOpen: () => {
                Swal.showLoading()
            },
        });
        Swal.showLoading();

        setTimeout(function(){
            var transaction_type = $("#transaction_type option:selected").val().trim();

            let check = true;
            let page = 1;
            let length = 3000;
            let result_csv = [];
            while(check){
                $.ajax({
                    url: url_ajax + 'getDatatableListExcel',
                    method: 'POST',
                    data: {
                        transaction_type : transaction_type,
                        start_date: start_date,
                        end_date: end_date,
                        start: (page-1)*length,
                        length: length,
                        draw: page,
                    },
                    async: false,
                    dataType: "json",
                    success: function (response) {
                        if(response.error == 0){
                            result_csv = result_csv.concat(response.result);
                        }else{
                            // console.log("step 3");
                            check = false;
                        }
                    }
                })
                if(page >50){
                    check = false;
                }
                page++;
            }
            if(result_csv.length > 0){
                exportExcel(result_csv, `csv_deposit_activity_history_report_${moment().format("YYYY-MM-DD_HH-mm-ss").toString()}.xlsx`);
            }else{
                alert('ไม่พบข้อมูล');
            }
            Swal.close()
        },300)

        return false;
    }

    function exportExcel(data,filename){
        // Define the data to be exported
        // const data = [
        //     { name: 'Alice', age: 25, email: '<EMAIL>' },
        //     { name: 'Bob', age: 30, email: '<EMAIL>' },
        //     { name: 'Charlie', age: 35, email: '<EMAIL>' }
        // ];

        // Define the filename for the exported file
        // const filename = 'example.xlsx';

        // Convert the data to a worksheet
        const workbook = XLSX.utils.book_new();
        const worksheet = XLSX.utils.json_to_sheet(data);

        // define style with table border
        worksheet['!table'] = { // Add the border style
            style: 'border: 1px solid black;',
        };

        // Create a new workbook and add the worksheet to it
        XLSX.utils.book_append_sheet(workbook, worksheet);

        // Convert the workbook to a binary string
        const binaryString = XLSX.write(workbook, { bookType: 'xlsx', type: 'binary' });

        // Convert the binary string to a Blob object
        const blob = new Blob([s2ab(binaryString)], { type: 'application/octet-stream' });

        // Save the Blob object as a file using FileSaver.js
        window.saveAs(blob, filename);
    }

    // Helper function to convert a string to an ArrayBuffer
    function s2ab(s) {
        const buf = new ArrayBuffer(s.length);
        const view = new Uint8Array(buf);
        for (let i = 0; i < s.length; i++) {
            view[i] = s.charCodeAt(i) & 0xFF;
        }
        return buf;
    }

</script>
</body>

</html>

