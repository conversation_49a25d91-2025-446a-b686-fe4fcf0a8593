<!doctype html>
<html lang="en" data-layout="vertical" data-topbar="light" data-sidebar="dark" data-sidebar-size="lg" data-sidebar-image="none" data-preloader="disable">

<head>

    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="sugarpay66 payment">
    <meta name="keywords" content="sugarpay66 payment">
    <meta name="author" content="member.sugarpay66.io">
    <meta property="og:title" content="Merchant sugarpay66 payment" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="/" />
    <meta property="og:image" content="/public/assets//images/logo/android-chrome-512x512.png" />
    <title>ร้าน tiger-001 - Merchant sugarpay66 payment</title>


    <style>
        :root {
            --theme-bg_color_login: linear-gradient(to right, #bfa2a8, #6e5c60);
            --theme-color_primary:#6e5c60;
        }
    </style>
    
    <!-- For IE6+ -->
    <link rel="shortcut icon" href="public/assets/images/logo/favicon.ico" type="image/x-icon">

    <!-- For all other browsers -->
    <link rel="icon" href="public/assets/images/logo/favicon.ico"/>

    <!-- Different sizes -->
    <link rel="icon" href="public/assets/images/logo/favicon-16x16.png" sizes="16x16">
    <link rel="icon" href="public/assets/images/logo/favicon-32x32.png" sizes="32x32">

    <!-- For Modern Browsers with PNG Support -->
    <link rel="icon" type="image/png" href="public/assets/images/logo/apple-touch-icon.png">

    <!-- Works in Firefox, Opera, Chrome and Safari -->
    <link rel="icon" href="public/assets/images/logo/apple-touch-icon.png">

    <!-- For rounded corners and reflective shine in Apple devices -->
    <link rel="apple-touch-icon" href="public/assets/images/logo/apple-touch-icon.png" />

    <!-- Favicon without reflective shine -->
    <link rel="apple-touch-icon-precomposed" href="public/assets/images/logo/apple-touch-icon.png">

    <!-- jsvectormap css -->
    <link href="public/assets/libs/jsvectormap/css/jsvectormap.min.css" rel="stylesheet" type="text/css" />

    <!--Swiper slider css-->
    <link href="public/assets/libs/swiper/swiper-bundle.min.css" rel="stylesheet" type="text/css" />


    <!-- Sweet Alert css-->
    <link href="public/assets/libs/sweetalert2/sweetalert2.min.css" rel="stylesheet" type="text/css" />

    <link href="public/assets/libs/flatpickr/flatpickr.min.css" rel="stylesheet" type="text/css" />

    <!-- Layout config Js -->
    <script src="public/assets/js/layout.js"></script>
    <!-- Bootstrap Css -->
    <link href="public/assets/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <!-- Icons Css -->
    <link href="public/assets/css/icons.min.css" rel="stylesheet" type="text/css" />
    <!-- App Css-->
    <link href="public/assets/css/app.css?t=1746271782" rel="stylesheet" type="text/css" />

    <!-- custom Css-->
    <link href="public/assets/css/custom.css?t=1746271782" rel="stylesheet" type="text/css" />


    <!--datatable css-->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" />
    <!--datatable responsive css-->
    <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.dataTables.min.css">
    <style type="text/css">
        .text-bold{
            font-weight: bold;
        }
        .text-right{
            text-align: right;
        }
        .modal-xl {
            max-width: 1300px !important;
        }
        #modalAnnouncement img{
            width :100%;
        }
        #modalAnnouncement  p {
            margin-top: 0;
            margin-bottom: 0.2rem;
        }
    </style>
    <style></style></head>

<body>

<!-- Begin page -->
<div id="layout-wrapper">

    <header id="page-topbar">
        <div class="layout-width">
            <div class="navbar-header">
                <div class="d-flex">
                    <!-- LOGO -->
                    <div class="navbar-brand-box horizontal-logo">
                        <a href="" class="logo logo-dark">
                                <span class="logo-sm">
                                    <img src="public/assets/images/logo/logo-sm.png" alt="" height="22">
                                </span>
                            <span class="logo-lg">
                                    <img src="public/assets/images/logo/logo.png" alt="" height="48">
                                </span>
                        </a>

                        <a href="index.html" class="logo logo-light">
                                <span class="logo-sm">
                                    <img src="public/assets/images/logo/logo-sm.png" alt="" height="22">
                                </span>
                            <span class="logo-lg">
                                    <img src="public/assets/images/logo/logo.png" alt="" height="48">
                                </span>
                        </a>
                    </div>

                    <button type="button" class="btn btn-sm px-3 fs-16 header-item vertical-menu-btn topnav-hamburger"
                            id="topnav-hamburger-icon">
                            <span class="hamburger-icon">
                                <span></span>
                                <span></span>
                                <span></span>
                            </span>
                    </button>
                </div>

                <div class="d-flex align-items-center">
                                        <div class="ms-1 header-item d-none d-sm-flex">
                        <button type="button" class="btn btn-icon btn-topbar btn-ghost-secondary rounded-circle"
                                data-toggle="fullscreen">
                            <i class='bx bx-fullscreen fs-22'></i>
                        </button>
                    </div>
                    <div class="dropdown ms-sm-3 header-item topbar-user">
                        <button type="button" class="btn" id="page-header-user-dropdown" data-bs-toggle="dropdown"
                                aria-haspopup="true" aria-expanded="false">
                                <span class="d-flex align-items-center">
                                    <img class="rounded-circle header-profile-user" src="public/assets/images/logo/logo-sm.png"
                                         alt="Header Avatar">
                                    <span class="text-start ms-xl-2">
                                        <span class="d-none d-xl-inline-block ms-1 fw-medium user-name-text">tiger-001</span>
                                        <span class="d-none d-xl-block ms-1 fs-12 text-muted user-name-sub-text"></span>
                                    </span>
                                </span>
                        </button>
                        <div class="dropdown-menu dropdown-menu-end">
                            <!-- item-->
                            <h6 class="dropdown-header">ยินดีต้อนรับ tiger-001</h6>
                                                        <a class="dropdown-item" href="login/logout"><i
                                        class="mdi mdi-logout text-muted fs-16 align-middle me-1"></i> <span
                                        class="align-middle" data-key="t-logout">ออกจากระบบ</span></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- ========== App Menu ========== -->
    <div class="app-menu navbar-menu">
        <!-- LOGO -->
        <div class="navbar-brand-box">
            <!-- Dark Logo-->
            <a href="" class="logo logo-dark">
                    <span class="logo-sm">
                        <img src="public/assets/images/logo/logo-sm.png" alt="" height="22">
                    </span>
                <span class="logo-lg">
                        <img src="public/assets/images/logo/logo.png" alt="" height="48">
                    </span>
            </a>
            <!-- Light Logo-->
            <a href="" class="logo logo-light">
                    <span class="logo-sm">
                        <img src="public/assets/images/logo/logo-sm.png" alt="" height="22">
                    </span>
                <span class="logo-lg">
                        <img src="public/assets/images/logo/logo.png" alt="" height="48">
                    </span>
            </a>
            <button type="button" class="btn btn-sm p-0 fs-20 header-item float-end btn-vertical-sm-hover" id="vertical-hover">
                <i class="ri-record-circle-line"></i>
            </button>
        </div>

        <div id="scrollbar">
            <div class="container-fluid">

                <div id="two-column-menu">
                </div>
                <ul class="navbar-nav mt-3" id="navbar-nav">
                                            <li class="nav-item">
                            <a class="nav-link menu-link " href="dashboard.html" >
                                <i class="ri-article-line"></i> <span data-key="t-users">Dashboard</span>
                            </a>
                        </li>
                                        
                                        <li class="nav-item">
                        <a class="nav-link menu-link " href="transaction.html" >
                            <i class="ri-article-line"></i> <span data-key="t-users">Transaction</span>
                        </a>
                    </li>
                                                                <li class="nav-item">
                            <a class="nav-link menu-link " href="withdraw_approve.html" >
                                <i class="ri-article-line"></i> <span data-key="t-users">Withdraw Approve</span>
                            </a>
                        </li>
                                                            <li class="nav-item">
                        <a class="nav-link menu-link " href="bank_statement.html" >
                            <i class="ri-bank-line"></i> <span data-key="t-users">Statement</span>
                        </a>
                    </li>
                                                            <li class="nav-item">
                        <a class="nav-link menu-link " href="summary_transfer.html" >
                            <i class="ri-file-transfer-line"></i> <span data-key="t-users">Settlement</span>
                        </a>
                    </li>
                    
                                        <a class="nav-link menu-link " href="#sidebarReport" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarReport">
                        <i class="ri-article-line"></i> <span data-key="t-report">Report</span>
                    </a>
                    <div class="collapse menu-dropdown" id="sidebarReport">
                        <ul class="nav nav-sm flex-column">

                                                        <li class="nav-item">
                                <a href="report_agent_daily_realtime.html" class="nav-link" data-key="t-report">Daily Summary Report</a>
                            </li>
                             

                                                            <li class="nav-item">
                                    <a href="report_withdraw_channel.html" class="nav-link " data-key="t-users">Withdraw Channel Report</a>
                                </li>
                            
                                                        <li class="nav-item">
                                <a href="report_withdraw.html" class="nav-link" data-key="t-report">Withdraw Activity Report</a>
                            </li>
                             

                                                        <li class="nav-item">
                                <a href="report_deposit.html" class="nav-link" data-key="t-report">Deposit Activity Report</a>
                            </li>
                             
                            
                            <!--                            <li class="nav-item">-->
<!--                                <a href="--><!--" class="nav-link" data-key="t-report"> Withdraw Slip</a>-->
<!--                            </li>-->
                                                    </ul>
                    </div>
                    
                  

                                        <li class="nav-item">
                        <a class="nav-link menu-link " href="withdraw_fund.html" >
                            <i class="ri-file-transfer-line"></i> <span data-key="t-users">Withdraw Fund</span>
                        </a>
                    </li>
                                                            <li class="nav-item">
                        <a class="nav-link menu-link " href="scanslip-step-import.html" >
                            <i class=" ri-qr-code-fill"></i> <span data-key="t-users">STEP 1 Import Slip</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link menu-link " href="scanslip-step-verify.html" >
                            <i class=" ri-qr-code-fill"></i> <span data-key="t-users">STEP 2 Verify Slip</span>
                        </a>
                    </li>
                    

                                        <a class="nav-link menu-link " href="#sidebarTools" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarTools">
                        <i class="ri-article-line"></i> <span data-key="t-report">Tools</span>
                    </a>
                    <div class="collapse menu-dropdown" id="sidebarTools">
                        <ul class="nav nav-sm flex-column">
                             
                                                        <li class="nav-item">
                                <a href="black_list.html" class="nav-link" data-key="t-report">Blacklist Manage</a>
                            </li>
                             
 
                        </ul>
                    </div>
                    

                    <li class="nav-item">
                        <a class="nav-link menu-link " href="profile.html" >
                            <i class="ri-folder-user-line"></i> <span data-key="t-users">Profile</span>
                        </a>
                    </li>
                                            <a class="nav-link menu-link " href="#sidebarSetupUser" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarSetupUser">
                            <i class="ri-folder-user-line"></i> <span data-key="t-setup-user">Settings</span>
                        </a>
                                                <div class="collapse menu-dropdown " id="sidebarSetupUser">
                            <ul class="nav nav-sm flex-column">
                                <li class="nav-item">
                                    <a href="user_group.html" class="nav-link " data-key="t-user-group"> User Groups </a>
                                </li>
                                <li class="nav-item">
                                    <a href="user_permission.html" class="nav-link " data-key="t-permission"> Group Permission </a>
                                </li>
                                <li class="nav-item">
                                    <a href="user.html" class="nav-link " data-key="t-users"> Sub Users </a>
                                </li>
                            </ul>
                        </div>
                                        <li class="nav-item">
                        <a class="nav-link menu-link " href="login/logout" >
                            <i class="ri-logout-box-r-line"></i> <span data-key="t-users">Logout</span>
                        </a>
                    </li>
<!--                    <li class="nav-item">-->
<!--                        <a class="nav-link menu-link " href="https://vizpay.supportnow.me/" target="_blank">-->
<!--                            <img src="--><!--images/ticket_now.jpg" alt="" height="60">-->
<!--                        </a>-->
<!--                    </li>-->
                </ul>
            </div>
            <!-- Sidebar -->
        </div>

        <div class="sidebar-background"></div>
    </div>
    <!-- Left Sidebar End -->
    <!-- Vertical Overlay-->
    <div class="vertical-overlay"></div>

    <!-- ============================================================== -->
    <!-- Start right Content here -->
    <!-- ============================================================== -->
    <div class="main-content">

<div class="page-content">
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                    <h4 class="mb-sm-0">Withdraw Fund</h4>

                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item active">Withdraw Fund</li>
                        </ol>
                    </div>

                </div>
            </div>
        </div>
        <!-- end page title -->


        <div class="row h-100">
                        <div class="col-lg-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="avatar-sm flex-shrink-0">
                                <span class="avatar-title bg-light text-primary rounded-circle fs-3">
                                    <i class="ri-money-dollar-circle-fill align-middle"></i>
                                </span>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <p class="text-uppercase fw-semibold fs-12 text-muted mb-1"> Deposit Balance</p>
                                <h4 class=" mb-0"><span class="counter-value text-primary" id="total_balance_amount" data-target=""></span></h4>
                            </div>
                            <div class="flex-shrink-0 align-self-end">
                                <!--                                <span class="badge badge-soft-success"><i class="ri-arrow-up-s-fill align-middle me-1"></i>6.24 %<span> </span></span>-->
                            </div>
                        </div>
                    </div><!-- end card body -->
                </div><!-- end card -->
            </div><!-- end col -->
                                    <div class="col-lg-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="avatar-sm flex-shrink-0">
                                <span class="avatar-title bg-light text-primary rounded-circle fs-3">
                                    <i class="ri-money-dollar-circle-fill align-middle"></i>
                                </span>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <p class="text-uppercase fw-semibold fs-12 text-muted mb-1"> Withdraw Balance</p>
                                <h4 class=" mb-0"><span class="counter-value text-danger" id="total_withdraw_balance" data-target=""></span></h4>
                            </div>
                            <div class="flex-shrink-0 align-self-end">
                                <!--                                <span class="badge badge-soft-success"><i class="ri-arrow-up-s-fill align-middle me-1"></i>6.24 %<span> </span></span>-->
                            </div>
                        </div>
                    </div><!-- end card body -->
                </div><!-- end card -->
            </div><!-- end col -->
                    </div>


        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header align-items-center d-flex">
                        <h4 class="card-title mb-0 flex-grow-1">Filter</h4>
                        <div class="flex-shrink-0">
                                                            <a href="javascript:void(0)" onclick="makePayment();" class="btn btn-primary w-sm" style="display: initial;"><i class="mdi mdi-plus"></i> Topup Fund</a>
                                                                                        <a href="javascript:void(0)" onclick="makeMovement();" class="btn btn-warning w-sm" style="display: initial;"><i class="mdi mdi-transfer"></i> Transfer Fund</a>
                                                    </div>
                    </div><!-- end card header -->
                    <div class="card-body">
                        <div class="live-preview mb-2">
                            <form action="#" id="form_search" method="post">
                                <div class="row g-3">
                                    <div class="col-lg-3">
                                        <label for="field1" class="form-label">Start Date</label>
                                        <input type="text" class="form-control" id="start_date" value="2025-04-26 00:00" >
                                    </div>
                                    <div class="col-lg-3">
                                        <label for="field1" class="form-label">End Date</label>
                                        <input type="text" class="form-control" id="end_date" value="2025-05-03 23:59">
                                    </div>
                                    
                                    <div class="col-lg-3 d-none">
                                        <label for="field1" class="form-label">Deposit Type</label>
                                        <select id="deposit_type_search" class="form-select">
                                            <option value="ALL" selected>All</option>
                                            <option value="TRANSFER">TRANSFER</option><option value="QRCODE" >QRCODE</option>
                                        </select>
                                    </div>
                                    <div class="col-lg-3">
                                        <label for="field1" class="form-label">Status</label>
                                        <select id="status_search" class="form-select">
                                            <option value="ALL">All</option>
                                            <option value="CREATE">CREATE</option>
                                            <option value="SUCCESS">SUCCESS</option>
                                            <option value="FAILED">FAILED</option>
                                        </select>
                                    </div>
                                    <div class="col-lg-3">
                                        <label for="field1" class="form-label">Text in table</label>
                                        <input type="text" class="form-control" id="text_search" value="">
                                    </div>


                                    <div class="col-lg-6">
                                        <label for="txn_amount_search" class="form-label">จำนวนเงิน</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control number-only" id="from_txn_amount_search">
                                            <span class="input-group-text"> - </span>
                                            <input type="text" class="form-control number-only" id="to_txn_amount_search">
                                        </div>
                                    </div>

                                    <div class="col-lg-3">
                                        <label for="field1" class="form-label">Type</label>
                                        <select id="transaction_type_search" class="form-select">
                                            <option value="ALL">All</option>
                                            <option value="TOPUP">TOPUP</option>
                                            <option value="MOVE">TRANSFER</option>
                                        </select>
                                    </div>

                                </div>
                                <div>
                                    <div class="row g-3 mt-3">
                                        <div class="col-12 text-center">
                                            <button type="button" class="btn btn-success  waves-effect waves-light" onclick="get_datatable_list();"><i class="mdi mdi-filter-plus-outline"></i> Search</button>
                                            <button type="reset" class="btn btn-soft-dark waves-effect waves-light" onclick="setTimeout(get_datatable_list,300)"><i class="mdi mdi-filter-remove-outline"></i> Clear</button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>



        <div class="row h-100">
            <div class="col-lg-3 col-md-4">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1 ms-3">
                                <p class="text-uppercase fw-semibold fs-12 text-muted mb-1"> Total All (net)</p>
                                <h4 class=" mb-0"><span class="counter-value text-primary" id="total_all" data-target=""></span></h4>
                            </div>
                            <div class="flex-shrink-0 align-self-end">
                                <!--                                <span class="badge badge-soft-success"><i class="ri-arrow-up-s-fill align-middle me-1"></i>6.24 %<span> </span></span>-->
                            </div>
                        </div>
                    </div><!-- end card body -->
                </div><!-- end card -->
            </div><!-- end col -->
            <div class="col-lg-3 col-md-4">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1 ms-3">
                                <p class="text-uppercase fw-semibold fs-12 text-muted mb-1"> Total Topup (net)</p>
                                <h4 class=" mb-0"><span class="counter-value text-danger" id="total_topup" data-target=""></span></h4>
                            </div>
                            <div class="flex-shrink-0 align-self-end">
                                <!--                                <span class="badge badge-soft-success"><i class="ri-arrow-up-s-fill align-middle me-1"></i>6.24 %<span> </span></span>-->
                            </div>
                        </div>
                    </div><!-- end card body -->
                </div><!-- end card -->
            </div><!-- end col -->
            <div class="col-lg-3 col-md-4">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1 ms-3">
                                <p class="text-uppercase fw-semibold fs-12 text-muted mb-1"> Total Transfer (net)</p>
                                <h4 class=" mb-0"><span class="counter-value text-danger" id="total_transfer" data-target=""></span></h4>
                            </div>
                            <div class="flex-shrink-0 align-self-end">
                                <!--                                <span class="badge badge-soft-success"><i class="ri-arrow-up-s-fill align-middle me-1"></i>6.24 %<span> </span></span>-->
                            </div>
                        </div>
                    </div><!-- end card body -->
                </div><!-- end card -->
            </div><!-- end col -->
        </div>

        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header align-items-center d-flex">
                        <h4 class="card-title mb-0 flex-grow-1">
                        <span class="text-danger">กรณี Topup โอนเงินแล้วเงินไม่เข้าบัญชีถอน เกิดจากการระบุเลขบัญชีฝากไม่ถูกต้อง ให้ทางร้านค้าสร้างรายการ Topup ใหม่ โดยใส่ธนาคารและเลขบัญชีให้ถูกต้อง เพื่อให้ระบบสามารถจับคู่ได้ถูกต้อง ส่วนรายการเก่าที่เลขบัญชีผิดเดี๋ยวรายการจะหมดเวลาไปเอง </span>
                        </h4>
                    </div><!-- end card header -->
                    <div class="card-body">
                        <!-- Datatable -->
                        <div class="table-responsive">
                            <table id="datatable-list" class="table nowrap dt-responsive align-middle table-hover table-bordered" style="width:100%">
                                <thead>
                                <tr>
                                    <th scope="col">Create Date</th>
                                    <th scope="col">Type</th>
                                    <th scope="col">Order Id</th>
                                    <th scope="col">Amount</th>
                                    <th scope="col">MDR</th>
                                    <th scope="col">Net</th>
                                    <th scope="col">Status</th>
                                    <th scope="col">Account No</th>
                                    <th scope="col">Transaction Date</th>
                                    <th scope="col">Before</th>
                                    <th scope="col">After</th>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!--end col-->
        </div>
        <!--end row-->

    </div>
    <!-- container-fluid -->
</div>
<!-- End Page-content -->


<div class="modal fade" id="modalMakePayment" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="modalMakePayment" aria-modal="true">
    <div class="modal-dialog ">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalgridLabel">Topup Fund <span class="text-danger">(ใช้ได้เฉพาะช่วงเวลา 02:00 - 22:50)</span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="javascript:void(0);" id="formMakePayment" enctype="multipart/form-data">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div>
                                <label for="makePaymentType" class="form-label">Type <span class="text-danger">*</span></label>
                                <select name="makePaymentType" id="makePaymentType" onChange="changePaymentType()" class="form-select" required>
                                    <option value="TRANSFER" selected>TRANSFER</option><option value="CDM" >ฝากเงินสด (CDM)</option>                                </select>
                            </div>
                        </div><!--end col-->
                                                <div class="col-md-12 mb-2">
                            <h4 class="mb-1 " style="color:#0008ff;">
                                ข้อควรทราบ ร้านค้าต้องสร้างรายการก่อนโอนเงินทุกครั้ง และดูเลขบัญชีของบริษัทที่จะโอนเข้า หลังจากกดปุ่ม Submit เท่านั้น (ห้ามบันทึกเลขบัญชีบริษัทไว้ เนื่องจากอาจจะเปลี่ยนแปลงตลอดเวลา)
                            </h4>
<!--                            <span style="color:red">*โอนมาที่ --><!-- ชื่อบัญชี --><!-- เลขที่บัญชี <b>--><!--</b></span>-->
                        </div>

                        <div class="col-md-12 mb-2">
                            <span style="color:red" id="remarkCDM"></b></span>
                        </div>

                        <div class="col-md-6">
                            <div>
                                <label for="makePaymentBankAccountName" class="form-label">Bank Account Name </label>
                                <input type="text" class="form-control" name="makePaymentBankAccountName" id="makePaymentBankAccountName" placeholder="สมชาย ใจดี" >
                            </div>
                        </div><!--end col-->
                        <div class="col-md-6">
                            <div>
                                <label for="makePaymentBankAccountNo" class="form-label">Bank Account No <span class="text-danger">*</span> </label>
                                <input type="text" class="form-control" name="makePaymentBankAccountNo" id="makePaymentBankAccountNo" required maxlength="13" placeholder="xxxxxxxxx" >
                            </div>
                        </div><!--end col-->
                        <div class="col-md-6">
                            <div>
                                <label for="makePaymentBankName" class="form-label">Bank Name <span class="text-danger">* </span></label>
                                <select name="makePaymentBankName" id="makePaymentBankName" class="form-select" required >
                                    <option value="">Please select bank</option>
                                    <option value="034">BAAC-ธนาคารเพื่อการเกษตรและสหกรณ์การเกษตร</option><option value="025">BAY-ธนาคารกรุงศรีอยุธยา</option><option value="002">BBL-ธนาคารกรุงเทพ</option><option value="901">CDM-โอนเงินจากตู้ ATM</option><option value="022">CIMB-ธนาคารซีไอเอ็มบีไทย</option><option value="017">CITI-ซิตี้แบงก์</option><option value="033">GHB-ธนาคารอาคารสงเคราะห์ (ธอส.)</option><option value="030">GSB-ธนาคารออมสิน</option><option value="066">ISBT-ธนาคารอิสลาม</option><option value="004">KBANK-ธนาคารกสิกรไทย</option><option value="069">KKP- ธนาคาร เกียรตินาคิน </option><option value="006">KTB-ธนาคารกรุงไทย</option><option value="073">LHBANK-ธนาคารแลนด์ แอนด์ เฮาส์</option><option value="014">SCB-ไทยพาณิชย์</option><option value="018">SMBC-ธนาคาร ซูมิโตโม มิตซุย แบงกิ้ง</option><option value="071">TCRB-ธนาคารไทยเครดิต เพื่อรายย่อย</option><option value="067">TISCO-ธนาคารทิสโก้</option><option value="801">TRUE-True Wallet</option><option value="011">TTB-ธนาคารทหารไทยธนชาติ</option><option value="024">UOB-ธนาคารยูโอบี</option>                                </select>
                            </div>
                        </div><!--end col-->
                        <div class="col-md-6">
                            <div>
                                <label for="makePaymentAmount" class="form-label">Amount <span class="text-danger">* (10,000 - 1,999,999)</span></label>
                                <input type="text" class="form-control" name="makePaymentAmount" id="makePaymentAmount"  required placeholder="ขั้นต่ำ 10,000 ถึง สูงสุด 1,999,999" >
                            </div>
                        </div><!--end col-->
                        <div class="col-md-6">
                            <div>
                                <label for="makePaymentMDRAmount" class="form-label">MDR (<span id="makePaymentMDRRate">0</span>%)</label>
                                <input type="text" class="form-control" name="makePaymentMDRAmount" id="makePaymentMDRAmount" readonly placeholder="0" >
                            </div>
                        </div><!--end col-->
                        <div class="col-md-6">
                            <div>
                                <label for="makePaymentNetAmount" class="form-label">Net Received</label>
                                <input type="text" class="form-control" name="makePaymentNetAmount" id="makePaymentNetAmount" readonly placeholder="0" >
                            </div>
                        </div><!--end col-->
                    </div>
                                        <div class="row g-3 mt-2" id="makePaymentSubmit">
                        <div class="col-md-12">
                            <div class="hstack gap-2 justify-content-center">
                                <button type="submit" class="btn btn-success" onclick="return confirmMakePayment();"><i class="mdi mdi-plus-thick"></i> Submit</button>
                                <button type="button" class="btn btn-light" data-bs-dismiss="modal"><i class="mdi mdi-close-thick"></i> Cancel</button>
                            </div>
                        </div><!--end col-->
                    </div><!--end row-->

                                        <div class="row g-3 mt-2" id="makePaymentResult" style="display: none;">
                        <table class="table table-bordered table-striped">
                            <tbody>
                                <tr>
                                    <td>Ref ID</td>
                                    <td class="text-right" id="makePaymentResultRefId"></td>
                                </tr>
                                <tr>
                                    <td>Order ID</td>
                                    <td class="text-right" id="makePaymentResultOrderId"></td>
                                </tr>
                                <tr>
                                    <td>Price</td>
                                    <td class="text-right text-danger text-bold" style="font-size: 1rem;" id="makePaymentResultPrice"></td>
                                </tr>
<!--                                <tr>-->
<!--                                    <td>Redirect URL</td>-->
<!--                                    <td class="text-right" id="makePaymentResultRedirectURL"></td>-->
<!--                                </tr>-->
                                <tr>
                                    <td>Image</td>
                                    <td class="text-right" id="makePaymentResultImage"></td>
                                </tr>
                                <tr>
                                    <td>Transfer To Bank</td>
                                    <td class="text-right" id="makePaymentResultTransferBank"></td>
                                </tr>
                                <tr>
                                    <td>Timeout</td>
                                    <td class="text-right" id="makePaymentResultTimeout"></td>
                                </tr>
                            </tbody>
                        </table>

                        <button type="button" class="btn btn-danger" data-bs-dismiss="modal"><i class="mdi mdi-close-thick"></i> Close</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>



<div class="modal fade" id="modalMovementFund" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="modalMovementFund" aria-modal="true">
    <div class="modal-dialog ">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalgridLabel">Transfer Fund <span class="text-danger">(ใช้ได้เฉพาะช่วงเวลา 02:30 - 22:00)</span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="javascript:void(0);" id="formMovementFund" enctype="multipart/form-data">
                    <div class="row g-3">
<!--                        <div class="col-md-6">-->
<!--                            <div>-->
<!--                                <label for="movementFundDepositBalance" class="form-label">Deposit Balance </label>-->
<!--                                <input type="text" class="form-control" name="movementFundDepositBalance" id="movementFundDepositBalance" placeholder="0" readonly >-->
<!--                            </div>-->
<!--                        </div>-->
                        <div class="col-md-2">
                        </div><!--end col-->
                        <div class="col-md-8">
                            <div>
                                <label for="movementFundMaxMoveBalance" class="form-label">Available Transfer Balance </label>
                                <input type="text" class="form-control" name="movementFundMaxMoveBalance" id="movementFundMaxMoveBalance" placeholder="0" readonly>
                            </div>
                        </div><!--end col-->
                        <div class="col-md-2">
                        </div><!--end col-->
                        <div class="col-md-2">
                        </div><!--end col-->
                        <div class="col-md-8">
                            <div>
                                <label for="movementFundAmount" class="form-label">Transfer Amount
                                    <span class="text-danger">(ขั้นต่ำ 1,000)</span>
                                    <a href="javascript:void(0);" class="badge text-bg-danger" onclick="movementMax();">MAX</a>
                                </label>
                                <input type="text" class="form-control" name="movementFundAmount" id="movementFundAmount" required placeholder="0" style="background: #e6ffe6;font-size: 1.2rem;color:#000;">
                            </div>
                        </div><!--end col-->
                        <div class="col-md-12 text-center text-danger">* ระบบอาจจะใช้เวลา 3-5 นาทีในการโยกเงินฝาก</div>
                    </div>
                                        <div class="row g-3 mt-2" id="movementFundSubmit">
                        <div class="col-md-12">
                            <div class="hstack gap-2 justify-content-center">
                                <button type="submit" class="btn btn-success" onclick="return confirmMakeMovement();"><i class="mdi mdi-plus-thick"></i> Submit</button>
                                <button type="button" class="btn btn-light" data-bs-dismiss="modal"><i class="mdi mdi-close-thick"></i> Cancel</button>
                            </div>
                        </div><!--end col-->
                    </div><!--end row-->
                                    </form>
            </div>
        </div>
    </div>
</div>

<footer class="footer">
    <div class="container-fluid">
        <div class="row">
            <div class="col-sm-6">
                Copyright <script>document.write(new Date().getFullYear())</script> © member.sugarpay66.io            </div>
            <div class="col-sm-6">
                
            </div>
        </div>
    </div>
</footer>
</div>
<!-- end main content-->

</div>
<!-- END layout-wrapper -->

<!--start back-to-top-->
<button onclick="topFunction()" class="btn btn-primary btn-icon" id="back-to-top">
    <i class="ri-arrow-up-line"></i>
</button>
<!--end back-to-top-->

<!--preloader-->
<div id="preloader">
    <div id="status">
        <div class="spinner-border text-primary avatar-sm" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
</div>

<div class="modal fade" id="modelPreviewImage" tabindex="-1" aria-labelledby="modelPreviewImage" aria-modal="true">
    <div class="modal-dialog ">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalgridLabel">Preview Image</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <img src="" id="imgPreviewImage" style="width: 100%;" alt="">
            </div>
        </div>
    </div>
</div>



<!-- JAVASCRIPT -->
<script src="public/assets/libs/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="public/assets/libs/simplebar/simplebar.min.js"></script>
<script src="public/assets/libs/node-waves/waves.min.js"></script>
<script src="public/assets/libs/feather-icons/feather.min.js"></script>
<script src="public/assets/js/pages/plugins/lord-icon-2.1.0.js"></script>
<script src="public/assets/js/plugins.js?date=202403042044"></script>

<script src="public/assets/js/pages/notifications.init.js"></script>

<script src="https://code.jquery.com/jquery-3.6.0.min.js" integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>

<!--datatable js-->
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<!-- <script src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script> -->
<script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>

<!-- Sweet Alerts js -->
<script src="public/assets/libs/sweetalert2/sweetalert2.min.js"></script>

<script src="public/assets/libs/flatpickr/flatpickr.min.js"></script>


<!-- Sweet alert init js-->
<script src="public/assets/js/pages/sweetalerts.init.js"></script>

<!-- Moment js -->
<script src="public/assets/libs/moment/moment.js"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.blockUI/2.70/jquery.blockUI.min.js" integrity="sha512-eYSzo+20ajZMRsjxB6L7eyqo5kuXuS2+wEbbOkpaur+sA2shQameiJiWEzCIDwJqaB0a4a6tCuEvCOBHUg3Skg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.blockUI/2.70/jquery.blockUI.js" integrity="sha512-QSb5le+VXUEVEQbfljCv8vPnfSbVoBF/iE+c6MqDDqvmzqnr4KL04qdQMCm0fJvC3gCWMpoYhmvKBFqm1Z4c9A==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<!-- <script src="public/assets/js/pages/datatables.init.js"></script> -->

<!-- dropzone min -->
<script src="public/assets/libs/dropzone/dropzone-min.js"></script>
<!-- filepond js -->
<script src="public/assets/libs/filepond/filepond.min.js"></script>
<script src="public/assets/libs/filepond-plugin-image-preview/filepond-plugin-image-preview.min.js"></script>
<script src="public/assets/libs/filepond-plugin-file-validate-size/filepond-plugin-file-validate-size.min.js"></script>
<script src="public/assets/libs/filepond-plugin-image-exif-orientation/filepond-plugin-image-exif-orientation.min.js"></script>
<script src="public/assets/libs/filepond-plugin-file-encode/filepond-plugin-file-encode.min.js"></script>

<!-- App js -->
<script src="public/assets/js/app.js?t=1746205200"></script>
<script>

 
    var tableDeposit =  { table : '',tableDrawStatus :true, tooltipTriggerList : '',tooltipList : ''};
    document.addEventListener("DOMContentLoaded",function(){
        new DataTable(".alternative-pagination",{
        })
        
    })
    $(document).ready(function () {
                        
                
    });

    function unshowAnnouncement(id){
        setCookie("cookie_announcement", id, 7); 
        $("#modalAnnouncement").modal('hide')
    }

    function setCookie(name, value, days) {
        var expires = "";
        if (days) {
            var date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + (value || "") + expires + "; path=/";
    }
    function getCookie(name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(';');
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    }

    $(document).on('click','img.previewImage',function(){
        let src = $(this).attr('src');
        $("#imgPreviewImage").attr('src', src);

        $("#modelPreviewImage").modal("show");
    });

    function formatNumber(value,digit = 2){
        var val = isNaN(value) ? 0 : value;
        var number = parseFloat(val).toFixed(digit).toLocaleString(undefined, {
            maximumFractionDigits: digit
        });
        return number.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
    function preview_image(event, obj) {
        var output = document.getElementById('show_' + obj.id);
        output.src = URL.createObjectURL(event.target.files[0]);
    }

   
    function copyButton(elm, copyText, afterTextButton){
        copyToClipboard(copyText);
        $(elm).html(afterTextButton);
    }
    function copyToClipboard(text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(
                function () { 
                    console.log('Text copied to clipboard'); 
                    Swal.fire({
                        position: 'top-end',
                        icon: 'success',
                        title: 'Text copied to clipboard',
                        showConfirmButton: false,
                        timer: 1500,
                        
                    })
                },
                function (err) { console.error('Could not copy text: ', err); }
            );
        } else {
            let input = document.createElement('textarea');
            input.style.position = 'fixed';
            input.style.zIndex = 9999;
            input.value = text;
            document.body.appendChild(input);
            input.select();
            input.focus();
            document.execCommand('copy');
            document.body.removeChild(input);
        }

    }


</script>

<script lang="javascript" src="https://cdn.sheetjs.com/xlsx-latest/package/dist/xlsx.full.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.js"></script>
<script>
    var url_ajax = '/withdraw_fund/';
    let start_date = moment().add(-7,"day").format('YYYY-MM-DD 00:00').toString();
    let end_date = moment().format('YYYY-MM-DD 23:59').toString();
    let table =  '';
    let tableDrawStatus =  true;
    let tooltipTriggerList ='';
    let tooltipList ='';
    let bank = {mdr_rate:0};
    let minimum_amount = 10;
    let minimum_movement_amount = 1000;
    let bank_list = [{"bank_code":"034","short_name":"BAAC","bank_name_th":"\u0e18\u0e19\u0e32\u0e04\u0e32\u0e23\u0e40\u0e1e\u0e37\u0e48\u0e2d\u0e01\u0e32\u0e23\u0e40\u0e01\u0e29\u0e15\u0e23\u0e41\u0e25\u0e30\u0e2a\u0e2b\u0e01\u0e23\u0e13\u0e4c\u0e01\u0e32\u0e23\u0e40\u0e01\u0e29\u0e15\u0e23","bank_name_en":"Bank for Agriculture and Agricultural Cooperatives.","scb_bank_code":"034","kbank_bank_code":"026","ktb_bank_code":"034","is_delete":"0","is_enable":"1","sms_short_name":"BAAC","is_transfer_group":"1"},{"bank_code":"025","short_name":"BAY","bank_name_th":"\u0e18\u0e19\u0e32\u0e04\u0e32\u0e23\u0e01\u0e23\u0e38\u0e07\u0e28\u0e23\u0e35\u0e2d\u0e22\u0e38\u0e18\u0e22\u0e32","bank_name_en":"Bank of Ayudhya Public Company Limite","scb_bank_code":"025","kbank_bank_code":"017","ktb_bank_code":"025","is_delete":"0","is_enable":"1","sms_short_name":"BAY","is_transfer_group":"1"},{"bank_code":"002","short_name":"BBL","bank_name_th":"\u0e18\u0e19\u0e32\u0e04\u0e32\u0e23\u0e01\u0e23\u0e38\u0e07\u0e40\u0e17\u0e1e","bank_name_en":"BANGKOK BANK PUBLIC COMPANY LIMITED","scb_bank_code":"002","kbank_bank_code":"003","ktb_bank_code":"002","is_delete":"0","is_enable":"1","sms_short_name":"BBL","is_transfer_group":"1"},{"bank_code":"901","short_name":"CDM","bank_name_th":"\u0e42\u0e2d\u0e19\u0e40\u0e07\u0e34\u0e19\u0e08\u0e32\u0e01\u0e15\u0e39\u0e49 ATM","bank_name_en":"ATM","scb_bank_code":"901","kbank_bank_code":"901","ktb_bank_code":"901","is_delete":"0","is_enable":"1","sms_short_name":"CDM","is_transfer_group":"1"},{"bank_code":"022","short_name":"CIMB","bank_name_th":"\u0e18\u0e19\u0e32\u0e04\u0e32\u0e23\u0e0b\u0e35\u0e44\u0e2d\u0e40\u0e2d\u0e47\u0e21\u0e1a\u0e35\u0e44\u0e17\u0e22","bank_name_en":"CIMB Thai Bank Public Company Limited","scb_bank_code":"022","kbank_bank_code":"018","ktb_bank_code":"022","is_delete":"0","is_enable":"1","sms_short_name":"CMBT","is_transfer_group":"1"},{"bank_code":"017","short_name":"CITI","bank_name_th":"\u0e0b\u0e34\u0e15\u0e35\u0e49\u0e41\u0e1a\u0e07\u0e01\u0e4c","bank_name_en":"Citibank","scb_bank_code":"017","kbank_bank_code":"024","ktb_bank_code":"017","is_delete":"0","is_enable":"1","sms_short_name":"CITI","is_transfer_group":"1"},{"bank_code":"033","short_name":"GHB","bank_name_th":"\u0e18\u0e19\u0e32\u0e04\u0e32\u0e23\u0e2d\u0e32\u0e04\u0e32\u0e23\u0e2a\u0e07\u0e40\u0e04\u0e23\u0e32\u0e30\u0e2b\u0e4c (\u0e18\u0e2d\u0e2a.)","bank_name_en":"Government Housing Bank","scb_bank_code":"033","kbank_bank_code":"025","ktb_bank_code":"033","is_delete":"0","is_enable":"1","sms_short_name":"GHB","is_transfer_group":"1"},{"bank_code":"030","short_name":"GSB","bank_name_th":"\u0e18\u0e19\u0e32\u0e04\u0e32\u0e23\u0e2d\u0e2d\u0e21\u0e2a\u0e34\u0e19","bank_name_en":"Government Savings Bank","scb_bank_code":"030","kbank_bank_code":"022","ktb_bank_code":"030","is_delete":"0","is_enable":"1","sms_short_name":"GSBA","is_transfer_group":"1"},{"bank_code":"066","short_name":"ISBT","bank_name_th":"\u0e18\u0e19\u0e32\u0e04\u0e32\u0e23\u0e2d\u0e34\u0e2a\u0e25\u0e32\u0e21","bank_name_en":"Islamic Bank of Thailand","scb_bank_code":"066","kbank_bank_code":"066","ktb_bank_code":"066","is_delete":"0","is_enable":"1","sms_short_name":"ISBT","is_transfer_group":"1"},{"bank_code":"004","short_name":"KBANK","bank_name_th":"\u0e18\u0e19\u0e32\u0e04\u0e32\u0e23\u0e01\u0e2a\u0e34\u0e01\u0e23\u0e44\u0e17\u0e22","bank_name_en":"KASIKORNBANK PUBLIC. COMPANY LIMITED","scb_bank_code":"004","kbank_bank_code":"001","ktb_bank_code":"004","is_delete":"0","is_enable":"1","sms_short_name":"KBNK","is_transfer_group":"1"},{"bank_code":"069","short_name":"KKP","bank_name_th":" \u0e18\u0e19\u0e32\u0e04\u0e32\u0e23 \u0e40\u0e01\u0e35\u0e22\u0e23\u0e15\u0e34\u0e19\u0e32\u0e04\u0e34\u0e19 ","bank_name_en":"KIATNAKIN BANK","scb_bank_code":"069","kbank_bank_code":"023","ktb_bank_code":"069","is_delete":"0","is_enable":"1","sms_short_name":"KKB","is_transfer_group":"1"},{"bank_code":"006","short_name":"KTB","bank_name_th":"\u0e18\u0e19\u0e32\u0e04\u0e32\u0e23\u0e01\u0e23\u0e38\u0e07\u0e44\u0e17\u0e22","bank_name_en":"Krung Thai Bank Public Company Limited.","scb_bank_code":"006","kbank_bank_code":"004","ktb_bank_code":"006","is_delete":"0","is_enable":"1","sms_short_name":"KTB","is_transfer_group":"1"},{"bank_code":"073","short_name":"LHBANK","bank_name_th":"\u0e18\u0e19\u0e32\u0e04\u0e32\u0e23\u0e41\u0e25\u0e19\u0e14\u0e4c \u0e41\u0e2d\u0e19\u0e14\u0e4c \u0e40\u0e2e\u0e32\u0e2a\u0e4c","bank_name_en":"LHBANK. Land and House Bank","scb_bank_code":"073","kbank_bank_code":"020","ktb_bank_code":"073","is_delete":"0","is_enable":"1","sms_short_name":"LHBANK","is_transfer_group":"1"},{"bank_code":"014","short_name":"SCB","bank_name_th":"\u0e44\u0e17\u0e22\u0e1e\u0e32\u0e13\u0e34\u0e0a\u0e22\u0e4c","bank_name_en":"Siam Commercial Bank PCL.","scb_bank_code":"014","kbank_bank_code":"010","ktb_bank_code":"014","is_delete":"0","is_enable":"1","sms_short_name":"SCB","is_transfer_group":"1"},{"bank_code":"018","short_name":"SMBC","bank_name_th":"\u0e18\u0e19\u0e32\u0e04\u0e32\u0e23 \u0e0b\u0e39\u0e21\u0e34\u0e42\u0e15\u0e42\u0e21 \u0e21\u0e34\u0e15\u0e0b\u0e38\u0e22 \u0e41\u0e1a\u0e07\u0e01\u0e34\u0e49\u0e07","bank_name_en":"Sumitomo Mitsui Banking Corporation","scb_bank_code":"018","kbank_bank_code":"032","ktb_bank_code":"018","is_delete":"0","is_enable":"1","sms_short_name":"SMBC","is_transfer_group":"1"},{"bank_code":"071","short_name":"TCRB","bank_name_th":"\u0e18\u0e19\u0e32\u0e04\u0e32\u0e23\u0e44\u0e17\u0e22\u0e40\u0e04\u0e23\u0e14\u0e34\u0e15 \u0e40\u0e1e\u0e37\u0e48\u0e2d\u0e23\u0e32\u0e22\u0e22\u0e48\u0e2d\u0e22","bank_name_en":"The Thai Credit Retail Bank Public Company Limited","scb_bank_code":"071","kbank_bank_code":"031","ktb_bank_code":"071","is_delete":"0","is_enable":"1","sms_short_name":"TCRB","is_transfer_group":"1"},{"bank_code":"067","short_name":"TISCO","bank_name_th":"\u0e18\u0e19\u0e32\u0e04\u0e32\u0e23\u0e17\u0e34\u0e2a\u0e42\u0e01\u0e49","bank_name_en":"TISCO Bank Public Company Limited","scb_bank_code":"067","kbank_bank_code":"029","ktb_bank_code":"067","is_delete":"0","is_enable":"1","sms_short_name":"TISCO","is_transfer_group":"1"},{"bank_code":"801","short_name":"TRUE","bank_name_th":"True Wallet","bank_name_en":"True Wallet","scb_bank_code":"801","kbank_bank_code":"801","ktb_bank_code":"801","is_delete":"0","is_enable":"1","sms_short_name":"TRUE","is_transfer_group":"1"},{"bank_code":"011","short_name":"TTB","bank_name_th":"\u0e18\u0e19\u0e32\u0e04\u0e32\u0e23\u0e17\u0e2b\u0e32\u0e23\u0e44\u0e17\u0e22\u0e18\u0e19\u0e0a\u0e32\u0e15\u0e34","bank_name_en":"Thai Military Bank Public Company Limited","scb_bank_code":"011","kbank_bank_code":"007","ktb_bank_code":"011","is_delete":"0","is_enable":"1","sms_short_name":"TMB","is_transfer_group":"1"},{"bank_code":"024","short_name":"UOB","bank_name_th":"\u0e18\u0e19\u0e32\u0e04\u0e32\u0e23\u0e22\u0e39\u0e42\u0e2d\u0e1a\u0e35","bank_name_en":"United Overseas Bank (Thai) Public Company Limited","scb_bank_code":"024","kbank_bank_code":"016","ktb_bank_code":"024","is_delete":"0","is_enable":"1","sms_short_name":"UOBT","is_transfer_group":"1"}];
    $(document).ready(function(){
        $("#start_date").flatpickr({
            enableTime: true,
            dateFormat: "Y-m-d H:i",
            time_24hr: true,
            defaultDate: [start_date],
            onChange: function(selectedDates, dateStr, instance) {
                start_date = dateStr;
            },
        });
        $("#end_date").flatpickr({
            enableTime: true,
            dateFormat: "Y-m-d H:i",
            time_24hr: true,
            defaultDate: [end_date],
            onChange: function(selectedDates, dateStr, instance) {
                end_date = dateStr;
            },
        });
        get_total_box_balance();
        get_datatable_list();
    });

    function get_total_box_balance(){
        $.ajax({
            url: url_ajax + 'getTotalBoxBalance',
            type: "post",
            async: false,
            data: {},
            dataType:"json",
            success: function (res) {
                if(res.error == '0'){
                    let deposit_balance = (res.result.deposit_balance!=null)?res.result.deposit_balance:0;
                    let withdraw_balance = (res.result.withdraw_balance!=null)?res.result.withdraw_balance:0;

                    $("#total_balance_amount").html(formatNumber(deposit_balance));
                    $("#total_withdraw_balance").html(formatNumber(withdraw_balance));

                    $("#total_balance_amount").attr('data-target',deposit_balance);
                    $("#total_withdraw_balance").attr('data-target',withdraw_balance);
                }else{
                    $("#total_balance_amount").html(0);
                    $("#total_withdraw_balance").html(0);

                    $("#total_balance_amount").attr('data-target',0);
                    $("#total_withdraw_balance").attr('data-target',0);
                }
            }
        });
    }

    function get_total_box_withdraw_fund(){
        var text_search = $("#text_search").val().trim();
        var status_search = $("#status_search").val().trim();
        var transaction_type_search = $("#transaction_type_search option:selected").val().trim();
        var from_txn_amount_search = $("#from_txn_amount_search").val().trim();
        var to_txn_amount_search = $("#to_txn_amount_search").val().trim();
        var deposit_type_search = $("#deposit_type_search").val().trim();
        $.ajax({
            url: url_ajax + 'getTotalBoxWithdrawFund',
            type: "post",
            async: false,
            data: {
                text_search: text_search,
                status_search: status_search,
                transaction_type_search: transaction_type_search,
                deposit_type_search: deposit_type_search,
                start_date: start_date+":00",
                end_date: end_date+":59",
                from_txn_amount : from_txn_amount_search,
                to_txn_amount : to_txn_amount_search
            },
            dataType:"json",
            success: function (res) {
                if(res.error == '0'){
                    let total_topup = (res.result.total_topup!=null)?res.result.total_topup:0;
                    let total_transfer = (res.result.total_transfer!=null)?res.result.total_transfer:0;
                    let total_all = parseFloat(total_topup) + parseFloat(total_transfer);

                    $("#total_all").html(formatNumber(total_all));
                    $("#total_topup").html(formatNumber(total_topup));
                    $("#total_transfer").html(formatNumber(total_transfer));

                    $("#total_all").attr('data-target',total_all);
                    $("#total_topup").attr('data-target',total_topup);
                    $("#total_transfer").attr('data-target',total_transfer);
                }else{
                    $("#total_all").html(0);
                    $("#total_topup").html(0);
                    $("#total_transfer").html(0);

                    $("#total_all").attr('data-target',0);
                    $("#total_topup").attr('data-target',0);
                    $("#total_transfer").attr('data-target',0);
                }
            }
        });
    }

    function get_datatable_list(){
        var text_search = $("#text_search").val().trim();
        var status_search = $("#status_search").val().trim();
        var transaction_type_search = $("#transaction_type_search option:selected").val().trim();
        var from_txn_amount_search = $("#from_txn_amount_search").val().trim();
        var to_txn_amount_search = $("#to_txn_amount_search").val().trim();
        var deposit_type_search = $("#deposit_type_search").val().trim();

        get_total_box_withdraw_fund()

        $.fn.dataTable.ext.errMode = 'none';
        table = $('#datatable-list').DataTable({
            // data: response.result,
            destroy: true,
            processing: true,
            serverSide: true,
            searchDelay: 500,
            ajax : {
                url : url_ajax + 'getDatatableList',
                type : "POST",
                data : {
                    text_search: text_search,
                    status_search: status_search,
                    transaction_type_search: transaction_type_search,
                    deposit_type_search: deposit_type_search,
                    start_date: start_date+":00",
                    end_date: end_date+":59",
                    from_txn_amount : from_txn_amount_search,
                    to_txn_amount : to_txn_amount_search
                },
            },
            columnDefs: [
                {
                    targets: [3,4,5,9,10],
                    className: 'text-right'
                }
                // ,{
                //     targets: [],
                //     className: 'text-center'
                // }
            ],
            columns : [
                {data: function(data, type, dataToSet) { return moment(data.created_date).format("YYYY-MM-DD HH:mm:ss"); }},
                {data: function(data, type, dataToSet) {
                    if(data.transaction_type == 'TOPUP'){
                        return `<span class="badge text-bg-primary">${data.transaction_type}</span>`;
                    }else if(data.transaction_type == 'MOVE'){
                        return `<span class="badge text-bg-warning">TRANSFER</span>`;
                    }else{
                        return `<span class="badge text-bg-dark">${data.transaction_type}</span>`;
                    }
                }},
                {data: function(data, type, dataToSet) { return data.order_id; }},
                {data: function(data, type, dataToSet) { return formatNumber(data.amount,2); }},
                {data: function(data, type, dataToSet) { return formatNumber(data.mdr_amount,2); }},
                {data: function(data, type, dataToSet) { return formatNumber(parseFloat(data.net_amount),2); }},
                {data: function(data, type, dataToSet) {
                    if(data.status == 'SUCCESS'){
                        return `<span class="badge text-bg-success">${data.status}</span>`;
                    }else if(data.status == 'WAIT_CONFIRM'){
                        return `<span class="badge text-bg-warning">${data.status}</span>`;
                    }else if(data.status == 'REFUND'){
                        return `<span class="badge text-bg-dark">${data.status}</span>`;
                    }else if(data.status == 'FAILED'){
                        return `<span class="badge text-bg-danger">${data.status}</span>`;
                    }else{
                        // return `<button class="btn btn-sm btn-warning" onclick="updatePaymentData(${data.transaction_id});"><i class="mdi mdi-cursor-default-click"></i> Get Update Data</button>`;
                        return `<span class="badge text-bg-warning" ><i class="mdi mdi-cursor-default-click"></i>WATIING</span>`;
                    }
                }},
                {data: function(data, type, dataToSet) { return `${data.bank_name??''} ${data.bank_acc_no??''}`; }},
                {data: function(data, type, dataToSet) {
                    if(data.updated_date == null){
                        return '';
                    }else{
                        return moment(data.updated_date).format("YYYY-MM-DD HH:mm:ss");
                    }
                }},
                {data: function(data, type, dataToSet) {
                    if(moment(data.updated_date).format("YYYY-MM-DD HH:mm:ss") > '2023-05-31 20:00:00'){
                        return formatNumber(data.before_balance,2);
                    }else{
                        return `<span class="text-muted">n/a</span>`;
                    }
                }},
                {data: function(data, type, dataToSet) {
                    if(moment(data.updated_date).format("YYYY-MM-DD HH:mm:ss") > '2023-05-31 20:00:00'){
                        return formatNumber(data.after_balance,2);
                    }else{
                        return `<span class="text-muted">n/a</span>`;
                    }
                }},
            ],
            order: [[0, 'desc']],
        });

        tableDraw();
    }

    function tableDraw(){
        if(tableDrawStatus){
            table.on( 'draw', function () {
                tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]')
                tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl))
            } );    
            tableDrawStatus = false;
        }
        
    }

    $('#makePaymentAmount,#makePaymentBankAccountNo,#movementFundAmount').on('keydown', function(e) {
        var val = $(this).val();
        val = val.replace(/[^\d.]/g,''); // Remove all non-numeric characters
        $(this).val(val);
    });

    $('#makePaymentAmount').on('keyup', function(e) {
        let amount = parseFloat($(this).val());
        if(amount > 1999999){
            amount = 1999999;
            $(this).val(amount)
        }
        let mdr = (amount * bank.mdr_rate) / 100;
        let net = parseFloat(amount-mdr);
        $('#makePaymentMDRAmount').val(formatNumber(mdr.toFixed(2),2));
        $('#makePaymentNetAmount').val(formatNumber(net.toFixed(2),2));
    });

    function updatePaymentData(transaction_id){
        $.ajax({
            url: url_ajax + 'update_payment_data.json',
            type: "post",
            async: false,
            data: {
                transaction_id:transaction_id
            },
            dataType:"json",
            success: function (res) {
                console.log(res);
                if(res.error == 0){
                    Swal.fire({
                        icon: 'success',
                        title: `Update withdraw fund success.`,
                        confirmButtonText: "Close"
                    })
                }else if(res.error == 2){
                    Swal.fire({
                        icon: 'warning',
                        title: `This transaction already update.`,
                        confirmButtonText: "Close"
                    })
                }else{
                    Swal.fire({
                        icon: 'warning',
                        title: `Waiting bank transaction update.`,
                        confirmButtonText: "Close"
                    })
                }
            }
        });
    }

    function makePayment(){
        $.ajax({
            url: url_ajax + 'get_bank_data.json',
            type: "post",
            async: false,
            data: {},
            contentType: false,
            processData: false,
            dataType:"json",
            success: function (res) {
                console.log(res);
                if(res.error == 0){
                    for(const item of res.result){
                        if(item.bank_type == 'WITHDRAW'){
                            bank = item;
                            bank['mdr_rate'] = parseFloat(bank.mdr_rate_topup);
                            $("#makePaymentMDRRate").html(formatNumber(bank.mdr_rate,2));
                        }
                    }

                    $("#formMakePayment")[0].reset();
                    $("#modalMakePayment").modal("show");
                }else{
                    Swal.fire({
                        icon: 'warning',
                        title: `Can't get withdraw bank data. please contact administrator.`,
                        confirmButtonText: "Close"
                    })
                }
            }
        });

    }


    function confirmMakePayment(){
        let paymentType = $("#makePaymentType option:selected").val();
        let amount = parseFloat($("#makePaymentAmount").val());
        let makePaymentBankAccountNo = $("#makePaymentBankAccountNo").val();;
        let makePaymentBankName = $("#makePaymentBankName option:selected").val();;

        if(paymentType=="CDM"){
            if(amount < minimum_amount || amount == null || amount == ''){
                Swal.fire({
                    icon: 'warning',
                    title: `Please input data was required.`,
                    confirmButtonText: "Close"
                })

                return false;
            }
            paymentType = "TRANSFER"
        }else{
            if(amount < minimum_amount || amount == null || amount == ''|| makePaymentBankAccountNo == ''|| makePaymentBankName == ''){
                Swal.fire({
                    icon: 'warning',
                    title: `Please input data was required.`,
                    confirmButtonText: "Close"
                })

                return false;
            }
        }

        if(amount > 1999999 ){
            Swal.fire({
                icon: 'warning',
                title: `Maximum amount is 1,999,999 THB.`,
                confirmButtonText: "Close"
            })

            return false;
        }

        Swal.fire({
            title: 'Confirm',
            text: "You want to make transaction fund, right?",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Confirm',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                saveMakePayment();
            }
        })
    }

    function saveMakePayment(){
        let paymentType = $("#makePaymentType option:selected").val();
        let amount = parseFloat($("#makePaymentAmount").val());
        let makePaymentBankAccountNo = $("#makePaymentBankAccountNo").val();;
        let makePaymentBankName = $("#makePaymentBankName").val();;

        if(amount > 1999999 ){
            Swal.fire({
                icon: 'warning',
                title: `Maximum amount is 1,999,999 THB.`,
                confirmButtonText: "Close"
            })

            return false;
        }

        if(paymentType=="CDM"){
            paymentType = "TRANSFER"
            if(amount < minimum_amount || amount == null || amount == ''){
                Swal.fire({
                    icon: 'warning',
                    title: `Please fill order id or amount`,
                    confirmButtonText: "Close"
                })

                return false;
            }
        }else{
            if(amount < minimum_amount || amount == null || amount == ''|| makePaymentBankAccountNo == ''|| makePaymentBankName == ''){
                Swal.fire({
                    icon: 'warning',
                    title: `Please fill order id or amount`,
                    confirmButtonText: "Close"
                })

                return false;
            }
        }
        const formData = new FormData($('#formMakePayment')[0]);

        if(paymentType=="CDM"){
            paymentType = "TRANSFER"
        }
        $("#makePaymentType").attr('disabled','disabled');
        $("#makePaymentAmount").attr('disabled','disabled');
        $("#makePaymentBankAccountName").attr('disabled','disabled');
        $("#makePaymentBankAccountNo").attr('disabled','disabled');
        $("#makePaymentBankName").attr('disabled','disabled');

        $.ajax({
            url: url_ajax + 'make_payment',
            type: "post",
            async: false,
            data: formData,
            contentType: false,
            processData: false,
            dataType:"json",
            success: function (res) {

                if(res.error == '0' && res.code == '200'){
                    $("#makePaymentSubmit").hide();
                    $("#makePaymentResult").show();

                    let redirect_url = '-';
                    if(res.result.redirect_url != ''){
                        redirect_url = `<button class="btn btn-sm btn-warning mx-1" onclick="copyButton(this, '${res.result.redirect_url}', 'Copied!!')">Copy Link</button> `
                        redirect_url += `<a href="${res.result.redirect_url}" class="btn btn-sm btn-info" target="_blank">Open Link.</a>`
                    }

                    if(paymentType == 'QRCODE'){
                        let imageQrcode = '-';
                        if(res.result.image != undefined && res.result.image != ''){
                            imageQrcode = `<button class="btn btn-sm btn-warning" onclick="copyButton(this, '${res.result.image}', 'Copied!!')">Copy Link Image</button><br>`
                            imageQrcode += `<img src="${res.result.image}" style="width: 100%; max-width: 200px;">`
                        }
                        $("#makePaymentResultImage").html(`${imageQrcode}`);
                        $("#makePaymentResultTransferBank").closest('tr').hide();
                    }else{
                        let transferBank = '';
                        let bankName = bank_list.filter((item)=>{
                            return item.short_name == res.result.bank_detail.bank_name;
                        })
                        if(res.result.bank_detail != undefined && res.result.bank_detail != ''){
                            transferBank = `กรุณาโอนเงินด้วยบัญชีที่ระบุด้านบนเข้ามาที่บัญชี<br>`;
                            transferBank += `ธนาคาร: <span class="text-danger text-bold">${res.result.bank_detail.bank_name} - ${bankName[0].bank_name_th}</span><br>`;
                            transferBank += `เลขที่: <span class="text-danger text-bold">${res.result.bank_detail.account_no}</span><br>`;
                            transferBank += `ชื่อบัญชี: <span class="text-danger text-bold">${res.result.bank_detail.account_name}</span>`;
                        }
                        $("#makePaymentResultTransferBank").html(`${transferBank}`);
                        $("#makePaymentResultImage").closest('tr').hide();
                    }

                    let timeOut = moment(res.result.timeout.date).format('YYYY-MM-DD HH:mm:ss').toString();

                    $("#makePaymentResultRefId").html(`${res.result.ref_id}`);
                    $("#makePaymentResultOrderId").html(`${res.result.order_id}`);
                    $("#makePaymentResultPrice").html(`${formatNumber(res.result.price,2)}`);
                    // $("#makePaymentResultRedirectURL").html(`${redirect_url}`);
                    $("#makePaymentResultTimeout").html(`${timeOut}`);

                }else{
                    $("#makePaymentType").removeAttr('disabled');
                    $("#makePaymentAmount").removeAttr('disabled');
                    $("#makePaymentBankAccountName").removeAttr('disabled');
                    $("#makePaymentBankAccountNo").removeAttr('disabled');
                    $("#makePaymentBankName").removeAttr('disabled');

                    $("#makePaymentSubmit").show();
                    $("#makePaymentResult").hide();

                    Swal.fire({
                        icon: 'warning',
                        title: res.message,
                        confirmButtonText: "Close"
                    }).then((result) => {
                    })

                }
            }
        });

        return false;
    }

    $('#modalMakePayment').on('hidden.bs.modal', function (e) {
        window.location.reload();
    })


    function changePaymentType(){
        var makePaymentType = $("#makePaymentType option:selected").val();
        if(makePaymentType=="CDM"){
            $("#remarkCDM").html(`<span style="color:red">*กรุณาถ่ายสลิปฝากเงิน และส่งกลับมาให้แอดมิน เพื่อยืนยันรายการด้วยคะ<br />*ห้ามรวมยอดสลิป สร้าง 1 รายการ ต่อ 1 สลิป ตามยอดที่ฝากเงิน</span>`)
            $("#makePaymentBankAccountName").attr('disabled','disabled')
            $("#makePaymentBankAccountName").val('')

            $("#makePaymentBankAccountNo").attr('disabled','disabled')
            $("#makePaymentBankAccountNo").val('')

            $("#makePaymentBankName").attr('disabled','disabled')
            $("#makePaymentBankName").val('')
            
        }else{
            $("#remarkCDM").html(``)
            $("#makePaymentBankAccountName").removeAttr('disabled')
            $("#makePaymentBankAccountName").val('')

            $("#makePaymentBankAccountNo").removeAttr('disabled')
            $("#makePaymentBankAccountNo").val('')
            
        }
    }

    function exportData(){
        Swal.fire({
            title: 'Processing data to export',
            allowOutsideClick: false,
            showConfirmButton: false,
            onBeforeOpen: () => {
                Swal.showLoading()
            },
        });
        Swal.showLoading();

        setTimeout(function(){
            var text_search = $("#text_search").val().trim();
            var status_search = $("#status_search").val().trim();
            var transaction_type_search = $("#transaction_type_search option:selected").val().trim();
            var from_txn_amount_search = $("#from_txn_amount_search").val().trim();
            var to_txn_amount_search = $("#to_txn_amount_search").val().trim();

            var deposit_type_search = $("#deposit_type_search").val().trim();

            let check = true;
            let page = 1;
            let length = 3000;
            let result_csv = [];
            while(check){
                $.ajax({
                    url: url_ajax + 'getDatatableList',
                    method: 'POST',
                    data: {
                        text_search: text_search,
                        status_search: status_search,
                        transaction_type_search: transaction_type_search,
                        deposit_type_search: deposit_type_search,
                        start_date: start_date+":00",
                        end_date: end_date+":59",
                        from_txn_amount : from_txn_amount_search,
                        to_txn_amount : to_txn_amount_search,
                        start: (page-1)*length,
                        length: length,
                        draw: page,
                    },
                    async: false,
                    dataType: "json",
                    success: function (response) {
                        if(response.error == 0){
                            result_csv = result_csv.concat(response.result);
                        }else{
                            // console.log("step 3");
                            check = false;
                        }
                    }
                })
                if(page >50){
                    check = false;
                }
                page++;
            }
            if(result_csv.length > 0){
                exportExcel(result_csv, `transaction_${moment().format("YYYY-MM-DD_HH-mm-ss").toString()}.xlsx`);
                // export_csv(result_csv,"export_รายชื่อพระภิกษุ_"+moment().format("YYYY-MM-DD_HH-mm-ss").toString())
            }else{
                alert('ไม่พบข้อมูล');
            }
            Swal.close()
        },300)

        return false;
    }

    function exportExcel(data,filename){
        // Define the data to be exported
        // const data = [
        //     { name: 'Alice', age: 25, email: '<EMAIL>' },
        //     { name: 'Bob', age: 30, email: '<EMAIL>' },
        //     { name: 'Charlie', age: 35, email: '<EMAIL>' }
        // ];

        // Define the filename for the exported file
        // const filename = 'example.xlsx';

        // Convert the data to a worksheet
        const workbook = XLSX.utils.book_new();
        const worksheet = XLSX.utils.json_to_sheet(data);

        // define style with table border
        worksheet['!table'] = { // Add the border style
            style: 'border: 1px solid black;',
        };

        // Create a new workbook and add the worksheet to it
        XLSX.utils.book_append_sheet(workbook, worksheet);

        // Convert the workbook to a binary string
        const binaryString = XLSX.write(workbook, { bookType: 'xlsx', type: 'binary' });

        // Convert the binary string to a Blob object
        const blob = new Blob([s2ab(binaryString)], { type: 'application/octet-stream' });

        // Save the Blob object as a file using FileSaver.js
        window.saveAs(blob, filename);
    }

    // Helper function to convert a string to an ArrayBuffer
    function s2ab(s) {
        const buf = new ArrayBuffer(s.length);
        const view = new Uint8Array(buf);
        for (let i = 0; i < s.length; i++) {
            view[i] = s.charCodeAt(i) & 0xFF;
        }
        return buf;
    }

    /*
     * WITHDRAW FUND MOVEMENT
     * */
    function makeMovement(){
        // $("#movementFundDepositBalance").val(0);
        $("#movementFundMaxMoveBalance").val(0);
        $("#movementFundMaxMoveBalanceLabel").html(`/0`);
        $.ajax({
            url: url_ajax + 'get_bank_deposit_data',
            type: "post",
            async: false,
            data: {},
            contentType: false,
            processData: false,
            dataType:"json",
            success: function (res) {
                console.log(res);
                if(res.error == 0){
                    // $("#movementFundDepositBalance").val(formatNumber(res.result.deposit_balance));
                    $("#movementFundMaxMoveBalance").val(formatNumber(res.result.movement_balance));
                    $("#movementFundMaxMoveBalanceLabel").html(`/${formatNumber(res.result.movement_balance)}`);

                    $("#modalMovementFund").modal("show");
                }else{
                    Swal.fire({
                        icon: 'warning',
                        title: `Can't get deposit balance data.`,
                        confirmButtonText: "Close"
                    })
                }
            }
        });

    }

    function movementMax(){
        let movement_balance = $("#movementFundMaxMoveBalance").val().replace(/,/ig, "");
        $("#movementFundAmount").val(movement_balance);
    }

    
    function confirmMakeMovement(){
        let amount = parseFloat($("#movementFundAmount").val().replace(/,/ig, "").trim());
        let movementFundMaxMoveBalance = parseFloat($("#movementFundMaxMoveBalance").val().replace(/,/ig, "").trim());
        if(amount == null || amount === NaN || amount == '' || $("#movementFundAmount").val().trim() == ''){
            Swal.fire({
                icon: 'warning',
                title: `Some input data is empty. Please ensure that all input data is filled.`,
                confirmButtonText: "Close"
            })

            return false;
        }else if(amount < minimum_movement_amount || amount > movementFundMaxMoveBalance){
            Swal.fire({
                icon: 'warning',
                title: `Minimum amount is ${minimum_movement_amount} and Maximum amount on you deposit wallet is ${movementFundMaxMoveBalance}. Please fill amount in this range.`,
                confirmButtonText: "Close"
            })

            return false;
        }else{

            Swal.fire({
                title: 'Confirm',
                text: "You want to transfer deposit fund to withdraw fund, right?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Confirm',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    saveMakeMovement(amount);
                }
            })
        }

    }

    function saveMakeMovement(amount){
        Swal.fire({
            title: 'Processing movement data ',
            allowOutsideClick: false,
            showConfirmButton: false,
            onBeforeOpen: () => {
                Swal.showLoading()
            },
        });
        Swal.showLoading();

        $.ajax({
            url: url_ajax + 'process_withdraw_fund_movement',
            type: "post",
            async: false,
            data: {
                amount: amount,
            },
            dataType:"json",
            success: function (res) {
                console.log(res);
                Swal.close()
                if(res.error == 0){
                    Swal.fire({
                        icon: 'success',
                        title: res.msg,
                        confirmButtonText: "Close"
                    }).then((result) => {
                        window.location.reload();
                    })

                    $("#formMovementFund")[0].reset();
                    $("#modalMovementFund").modal("hide");
                }else{
                    Swal.fire({
                        icon: 'warning',
                        title: res.msg,
                        confirmButtonText: "Close"
                    })
                }
            }
        });
    }
    /*
     * WITHDRAW FUND MOVEMENT
     * */
    
</script>

</body>

</html>

