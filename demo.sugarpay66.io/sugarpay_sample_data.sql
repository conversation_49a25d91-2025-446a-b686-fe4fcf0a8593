-- =====================================================
-- SugarPay Sample Data and Initial Setup
-- ข้อมูลตัวอย่างและการตั้งค่าเริ่มต้น
-- =====================================================

-- ข้อมูลผังบัญชีพื้นฐาน
INSERT INTO chart_of_accounts (account_code, account_name, account_name_en, account_type, account_subtype, normal_balance, is_system_account, description) VALUES
-- สินทรัพย์
('1000', 'เงินสดและเงินฝากธนาคาร', 'Cash and Bank Deposits', 'ASSET', 'CURRENT_ASSET', 'DEBIT', 1, 'บัญชีเงินสดและเงินฝากธนาคารทั้งหมด'),
('1001', 'เงินสดในมือ', 'Cash on Hand', 'ASSET', 'CURRENT_ASSET', 'DEBIT', 1, 'เงินสดที่มีในมือ'),
('1002', 'เงินฝากธนาคาร - ฝาก', 'Bank Deposit Account', 'ASSET', 'CURRENT_ASSET', 'DEBIT', 1, 'บัญชีธนาคารสำหรับรับเงินฝาก'),
('1003', 'เงินฝากธนาคาร - ถอน', 'Bank Withdraw Account', 'ASSET', 'CURRENT_ASSET', 'DEBIT', 1, 'บัญชีธนาคารสำหรับจ่ายเงินถอน'),
('1100', 'ลูกหนี้การค้า', 'Accounts Receivable', 'ASSET', 'CURRENT_ASSET', 'DEBIT', 1, 'ลูกหนี้จากการให้บริการ'),
('1101', 'ลูกหนี้ร้านค้า - ยอดฝาก', 'Merchant Receivable - Deposit', 'ASSET', 'CURRENT_ASSET', 'DEBIT', 1, 'ยอดเงินฝากของร้านค้า'),
('1102', 'ลูกหนี้ร้านค้า - ยอดถอน', 'Merchant Receivable - Withdraw', 'ASSET', 'CURRENT_ASSET', 'DEBIT', 1, 'ยอดเงินถอนของร้านค้า'),

-- หนี้สิน
('2000', 'เจ้าหนี้การค้า', 'Accounts Payable', 'LIABILITY', 'CURRENT_LIABILITY', 'CREDIT', 1, 'เจ้าหนี้จากการดำเนินธุรกิจ'),
('2001', 'เจ้าหนี้ร้านค้า - ยอดฝาก', 'Merchant Payable - Deposit', 'LIABILITY', 'CURRENT_LIABILITY', 'CREDIT', 1, 'เงินฝากที่ต้องจ่ายให้ร้านค้า'),
('2002', 'เจ้าหนี้ร้านค้า - ยอดถอน', 'Merchant Payable - Withdraw', 'LIABILITY', 'CURRENT_LIABILITY', 'CREDIT', 1, 'เงินถอนที่ต้องจ่ายให้ร้านค้า'),
('2100', 'หนี้สินหมุนเวียนอื่น', 'Other Current Liabilities', 'LIABILITY', 'CURRENT_LIABILITY', 'CREDIT', 1, 'หนี้สินหมุนเวียนอื่นๆ'),

-- ส่วนของเจ้าของ
('3000', 'ทุน', 'Capital', 'EQUITY', 'CAPITAL', 'CREDIT', 1, 'ทุนของบริษัท'),
('3100', 'กำไรสะสม', 'Retained Earnings', 'EQUITY', 'RETAINED_EARNINGS', 'CREDIT', 1, 'กำไรสะสมจากการดำเนินงาน'),

-- รายได้
('4000', 'รายได้จากค่าธรรมเนียม', 'Fee Revenue', 'REVENUE', 'OPERATING_REVENUE', 'CREDIT', 1, 'รายได้จากค่าธรรมเนียมทั้งหมด'),
('4001', 'รายได้ค่าธรรมเนียมฝาก (MDR)', 'Deposit Fee Revenue', 'REVENUE', 'OPERATING_REVENUE', 'CREDIT', 1, 'รายได้จากค่าธรรมเนียมการฝากเงิน'),
('4002', 'รายได้ค่าธรรมเนียมถอน', 'Withdraw Fee Revenue', 'REVENUE', 'OPERATING_REVENUE', 'CREDIT', 1, 'รายได้จากค่าธรรมเนียมการถอนเงิน'),
('4003', 'รายได้ค่าธรรมเนียม TopUp', 'TopUp Fee Revenue', 'REVENUE', 'OPERATING_REVENUE', 'CREDIT', 1, 'รายได้จากค่าธรรมเนียมการเติมเงิน'),
('4004', 'รายได้ค่าธรรมเนียม Settlement', 'Settlement Fee Revenue', 'REVENUE', 'OPERATING_REVENUE', 'CREDIT', 1, 'รายได้จากค่าธรรมเนียมการโอนเงิน'),
('4100', 'รายได้อื่น', 'Other Revenue', 'REVENUE', 'OTHER_REVENUE', 'CREDIT', 1, 'รายได้อื่นๆ'),

-- ค่าใช้จ่าย
('5000', 'ค่าใช้จ่ายในการดำเนินงาน', 'Operating Expenses', 'EXPENSE', 'OPERATING_EXPENSE', 'DEBIT', 1, 'ค่าใช้จ่ายในการดำเนินงาน'),
('5001', 'ค่าธรรมเนียมธนาคาร', 'Bank Fees', 'EXPENSE', 'OPERATING_EXPENSE', 'DEBIT', 1, 'ค่าธรรมเนียมที่จ่ายให้ธนาคาร'),
('5002', 'ค่าใช้จ่ายระบบ IT', 'IT System Expenses', 'EXPENSE', 'OPERATING_EXPENSE', 'DEBIT', 1, 'ค่าใช้จ่ายระบบเทคโนโลยี'),
('5100', 'ค่าใช้จ่ายอื่น', 'Other Expenses', 'EXPENSE', 'OTHER_EXPENSE', 'DEBIT', 1, 'ค่าใช้จ่ายอื่นๆ');

-- ข้อมูลสิทธิ์พื้นฐาน
INSERT INTO permissions (permission_name, permission_description, permission_category, is_system_permission) VALUES
-- สิทธิ์ร้านค้า
('merchant.view', 'ดูข้อมูลร้านค้า', 'MERCHANT', 1),
('merchant.edit', 'แก้ไขข้อมูลร้านค้า', 'MERCHANT', 1),
('merchant.create', 'สร้างร้านค้าใหม่', 'MERCHANT', 1),
('merchant.delete', 'ลบร้านค้า', 'MERCHANT', 1),
('merchant.balance.view', 'ดูยอดเงินร้านค้า', 'MERCHANT', 1),
('merchant.balance.adjust', 'ปรับยอดเงินร้านค้า', 'MERCHANT', 1),

-- สิทธิ์ธุรกรรม
('transaction.view', 'ดูรายการธุรกรรม', 'TRANSACTION', 1),
('transaction.approve', 'อนุมัติธุรกรรม', 'TRANSACTION', 1),
('transaction.cancel', 'ยกเลิกธุรกรรม', 'TRANSACTION', 1),
('transaction.refund', 'คืนเงินธุรกรรม', 'TRANSACTION', 1),

-- สิทธิ์ธนาคาร
('bank.view', 'ดูข้อมูลบัญชีธนาคาร', 'BANK', 1),
('bank.edit', 'แก้ไขข้อมูลบัญชีธนาคาร', 'BANK', 1),
('bank.reconcile', 'กระทบยอดธนาคาร', 'BANK', 1),

-- สิทธิ์รายงาน
('report.view', 'ดูรายงาน', 'REPORT', 1),
('report.export', 'ส่งออกรายงาน', 'REPORT', 1),
('report.schedule', 'ตั้งค่ารายงานอัตโนมัติ', 'REPORT', 1),

-- สิทธิ์ผู้ใช้งาน
('user.view', 'ดูข้อมูลผู้ใช้งาน', 'USER', 1),
('user.create', 'สร้างผู้ใช้งานใหม่', 'USER', 1),
('user.edit', 'แก้ไขข้อมูลผู้ใช้งาน', 'USER', 1),
('user.delete', 'ลบผู้ใช้งาน', 'USER', 1),
('user.permission', 'จัดการสิทธิ์ผู้ใช้งาน', 'USER', 1),

-- สิทธิ์ระบบ
('system.admin', 'ผู้ดูแลระบบ', 'SYSTEM', 1),
('system.settings', 'จัดการการตั้งค่าระบบ', 'SYSTEM', 1),
('system.audit', 'ดูบันทึกการตรวจสอบ', 'SYSTEM', 1),
('system.backup', 'สำรองข้อมูล', 'SYSTEM', 1);

-- ข้อมูลกลุ่มผู้ใช้งานพื้นฐาน
INSERT INTO user_groups (group_name, group_description, is_system_group, is_active) VALUES
('Super Admin', 'ผู้ดูแลระบบสูงสุด', 1, 1),
('System Admin', 'ผู้ดูแลระบบ', 1, 1),
('Merchant Admin', 'ผู้ดูแลร้านค้า', 1, 1),
('Merchant User', 'ผู้ใช้งานร้านค้า', 1, 1),
('Accountant', 'นักบัญชี', 1, 1),
('Auditor', 'ผู้ตรวจสอบ', 1, 1),
('Support', 'ฝ่ายสนับสนุน', 1, 1);

-- กำหนดสิทธิ์ให้กลุ่ม Super Admin (ทุกสิทธิ์)
INSERT INTO user_group_permissions (user_group_id, permission_id, is_granted)
SELECT 1, permission_id, 1 FROM permissions;

-- กำหนดสิทธิ์ให้กลุ่ม System Admin
INSERT INTO user_group_permissions (user_group_id, permission_id, is_granted)
SELECT 2, permission_id, 1 FROM permissions 
WHERE permission_name NOT IN ('system.admin');

-- กำหนดสิทธิ์ให้กลุ่ม Merchant Admin
INSERT INTO user_group_permissions (user_group_id, permission_id, is_granted)
SELECT 3, permission_id, 1 FROM permissions 
WHERE permission_category IN ('MERCHANT', 'TRANSACTION', 'BANK', 'REPORT');

-- กำหนดสิทธิ์ให้กลุ่ม Merchant User
INSERT INTO user_group_permissions (user_group_id, permission_id, is_granted)
SELECT 4, permission_id, 1 FROM permissions 
WHERE permission_name IN ('merchant.view', 'merchant.balance.view', 'transaction.view', 'bank.view', 'report.view');

-- ข้อมูลการตั้งค่าระบบพื้นฐาน
INSERT INTO system_settings (setting_key, setting_value, setting_type, setting_description, setting_category, is_system_setting) VALUES
-- การตั้งค่าทั่วไป
('system.name', 'SugarPay Payment System', 'STRING', 'ชื่อระบบ', 'GENERAL', 1),
('system.version', '1.0.0', 'STRING', 'เวอร์ชันระบบ', 'GENERAL', 1),
('system.timezone', 'Asia/Bangkok', 'STRING', 'เขตเวลา', 'GENERAL', 1),
('system.currency', 'THB', 'STRING', 'สกุลเงิน', 'GENERAL', 1),

-- การตั้งค่าค่าธรรมเนียม
('fee.deposit.default_rate', '1.50', 'DECIMAL', 'อัตราค่าธรรมเนียมฝากเริ่มต้น (%)', 'FEE', 1),
('fee.withdraw.default_amount', '10.00', 'DECIMAL', 'ค่าธรรมเนียมถอนเริ่มต้น (THB)', 'FEE', 1),
('fee.topup.default_rate', '1.50', 'DECIMAL', 'อัตราค่าธรรมเนียม TopUp เริ่มต้น (%)', 'FEE', 1),
('fee.settlement.default_amount', '10.00', 'DECIMAL', 'ค่าธรรมเนียม Settlement เริ่มต้น (THB)', 'FEE', 1),

-- การตั้งค่าธุรกรรม
('transaction.timeout.deposit', '1800', 'INTEGER', 'เวลาหมดอายุธุรกรรมฝาก (วินาที)', 'TRANSACTION', 1),
('transaction.timeout.withdraw', '3600', 'INTEGER', 'เวลาหมดอายุธุรกรรมถอน (วินาที)', 'TRANSACTION', 1),
('transaction.min_amount.deposit', '1.00', 'DECIMAL', 'จำนวนเงินขั้นต่ำสำหรับฝาก', 'TRANSACTION', 1),
('transaction.max_amount.deposit', '1000000.00', 'DECIMAL', 'จำนวนเงินสูงสุดสำหรับฝาก', 'TRANSACTION', 1),
('transaction.min_amount.withdraw', '100.00', 'DECIMAL', 'จำนวนเงินขั้นต่ำสำหรับถอน', 'TRANSACTION', 1),
('transaction.max_amount.withdraw', '500000.00', 'DECIMAL', 'จำนวนเงินสูงสุดสำหรับถอน', 'TRANSACTION', 1),

-- การตั้งค่าความปลอดภัย
('security.session_timeout', '3600', 'INTEGER', 'เวลาหมดอายุ Session (วินาที)', 'SECURITY', 1),
('security.max_login_attempts', '5', 'INTEGER', 'จำนวนครั้งสูงสุดในการเข้าสู่ระบบ', 'SECURITY', 1),
('security.password_min_length', '8', 'INTEGER', 'ความยาวรหัสผ่านขั้นต่ำ', 'SECURITY', 1),
('security.require_2fa', 'false', 'BOOLEAN', 'บังคับใช้ 2FA', 'SECURITY', 1),

-- การตั้งค่ารายงาน
('report.auto_generate_daily', 'true', 'BOOLEAN', 'สร้างรายงานรายวันอัตโนมัติ', 'REPORT', 1),
('report.auto_generate_time', '06:00:00', 'STRING', 'เวลาสร้างรายงานอัตโนมัติ', 'REPORT', 1),
('report.retention_days', '365', 'INTEGER', 'จำนวนวันเก็บรายงาน', 'REPORT', 1);

-- ข้อมูลงวดบัญชีเริ่มต้น
INSERT INTO accounting_periods (period_name, period_type, start_date, end_date, status) VALUES
('มกราคม 2025', 'MONTHLY', '2025-01-01', '2025-01-31', 'OPEN'),
('ไตรมาส 1/2025', 'QUARTERLY', '2025-01-01', '2025-03-31', 'OPEN'),
('ปี 2025', 'YEARLY', '2025-01-01', '2025-12-31', 'OPEN');
