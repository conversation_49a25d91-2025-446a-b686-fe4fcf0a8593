# 📊 เอกสารโครงสร้างฐานข้อมูล SugarPay Payment System

## 🎯 ภาพรวมระบบ

ระบบ SugarPay Payment System ประกอบด้วย 2 ระบบหลัก:
1. **ระบบ Merchant** - สำหรับร้านค้าจัดการธุรกรรมและยอดเงิน
2. **ระบบ Backoffice** - สำหรับผู้ดูแลระบบจัดการและตรวจสอบ

## 💰 โครงสร้างค่าธรรมเนียม (MDR Fees)

| ประเภทรายการ | ค่าธรรมเนียม MDR (Default) | หมายเหตุ |
|-------------|---------------------------|----------|
| **Deposit** | 1.5% | ขาฝาก |
| **Withdraw** | 10 THB ต่อรายการ | ขาถอน - ค่าธรรมเนียมเรียกเก็บแยกต่างหาก |
| **TopUp** | 1.5% | เติมเงินไปยัง Withdraw Balance |
| **Transfer** | ไม่มีค่าธรรมเนียม | โยกเงินจาก Deposit ไป Withdraw Balance |
| **Settlement** | 10 THB ต่อรายการ | ย้ายเงินไปบัญชีร้านค้าอัตโนมัติ |

## 🏪 ระบบ Merchant Database Schema

### 1. ตาราง `merchants` - ข้อมูลร้านค้า
จัดเก็บข้อมูลพื้นฐานของร้านค้าและการตั้งค่าต่างๆ

**ฟิลด์สำคัญ:**
- `merchant_id` - รหัสร้านค้าหลัก
- `merchant_name` - ชื่อร้านค้า
- `merchant_code` - รหัสร้านค้าที่ไม่ซ้ำ
- `api_key`, `secret_key` - สำหรับการเชื่อมต่อ API
- `deposit_mdr_rate` - อัตราค่าธรรมเนียมฝาก (Default: 1.50%)
- `withdraw_fee_amount` - ค่าธรรมเนียมถอน (Default: 10.00 THB)
- `callback_url` - URL สำหรับรับการแจ้งเตือน
- `is_auto_cancel_withdraw` - การยกเลิกถอนอัตโนมัติ
- `withdraw_pin_code` - PIN Code สำหรับถอนเงิน

### 2. ตาราง `merchant_balances` - ยอดเงินของร้านค้า
จัดเก็บยอดเงินแต่ละประเภทของร้านค้า

**ประเภทยอดเงิน:**
- `deposit_balance` - ยอดเงินฝาก (จากการรับชำระ)
- `withdraw_balance` - ยอดเงินถอน (พร้อมถอนได้)
- `frozen_balance` - ยอดเงินที่ถูกระงับ
- `wait_confirm_amount` - ยอดรอการยืนยัน

### 3. ตาราง `bank_accounts` - ข้อมูลบัญชีธนาคาร
จัดเก็บข้อมูลบัญชีธนาคารที่เชื่อมต่อกับร้านค้า

**ประเภทบัญชี:**
- `DEPOSIT` - บัญชีสำหรับรับเงินฝาก (สูงสุด 10 บัญชี)
- `WITHDRAW` - บัญชีสำหรับจ่ายเงินถอน (สูงสุด 10 บัญชี)
- `SAVINGS` - บัญชีสำหรับเก็บเงิน (สูงสุด 10 บัญชี)

**ฟิลด์สำคัญ:**
- `bank_api_endpoint`, `bank_api_token` - การเชื่อมต่อ API ธนาคาร
- `current_balance` - ยอดเงินปัจจุบัน
- `daily_limit` - วงเงินรายวัน
- `is_primary` - บัญชีหลักหรือไม่
- `priority_order` - ลำดับความสำคัญ

### 4. ตาราง `transactions` - รายการธุรกรรม
จัดเก็บรายการธุรกรรมทั้งหมดของระบบ

**ประเภทธุรกรรม:**
- `DEPOSIT` - การฝากเงิน
- `WITHDRAW` - การถอนเงิน
- `TOPUP` - การเติมเงิน
- `TRANSFER` - การโอนเงิน
- `SETTLEMENT` - การโอนเงินอัตโนมัติ

**สถานะธุรกรรม:**
- `PENDING` - รอดำเนินการ
- `SUCCESS` - สำเร็จ
- `FAILED` - ล้มเหลว
- `CANCELLED` - ยกเลิก
- `EXPIRED` - หมดอายุ

### 5. ตาราง `balance_logs` - ประวัติการเปลี่ยนแปลงยอดเงิน
บันทึกการเปลี่ยนแปลงยอดเงินทุกครั้งเพื่อการตรวจสอบ

### 6. ตาราง `blacklist` - รายการบัญชีที่ถูกบล็อก
จัดเก็บรายการที่ถูกบล็อกไม่ให้ทำธุรกรรม

**ประเภทการบล็อก:**
- `BANK_ACCOUNT` - บัญชีธนาคาร
- `PHONE` - เบอร์โทรศัพท์
- `IP_ADDRESS` - IP Address
- `EMAIL` - อีเมล

### 7. ตาราง `slip_uploads` - การอัปโหลดสลิปธนาคาร
จัดเก็บข้อมูลการอัปโหลดและประมวลผลสลิปธนาคาร

**ฟีเจอร์หลัก:**
- อัปโหลดรูปสลิป (JPG, PNG, PDF)
- ระบบ OCR สำหรับสแกนข้อมูลจากสลิป
- การจับคู่อัตโนมัติกับธุรกรรม
- การตรวจสอบความถูกต้อง
- ตรวจจับสลิปซ้ำ

**สถานะการตรวจสอบ:**
- `PENDING` - รอการตรวจสอบ
- `VERIFIED` - ตรวจสอบแล้ว
- `REJECTED` - ปฏิเสธ
- `DUPLICATE` - ซ้ำ

**วิธีการตรวจสอบ:**
- `AUTO` - ตรวจสอบอัตโนมัติ
- `MANUAL` - ตรวจสอบด้วยมือ
- `API` - ตรวจสอบผ่าน API

### 8. ตาราง `slip_verification_rules` - กฎการตรวจสอบสลิป
กำหนดกฎและเงื่อนไขการตรวจสอบสลิปอัตโนมัติ

**ประเภทกฎ:**
- `AMOUNT_MATCH` - ตรวจสอบจำนวนเงิน
- `TIME_WINDOW` - ตรวจสอบช่วงเวลา
- `ACCOUNT_MATCH` - ตรวจสอบบัญชี
- `DUPLICATE_CHECK` - ตรวจสอบความซ้ำ
- `CUSTOM` - กฎกำหนดเอง

### 9. ตาราง `slip_processing_logs` - บันทึกการประมวลผลสลิป
บันทึกขั้นตอนการประมวลผลสลิปแต่ละขั้นตอน

**ขั้นตอนการประมวลผล:**
- `UPLOAD` - อัปโหลดไฟล์
- `OCR` - สแกนข้อมูลด้วย OCR
- `ANALYSIS` - วิเคราะห์ข้อมูล
- `VERIFICATION` - ตรวจสอบความถูกต้อง
- `MATCHING` - จับคู่กับธุรกรรม
- `COMPLETION` - เสร็จสิ้น

## 👥 ระบบ Backoffice Database Schema

### 1. ตาราง `users` - ผู้ใช้งานระบบ
จัดเก็บข้อมูลผู้ใช้งานระบบ backoffice

**ฟิลด์สำคัญ:**
- `merchant_id` - รหัสร้านค้า (NULL = Super Admin)
- `user_group_id` - กลุ่มผู้ใช้งาน
- `is_google2fa_enabled` - เปิดใช้ Google 2FA
- `pin_code` - PIN Code สำหรับการยืนยัน
- `last_login_at` - เข้าสู่ระบบครั้งล่าสุด

### 2. ตาราง `user_groups` - กลุ่มผู้ใช้งาน
จัดเก็บข้อมูลกลุ่มผู้ใช้งานและสิทธิ์

### 3. ตาราง `permissions` - สิทธิ์การใช้งาน
กำหนดสิทธิ์การใช้งานต่างๆ ในระบบ

### 4. ตาราง `audit_logs` - บันทึกการตรวจสอบ
บันทึกการกระทำทั้งหมดในระบบเพื่อการตรวจสอบ

**ข้อมูลที่บันทึก:**
- การเข้าสู่ระบบ/ออกจากระบบ
- การแก้ไขข้อมูล
- การอนุมัติธุรกรรม
- การเปลี่ยนแปลงการตั้งค่า

### 5. ตาราง `api_logs` - บันทึกการเรียกใช้ API
บันทึกการเรียกใช้ API ทั้งหมดเพื่อการตรวจสอบและแก้ไขปัญหา

## 📊 ระบบการเทียบยอดธุรกรรม (Simplified Accounting)

### 1. ตาราง `bank_statements` - รายการเดินบัญชีธนาคาร
จัดเก็บรายการเดินบัญชีจากธนาคารเพื่อเทียบกับธุรกรรมในระบบ

**ประเภทรายการ:**
- `CREDIT` - เงินเข้า
- `DEBIT` - เงินออก

**สถานะการจับคู่:**
- `UNMATCHED` - ยังไม่จับคู่
- `MATCHED` - จับคู่แล้ว
- `PARTIAL` - จับคู่บางส่วน
- `DISPUTED` - มีข้อโต้แย้ง

**แหล่งที่มาข้อมูล:**
- `API` - จาก API ธนาคาร
- `FILE` - จากไฟล์ที่นำเข้า
- `MANUAL` - ป้อนด้วยมือ
- `SLIP` - จากการสแกนสลิป

### 2. ตาราง `transaction_reconciliation` - การกระทบยอดธุรกรรม
สรุปผลการกระทบยอดระหว่างธุรกรรมระบบกับรายการธนาคาร

**สถานะการกระทบยอด:**
- `BALANCED` - ยอดตรงกัน
- `UNBALANCED` - ยอดไม่ตรงกัน
- `INVESTIGATING` - กำลังตรวจสอบ
- `RESOLVED` - แก้ไขแล้ว

### 3. ตาราง `matching_rules` - กฎการจับคู่ธุรกรรม
กำหนดกฎการจับคู่ธุรกรรมอัตโนมัติ

**ประเภทกฎ:**
- `EXACT_AMOUNT` - จำนวนเงินตรงกัน
- `AMOUNT_RANGE` - จำนวนเงินในช่วง
- `TIME_WINDOW` - ช่วงเวลาที่กำหนด
- `REFERENCE_MATCH` - จับคู่จากรหัสอ้างอิง
- `CUSTOM` - กฎกำหนดเอง

### 4. ตาราง `reconciliation_exceptions` - ข้อยกเว้นการกระทบยอด
บันทึกรายการที่ไม่สามารถจับคู่ได้หรือมีปัญหา

**ประเภทข้อยกเว้น:**
- `UNMATCHED_TRANSACTION` - ธุรกรรมที่ไม่จับคู่
- `UNMATCHED_STATEMENT` - รายการธนาคารที่ไม่จับคู่
- `AMOUNT_DIFFERENCE` - จำนวนเงินไม่ตรงกัน
- `DUPLICATE` - รายการซ้ำ
- `OTHER` - อื่นๆ

## 📈 ระบบรายงาน

### 1. ตาราง `daily_reports` - รายงานรายวัน
สรุปข้อมูลธุรกรรมและยอดเงินรายวัน

### 2. ตาราง `revenue_reports` - รายงานรายได้
รายงานรายได้จากค่าธรรมเนียมแต่ละประเภท

### 3. ตาราง `bank_channel_reports` - รายงานช่องทางธนาคาร
รายงานประสิทธิภาพของแต่ละช่องทางธนาคาร

## 🔧 การตั้งค่าและการใช้งาน

### ข้อกำหนดสำคัญ:
1. **ใช้ `created_at` แทน `created_date`** - ตามมาตรฐานการตั้งชื่อ
2. **รองรับ UTF-8** - สำหรับภาษาไทย
3. **Double Entry Accounting** - ทุกธุรกรรมต้องมีรายการบัญชี
4. **Audit Trail** - บันทึกการเปลี่ยนแปลงทั้งหมด
5. **Balance Reconciliation** - ตรวจสอบยอดเงินทุกวัน

### การรักษาความปลอดภัย:
- เข้ารหัสรหัสผ่านด้วย Hash
- รองรับ Google 2FA
- บันทึก IP Address และ User Agent
- จำกัดการเข้าถึงด้วย IP Whitelist
- PIN Code สำหรับการยืนยันธุรกรรม

## 🚀 คำแนะนำการติดตั้งและใช้งาน

### 1. การสร้างฐานข้อมูล
```sql
-- สร้างฐานข้อมูล
CREATE DATABASE sugarpay_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- ใช้ฐานข้อมูล
USE sugarpay_db;

-- รันไฟล์ SQL ตามลำดับ
SOURCE sugarpay_merchant_database_schema.sql;
SOURCE sugarpay_backoffice_database_schema.sql;
SOURCE sugarpay_accounting_database_schema.sql;
SOURCE sugarpay_reporting_database_schema.sql;
```

### 2. การเพิ่มข้อมูลเริ่มต้น

#### ผังบัญชีพื้นฐาน:
```sql
-- บัญชีสินทรัพย์
INSERT INTO chart_of_accounts (account_code, account_name, account_type, normal_balance, is_system_account) VALUES
('1000', 'เงินสดและเงินฝากธนาคาร', 'ASSET', 'DEBIT', 1),
('1100', 'ลูกหนี้การค้า', 'ASSET', 'DEBIT', 1),
('1200', 'สินทรัพย์หมุนเวียนอื่น', 'ASSET', 'DEBIT', 1);

-- บัญชีหนี้สิน
INSERT INTO chart_of_accounts (account_code, account_name, account_type, normal_balance, is_system_account) VALUES
('2000', 'เจ้าหนี้การค้า', 'LIABILITY', 'CREDIT', 1),
('2100', 'หนี้สินหมุนเวียนอื่น', 'LIABILITY', 'CREDIT', 1);

-- บัญชีรายได้
INSERT INTO chart_of_accounts (account_code, account_name, account_type, normal_balance, is_system_account) VALUES
('4000', 'รายได้จากค่าธรรมเนียม', 'REVENUE', 'CREDIT', 1),
('4100', 'รายได้อื่น', 'REVENUE', 'CREDIT', 1);
```

#### สิทธิ์พื้นฐาน:
```sql
INSERT INTO permissions (permission_name, permission_description, permission_category, is_system_permission) VALUES
('merchant.view', 'ดูข้อมูลร้านค้า', 'MERCHANT', 1),
('merchant.edit', 'แก้ไขข้อมูลร้านค้า', 'MERCHANT', 1),
('transaction.view', 'ดูรายการธุรกรรม', 'TRANSACTION', 1),
('transaction.approve', 'อนุมัติธุรกรรม', 'TRANSACTION', 1),
('report.view', 'ดูรายงาน', 'REPORT', 1),
('report.export', 'ส่งออกรายงาน', 'REPORT', 1),
('user.manage', 'จัดการผู้ใช้งาน', 'USER', 1),
('system.admin', 'ผู้ดูแลระบบ', 'SYSTEM', 1);
```

### 3. ตัวอย่างการใช้งาน

#### การสร้างร้านค้าใหม่:
```sql
-- เพิ่มร้านค้า
INSERT INTO merchants (merchant_name, merchant_code, api_key, secret_key, email, phone) VALUES
('ร้านค้าทดสอบ', 'TEST001', 'api_key_here', 'secret_key_here', '<EMAIL>', '0812345678');

-- เพิ่มยอดเงินเริ่มต้น
INSERT INTO merchant_balances (merchant_id, deposit_balance, withdraw_balance, frozen_balance, wait_confirm_amount) VALUES
(1, 0.00, 0.00, 0.00, 0.00);
```

#### การบันทึกธุรกรรมฝากเงิน:
```sql
-- บันทึกธุรกรรม
INSERT INTO transactions (merchant_id, order_id, transaction_type, amount, mdr_amount, net_amount, status) VALUES
(1, 'ORD001', 'DEPOSIT', 1000.00, 15.00, 985.00, 'SUCCESS');

-- อัปเดตยอดเงิน
UPDATE merchant_balances SET deposit_balance = deposit_balance + 985.00 WHERE merchant_id = 1;

-- บันทึกประวัติการเปลี่ยนแปลงยอดเงิน
INSERT INTO balance_logs (merchant_id, transaction_id, balance_type, action_type, amount, balance_before, balance_after) VALUES
(1, 1, 'DEPOSIT', 'INCREASE', 985.00, 0.00, 985.00);
```

## 📋 Checklist การตรวจสอบระบบ

### ✅ การตรวจสอบรายวัน:
- [ ] ตรวจสอบยอดเงินของแต่ละร้านค้า
- [ ] ตรวจสอบการกระทบยอดธนาคาร
- [ ] ตรวจสอบธุรกรรมที่ค้างอยู่
- [ ] ตรวจสอบสลิปที่รอการตรวจสอบ
- [ ] ตรวจสอบ Error Logs
- [ ] สร้างรายงานรายวัน

### ✅ การตรวจสอบรายเดือน:
- [ ] สร้างรายงานรายได้
- [ ] ตรวจสอบการจับคู่ธุรกรรม
- [ ] สำรองข้อมูล
- [ ] ตรวจสอบประสิทธิภาพระบบ
- [ ] ทำความสะอาดข้อมูลเก่า

## 🔍 การแก้ไขปัญหาเบื้องต้น

### ปัญหายอดเงินไม่ตรงกัน:
1. ตรวจสอบ `balance_logs` เพื่อหาการเปลี่ยนแปลงล่าสุด
2. เปรียบเทียบกับ `transactions` ที่เกี่ยวข้อง
3. ตรวจสอบ `balance_reconciliation` ของวันที่เกิดปัญหา
4. รัน script การกระทบยอดใหม่

### ปัญหาธุรกรรมค้าง:
1. ตรวจสอบสถานะใน `transactions`
2. ตรวจสอบ `api_logs` สำหรับการเรียก API
3. ตรวจสอบ `error_logs` สำหรับข้อผิดพลาด
4. ตรวจสอบการเชื่อมต่อธนาคาร

### ปัญหาการเข้าสู่ระบบ:
1. ตรวจสอบ `user_sessions` สำหรับ session ที่ active
2. ตรวจสอบ `audit_logs` สำหรับการเข้าสู่ระบบล่าสุด
3. ตรวจสอบการตั้งค่า 2FA
4. ตรวจสอบ IP Whitelist

## 📞 การติดต่อและสนับสนุน

สำหรับข้อสงสัยเพิ่มเติมเกี่ยวกับการใช้งานระบบฐานข้อมูล SugarPay กรุณาติดต่อทีมพัฒนา

---
*เอกสารนี้อัปเดตล่าสุด: 2025-01-21*
