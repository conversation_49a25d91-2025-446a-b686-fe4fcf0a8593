-- =====================================================
-- SugarPay Backoffice Database Schema
-- ระบบฐานข้อมูลสำหรับ Backoffice Management System
-- =====================================================

-- ตาราง users - ผู้ใช้งานระบบ backoffice
CREATE TABLE users (
    user_id INT PRIMARY KEY AUTO_INCREMENT,
    merchant_id INT COMMENT 'รหัสร้านค้า (NULL = Super Admin)',
    user_group_id INT NOT NULL COMMENT 'รหัสกลุ่มผู้ใช้',
    
    -- ข้อมูลผู้ใช้
    username VARCHAR(100) UNIQUE NOT NULL COMMENT 'ชื่อผู้ใช้',
    email VARCHAR(255) UNIQUE COMMENT 'อีเมล',
    password_hash VARCHAR(255) NOT NULL COMMENT 'รหัสผ่านที่เข้ารหัส',
    
    -- ข้อมูลส่วนตัว
    first_name VARCHAR(100) COMMENT 'ชื่อจริง',
    last_name VARCHAR(100) COMMENT 'นามสกุล',
    phone VARCHAR(20) COMMENT 'เบอร์โทรศัพท์',
    
    -- การตั้งค่าความปลอดภัย
    is_google2fa_enabled TINYINT(1) DEFAULT 0 COMMENT 'เปิดใช้ Google 2FA หรือไม่',
    google2fa_secret VARCHAR(255) COMMENT 'Secret Key สำหรับ Google 2FA',
    pin_code VARCHAR(6) COMMENT 'PIN Code สำหรับการยืนยัน',
    
    -- สถานะและการตั้งค่า
    is_active TINYINT(1) DEFAULT 1 COMMENT 'เปิดใช้งานหรือไม่',
    is_deleted TINYINT(1) DEFAULT 0 COMMENT 'ลบหรือไม่',
    last_login_at TIMESTAMP NULL COMMENT 'เข้าสู่ระบบครั้งล่าสุด',
    last_login_ip VARCHAR(45) COMMENT 'IP ที่เข้าสู่ระบบครั้งล่าสุด',
    password_changed_at TIMESTAMP NULL COMMENT 'เปลี่ยนรหัสผ่านครั้งล่าสุด',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่อัปเดต',
    
    FOREIGN KEY (merchant_id) REFERENCES merchants(merchant_id) ON DELETE CASCADE,
    FOREIGN KEY (user_group_id) REFERENCES user_groups(user_group_id) ON DELETE RESTRICT,
    INDEX idx_username (username),
    INDEX idx_merchant_id (merchant_id),
    INDEX idx_user_group_id (user_group_id),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางผู้ใช้งานระบบ backoffice';

-- ตาราง user_groups - กลุ่มผู้ใช้งาน
CREATE TABLE user_groups (
    user_group_id INT PRIMARY KEY AUTO_INCREMENT,
    merchant_id INT COMMENT 'รหัสร้านค้า (NULL = ระดับระบบ)',
    
    -- ข้อมูลกลุ่ม
    group_name VARCHAR(100) NOT NULL COMMENT 'ชื่อกลุ่ม',
    group_description TEXT COMMENT 'รายละเอียดกลุ่ม',
    
    -- สถานะ
    is_active TINYINT(1) DEFAULT 1 COMMENT 'เปิดใช้งานหรือไม่',
    is_system_group TINYINT(1) DEFAULT 0 COMMENT 'กลุ่มระบบหรือไม่',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่อัปเดต',
    
    FOREIGN KEY (merchant_id) REFERENCES merchants(merchant_id) ON DELETE CASCADE,
    INDEX idx_merchant_group (merchant_id, group_name),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางกลุ่มผู้ใช้งาน';

-- ตาราง permissions - สิทธิ์การใช้งาน
CREATE TABLE permissions (
    permission_id INT PRIMARY KEY AUTO_INCREMENT,
    
    -- ข้อมูลสิทธิ์
    permission_name VARCHAR(100) UNIQUE NOT NULL COMMENT 'ชื่อสิทธิ์',
    permission_description TEXT COMMENT 'รายละเอียดสิทธิ์',
    permission_category VARCHAR(50) COMMENT 'หมวดหมู่สิทธิ์',
    
    -- การตั้งค่า
    is_system_permission TINYINT(1) DEFAULT 0 COMMENT 'สิทธิ์ระบบหรือไม่',
    is_active TINYINT(1) DEFAULT 1 COMMENT 'เปิดใช้งานหรือไม่',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่อัปเดต',
    
    INDEX idx_permission_name (permission_name),
    INDEX idx_permission_category (permission_category),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางสิทธิ์การใช้งาน';

-- ตาราง user_group_permissions - สิทธิ์ของกลุ่มผู้ใช้
CREATE TABLE user_group_permissions (
    group_permission_id INT PRIMARY KEY AUTO_INCREMENT,
    user_group_id INT NOT NULL COMMENT 'รหัสกลุ่มผู้ใช้',
    permission_id INT NOT NULL COMMENT 'รหัสสิทธิ์',
    
    -- การตั้งค่า
    is_granted TINYINT(1) DEFAULT 1 COMMENT 'อนุญาตหรือไม่',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่อัปเดต',
    
    FOREIGN KEY (user_group_id) REFERENCES user_groups(user_group_id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(permission_id) ON DELETE CASCADE,
    UNIQUE KEY unique_group_permission (user_group_id, permission_id),
    INDEX idx_user_group_id (user_group_id),
    INDEX idx_permission_id (permission_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางสิทธิ์ของกลุ่มผู้ใช้';

-- ตาราง user_sessions - เซสชันผู้ใช้งาน
CREATE TABLE user_sessions (
    session_id VARCHAR(128) PRIMARY KEY COMMENT 'รหัสเซสชัน',
    user_id INT NOT NULL COMMENT 'รหัสผู้ใช้',
    
    -- ข้อมูลเซสชัน
    ip_address VARCHAR(45) COMMENT 'IP Address',
    user_agent TEXT COMMENT 'User Agent',
    device_info JSON COMMENT 'ข้อมูลอุปกรณ์',
    
    -- สถานะ
    is_active TINYINT(1) DEFAULT 1 COMMENT 'เปิดใช้งานหรือไม่',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'กิจกรรมล่าสุด',
    expires_at TIMESTAMP NOT NULL COMMENT 'วันที่หมดอายุ',
    
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางเซสชันผู้ใช้งาน';

-- ตาราง audit_logs - บันทึกการตรวจสอบ
CREATE TABLE audit_logs (
    audit_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT COMMENT 'รหัสผู้ใช้ที่ทำการกระทำ',
    merchant_id INT COMMENT 'รหัสร้านค้าที่เกี่ยวข้อง',
    
    -- ข้อมูลการกระทำ
    action_type VARCHAR(50) NOT NULL COMMENT 'ประเภทการกระทำ',
    table_name VARCHAR(100) COMMENT 'ชื่อตารางที่ถูกกระทำ',
    record_id INT COMMENT 'รหัสเรคอร์ดที่ถูกกระทำ',
    
    -- รายละเอียด
    action_description TEXT COMMENT 'รายละเอียดการกระทำ',
    old_values JSON COMMENT 'ค่าเดิมก่อนเปลี่ยนแปลง',
    new_values JSON COMMENT 'ค่าใหม่หลังเปลี่ยนแปลง',
    
    -- ข้อมูลเพิ่มเติม
    ip_address VARCHAR(45) COMMENT 'IP Address',
    user_agent TEXT COMMENT 'User Agent',
    request_data JSON COMMENT 'ข้อมูล Request',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL,
    FOREIGN KEY (merchant_id) REFERENCES merchants(merchant_id) ON DELETE CASCADE,
    INDEX idx_user_action (user_id, action_type),
    INDEX idx_merchant_action (merchant_id, action_type),
    INDEX idx_table_record (table_name, record_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางบันทึกการตรวจสอบ';

-- ตาราง system_settings - การตั้งค่าระบบ
CREATE TABLE system_settings (
    setting_id INT PRIMARY KEY AUTO_INCREMENT,
    merchant_id INT COMMENT 'รหัสร้านค้า (NULL = ระดับระบบ)',
    
    -- ข้อมูลการตั้งค่า
    setting_key VARCHAR(100) NOT NULL COMMENT 'คีย์การตั้งค่า',
    setting_value TEXT COMMENT 'ค่าการตั้งค่า',
    setting_type ENUM('STRING', 'INTEGER', 'DECIMAL', 'BOOLEAN', 'JSON') DEFAULT 'STRING' COMMENT 'ประเภทข้อมูล',
    
    -- รายละเอียด
    setting_description TEXT COMMENT 'รายละเอียดการตั้งค่า',
    setting_category VARCHAR(50) COMMENT 'หมวดหมู่การตั้งค่า',
    
    -- การตั้งค่า
    is_system_setting TINYINT(1) DEFAULT 0 COMMENT 'การตั้งค่าระบบหรือไม่',
    is_editable TINYINT(1) DEFAULT 1 COMMENT 'แก้ไขได้หรือไม่',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่อัปเดต',
    
    FOREIGN KEY (merchant_id) REFERENCES merchants(merchant_id) ON DELETE CASCADE,
    UNIQUE KEY unique_merchant_setting (merchant_id, setting_key),
    INDEX idx_setting_key (setting_key),
    INDEX idx_setting_category (setting_category)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางการตั้งค่าระบบ';

-- ตาราง notifications - การแจ้งเตือน
CREATE TABLE notifications (
    notification_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT COMMENT 'รหัสผู้ใช้ (NULL = ทุกคน)',
    merchant_id INT COMMENT 'รหัสร้านค้า (NULL = ทุกร้าน)',

    -- ข้อมูลการแจ้งเตือน
    notification_type ENUM('INFO', 'WARNING', 'ERROR', 'SUCCESS') DEFAULT 'INFO' COMMENT 'ประเภทการแจ้งเตือน',
    title VARCHAR(255) NOT NULL COMMENT 'หัวข้อ',
    message TEXT NOT NULL COMMENT 'ข้อความ',

    -- ข้อมูลเพิ่มเติม
    action_url VARCHAR(500) COMMENT 'URL สำหรับการกระทำ',
    action_text VARCHAR(100) COMMENT 'ข้อความปุ่มกระทำ',
    metadata JSON COMMENT 'ข้อมูลเพิ่มเติม',

    -- สถานะ
    is_read TINYINT(1) DEFAULT 0 COMMENT 'อ่านแล้วหรือไม่',
    is_active TINYINT(1) DEFAULT 1 COMMENT 'เปิดใช้งานหรือไม่',

    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    read_at TIMESTAMP NULL COMMENT 'วันที่อ่าน',
    expires_at TIMESTAMP NULL COMMENT 'วันที่หมดอายุ',

    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (merchant_id) REFERENCES merchants(merchant_id) ON DELETE CASCADE,
    INDEX idx_user_unread (user_id, is_read),
    INDEX idx_merchant_active (merchant_id, is_active),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางการแจ้งเตือน';

-- ตาราง api_logs - บันทึกการเรียกใช้ API
CREATE TABLE api_logs (
    log_id INT PRIMARY KEY AUTO_INCREMENT,
    merchant_id INT COMMENT 'รหัสร้านค้า',

    -- ข้อมูล Request
    request_method VARCHAR(10) NOT NULL COMMENT 'HTTP Method',
    request_url VARCHAR(500) NOT NULL COMMENT 'URL ที่เรียก',
    request_headers JSON COMMENT 'Headers ของ Request',
    request_body TEXT COMMENT 'Body ของ Request',

    -- ข้อมูล Response
    response_status INT COMMENT 'HTTP Status Code',
    response_headers JSON COMMENT 'Headers ของ Response',
    response_body TEXT COMMENT 'Body ของ Response',
    response_time_ms INT COMMENT 'เวลาตอบสนอง (มิลลิวินาที)',

    -- ข้อมูลเพิ่มเติม
    ip_address VARCHAR(45) COMMENT 'IP Address',
    user_agent TEXT COMMENT 'User Agent',
    api_key VARCHAR(255) COMMENT 'API Key ที่ใช้',

    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',

    FOREIGN KEY (merchant_id) REFERENCES merchants(merchant_id) ON DELETE CASCADE,
    INDEX idx_merchant_method (merchant_id, request_method),
    INDEX idx_response_status (response_status),
    INDEX idx_created_at (created_at),
    INDEX idx_api_key (api_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางบันทึกการเรียกใช้ API';

-- ตาราง error_logs - บันทึกข้อผิดพลาด
CREATE TABLE error_logs (
    error_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT COMMENT 'รหัสผู้ใช้',
    merchant_id INT COMMENT 'รหัสร้านค้า',

    -- ข้อมูลข้อผิดพลาด
    error_type VARCHAR(100) NOT NULL COMMENT 'ประเภทข้อผิดพลาด',
    error_message TEXT NOT NULL COMMENT 'ข้อความข้อผิดพลาด',
    error_code VARCHAR(50) COMMENT 'รหัสข้อผิดพลาด',

    -- ข้อมูลเพิ่มเติม
    file_path VARCHAR(500) COMMENT 'ไฟล์ที่เกิดข้อผิดพลาด',
    line_number INT COMMENT 'บรรทัดที่เกิดข้อผิดพลาด',
    stack_trace TEXT COMMENT 'Stack Trace',
    request_data JSON COMMENT 'ข้อมูล Request',

    -- ข้อมูลสภาพแวดล้อม
    ip_address VARCHAR(45) COMMENT 'IP Address',
    user_agent TEXT COMMENT 'User Agent',

    -- สถานะ
    is_resolved TINYINT(1) DEFAULT 0 COMMENT 'แก้ไขแล้วหรือไม่',
    resolved_by INT COMMENT 'ผู้แก้ไข',
    resolved_at TIMESTAMP NULL COMMENT 'วันที่แก้ไข',

    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',

    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL,
    FOREIGN KEY (merchant_id) REFERENCES merchants(merchant_id) ON DELETE CASCADE,
    FOREIGN KEY (resolved_by) REFERENCES users(user_id) ON DELETE SET NULL,
    INDEX idx_error_type (error_type),
    INDEX idx_merchant_error (merchant_id, error_type),
    INDEX idx_is_resolved (is_resolved),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางบันทึกข้อผิดพลาด';
