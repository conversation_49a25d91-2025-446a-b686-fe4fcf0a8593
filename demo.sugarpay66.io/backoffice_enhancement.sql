-- =====================================================
-- SugarPay Backoffice Enhancement
-- เพิ่มเติม Features สำหรับ Backoffice Management
-- =====================================================

-- 1. ตาราง admin_settings - การตั้งค่าระบบ
CREATE TABLE `admin_settings` (
  `setting_id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL UNIQUE,
  `setting_value` text NOT NULL,
  `setting_type` enum('string','number','boolean','json') DEFAULT 'string',
  `description` varchar(500) DEFAULT NULL,
  `is_editable` tinyint(1) DEFAULT 1,
  `created_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_date` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`setting_id`),
  UNIQUE KEY `uk_setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. ตาราง admin_logs - บันทึกการทำงานของ Admin
CREATE TABLE `admin_logs` (
  `log_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `action` varchar(100) NOT NULL,
  `target_type` varchar(50) DEFAULT NULL COMMENT 'merchant, transaction, user, etc.',
  `target_id` int(11) DEFAULT NULL,
  `old_values` json DEFAULT NULL,
  `new_values` json DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`log_id`),
  KEY `idx_admin_logs_user` (`user_id`, `created_date`),
  KEY `idx_admin_logs_action` (`action`, `created_date`),
  KEY `idx_admin_logs_target` (`target_type`, `target_id`),
  CONSTRAINT `fk_admin_logs_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. ตาราง notifications - ระบบแจ้งเตือน
CREATE TABLE `notifications` (
  `notification_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `agent_id` int(11) DEFAULT NULL,
  `merchant_id` int(11) DEFAULT NULL,
  `notification_type` enum('info','warning','error','success') DEFAULT 'info',
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `action_url` varchar(500) DEFAULT NULL,
  `is_read` tinyint(1) DEFAULT 0,
  `is_system` tinyint(1) DEFAULT 0,
  `priority` enum('low','normal','high','urgent') DEFAULT 'normal',
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  `read_date` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`notification_id`),
  KEY `idx_notifications_user` (`user_id`, `is_read`, `created_date`),
  KEY `idx_notifications_agent` (`agent_id`, `is_read`),
  KEY `idx_notifications_merchant` (`merchant_id`, `is_read`),
  CONSTRAINT `fk_notifications_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`),
  CONSTRAINT `fk_notifications_agent` FOREIGN KEY (`agent_id`) REFERENCES `agents` (`agent_id`),
  CONSTRAINT `fk_notifications_merchant` FOREIGN KEY (`merchant_id`) REFERENCES `merchants` (`merchant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. ตาราง system_maintenance - การบำรุงรักษาระบบ
CREATE TABLE `system_maintenance` (
  `maintenance_id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `maintenance_type` enum('scheduled','emergency','update','backup') DEFAULT 'scheduled',
  `start_time` timestamp NOT NULL,
  `end_time` timestamp NOT NULL,
  `is_active` tinyint(1) DEFAULT 0,
  `affected_services` json DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_date` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`maintenance_id`),
  KEY `idx_maintenance_time` (`start_time`, `end_time`, `is_active`),
  CONSTRAINT `fk_system_maintenance_user` FOREIGN KEY (`created_by`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 5. ตาราง transaction_approvals - การอนุมัติธุรกรรม
CREATE TABLE `transaction_approvals` (
  `approval_id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_id` int(11) NOT NULL,
  `approval_type` enum('withdraw','settlement','manual_adjustment','refund') NOT NULL,
  `requested_by` int(11) NOT NULL,
  `approved_by` int(11) DEFAULT NULL,
  `approval_status` enum('pending','approved','rejected','cancelled') DEFAULT 'pending',
  `request_reason` text DEFAULT NULL,
  `approval_reason` text DEFAULT NULL,
  `approval_level` int(2) DEFAULT 1,
  `required_level` int(2) DEFAULT 1,
  `requested_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  `approved_date` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`approval_id`),
  KEY `idx_approval_transaction` (`transaction_id`),
  KEY `idx_approval_status` (`approval_status`, `requested_date`),
  KEY `idx_approval_user` (`requested_by`),
  CONSTRAINT `fk_transaction_approvals_transaction` FOREIGN KEY (`transaction_id`) REFERENCES `transactions` (`transaction_id`),
  CONSTRAINT `fk_transaction_approvals_requester` FOREIGN KEY (`requested_by`) REFERENCES `users` (`user_id`),
  CONSTRAINT `fk_transaction_approvals_approver` FOREIGN KEY (`approved_by`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 6. ตาราง merchant_limits - ขีดจำกัดของร้านค้า
CREATE TABLE `merchant_limits` (
  `limit_id` int(11) NOT NULL AUTO_INCREMENT,
  `merchant_id` int(11) NOT NULL,
  `limit_type` enum('daily_deposit','daily_withdraw','monthly_deposit','monthly_withdraw','single_transaction') NOT NULL,
  `limit_amount` decimal(15,2) NOT NULL,
  `current_usage` decimal(15,2) DEFAULT 0.00,
  `reset_period` enum('daily','weekly','monthly','yearly') DEFAULT 'daily',
  `last_reset` timestamp DEFAULT CURRENT_TIMESTAMP,
  `is_active` tinyint(1) DEFAULT 1,
  `created_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_date` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`limit_id`),
  UNIQUE KEY `uk_merchant_limit_type` (`merchant_id`, `limit_type`),
  CONSTRAINT `fk_merchant_limits_merchant` FOREIGN KEY (`merchant_id`) REFERENCES `merchants` (`merchant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 7. ตาราง bank_reconciliation - การกระทบยอดธนาคาร
CREATE TABLE `bank_reconciliation` (
  `reconciliation_id` int(11) NOT NULL AUTO_INCREMENT,
  `bank_account_id` int(11) NOT NULL,
  `reconciliation_date` date NOT NULL,
  `system_balance` decimal(15,2) NOT NULL,
  `bank_statement_balance` decimal(15,2) NOT NULL,
  `difference_amount` decimal(15,2) GENERATED ALWAYS AS (`system_balance` - `bank_statement_balance`) STORED,
  `reconciliation_status` enum('pending','matched','unmatched','investigating','resolved') DEFAULT 'pending',
  `notes` text DEFAULT NULL,
  `reconciled_by` int(11) DEFAULT NULL,
  `reconciled_date` timestamp NULL DEFAULT NULL,
  `created_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`reconciliation_id`),
  UNIQUE KEY `uk_bank_reconciliation_date` (`bank_account_id`, `reconciliation_date`),
  KEY `idx_reconciliation_status` (`reconciliation_status`, `reconciliation_date`),
  CONSTRAINT `fk_bank_reconciliation_bank` FOREIGN KEY (`bank_account_id`) REFERENCES `bank_accounts` (`bank_account_id`),
  CONSTRAINT `fk_bank_reconciliation_user` FOREIGN KEY (`reconciled_by`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 8. ตาราง report_schedules - การจัดการรายงานอัตโนมัติ
CREATE TABLE `report_schedules` (
  `schedule_id` int(11) NOT NULL AUTO_INCREMENT,
  `report_name` varchar(255) NOT NULL,
  `report_type` enum('daily_summary','weekly_summary','monthly_summary','transaction_detail','balance_report','fee_report') NOT NULL,
  `schedule_frequency` enum('daily','weekly','monthly','quarterly','yearly') NOT NULL,
  `schedule_time` time DEFAULT '09:00:00',
  `schedule_day` int(2) DEFAULT NULL COMMENT 'Day of month for monthly, day of week for weekly',
  `recipients` json NOT NULL COMMENT 'Email addresses',
  `filters` json DEFAULT NULL COMMENT 'Report filters',
  `is_active` tinyint(1) DEFAULT 1,
  `last_run` timestamp NULL DEFAULT NULL,
  `next_run` timestamp NULL DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_date` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`schedule_id`),
  KEY `idx_report_schedule_next_run` (`is_active`, `next_run`),
  CONSTRAINT `fk_report_schedules_user` FOREIGN KEY (`created_by`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- BACKOFFICE VIEWS
-- =====================================================

-- View สำหรับ Dashboard Overview
CREATE VIEW `v_backoffice_dashboard` AS
SELECT 
    (SELECT COUNT(*) FROM merchants WHERE status = 'active') as active_merchants,
    (SELECT COUNT(*) FROM transactions WHERE DATE(created_date) = CURDATE()) as today_transactions,
    (SELECT COALESCE(SUM(txn_amount), 0) FROM transactions WHERE DATE(created_date) = CURDATE() AND txn_status = 'SUCCESS') as today_volume,
    (SELECT COALESCE(SUM(mdr_amount + withdraw_fee_amount), 0) FROM transactions WHERE DATE(created_date) = CURDATE() AND txn_status = 'SUCCESS') as today_revenue,
    (SELECT COUNT(*) FROM transactions WHERE txn_status = 'PENDING') as pending_transactions,
    (SELECT COUNT(*) FROM transaction_approvals WHERE approval_status = 'pending') as pending_approvals,
    (SELECT COUNT(*) FROM notifications WHERE is_read = 0 AND is_system = 1) as unread_notifications;

-- View สำหรับ Transaction Monitoring
CREATE VIEW `v_transaction_monitoring` AS
SELECT 
    t.transaction_id,
    t.txn_hash,
    t.order_id,
    t.transaction_type,
    t.txn_amount,
    t.mdr_amount,
    t.withdraw_fee_amount,
    t.net_amount,
    t.txn_status,
    t.txn_date,
    t.created_date,
    m.merchant_code,
    m.merchant_name,
    a.agent_code,
    ba.bank_name,
    ba.bank_acc_no,
    CASE 
        WHEN t.txn_status = 'PENDING' AND t.created_date < DATE_SUB(NOW(), INTERVAL 30 MINUTE) THEN 'DELAYED'
        WHEN t.txn_status = 'FAILED' THEN 'FAILED'
        WHEN t.txn_status = 'SUCCESS' THEN 'SUCCESS'
        ELSE t.txn_status
    END as monitoring_status,
    TIMESTAMPDIFF(MINUTE, t.created_date, COALESCE(t.txn_date, NOW())) as processing_time_minutes
FROM transactions t
JOIN merchants m ON t.merchant_id = m.merchant_id
JOIN agents a ON t.agent_id = a.agent_id
LEFT JOIN bank_accounts ba ON t.bank_account_id = ba.bank_account_id
WHERE t.created_date >= DATE_SUB(NOW(), INTERVAL 24 HOUR);

-- View สำหรับ User Activity Monitoring
CREATE VIEW `v_user_activity_monitoring` AS
SELECT 
    u.user_id,
    u.username,
    u.name,
    u.user_type,
    u.last_login,
    u.login_attempts,
    u.is_locked,
    CASE 
        WHEN u.last_login IS NULL THEN 'NEVER_LOGGED_IN'
        WHEN u.last_login < DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 'INACTIVE'
        WHEN u.last_login < DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 'DORMANT'
        WHEN u.login_attempts >= 3 THEN 'SUSPICIOUS'
        ELSE 'ACTIVE'
    END as activity_status,
    (SELECT COUNT(*) FROM admin_logs WHERE user_id = u.user_id AND created_date >= DATE_SUB(NOW(), INTERVAL 24 HOUR)) as actions_today,
    a.agent_name,
    m.merchant_name
FROM users u
LEFT JOIN agents a ON u.agent_id = a.agent_id
LEFT JOIN merchants m ON u.merchant_id = m.merchant_id
WHERE u.is_delete = 0;

-- =====================================================
-- BACKOFFICE STORED PROCEDURES
-- =====================================================

DELIMITER //

-- Procedure สำหรับสร้าง Notification
CREATE PROCEDURE `sp_create_notification`(
    IN p_user_id INT,
    IN p_agent_id INT,
    IN p_merchant_id INT,
    IN p_type ENUM('info','warning','error','success'),
    IN p_title VARCHAR(255),
    IN p_message TEXT,
    IN p_action_url VARCHAR(500),
    IN p_priority ENUM('low','normal','high','urgent')
)
BEGIN
    INSERT INTO notifications (
        user_id, agent_id, merchant_id, notification_type, 
        title, message, action_url, priority
    ) VALUES (
        p_user_id, p_agent_id, p_merchant_id, p_type,
        p_title, p_message, p_action_url, p_priority
    );
END //

-- Procedure สำหรับบันทึก Admin Action
CREATE PROCEDURE `sp_log_admin_action`(
    IN p_user_id INT,
    IN p_action VARCHAR(100),
    IN p_target_type VARCHAR(50),
    IN p_target_id INT,
    IN p_old_values JSON,
    IN p_new_values JSON,
    IN p_ip_address VARCHAR(45),
    IN p_user_agent TEXT
)
BEGIN
    INSERT INTO admin_logs (
        user_id, action, target_type, target_id,
        old_values, new_values, ip_address, user_agent
    ) VALUES (
        p_user_id, p_action, p_target_type, p_target_id,
        p_old_values, p_new_values, p_ip_address, p_user_agent
    );
END //

DELIMITER ;

-- =====================================================
-- DEFAULT ADMIN SETTINGS
-- =====================================================

INSERT INTO `admin_settings` (`setting_key`, `setting_value`, `setting_type`, `description`) VALUES
('system_maintenance_mode', 'false', 'boolean', 'Enable/Disable system maintenance mode'),
('max_login_attempts', '5', 'number', 'Maximum login attempts before account lock'),
('session_timeout_minutes', '30', 'number', 'Session timeout in minutes'),
('auto_settlement_enabled', 'true', 'boolean', 'Enable automatic settlement'),
('auto_settlement_time', '23:00', 'string', 'Daily auto settlement time'),
('notification_email_enabled', 'true', 'boolean', 'Enable email notifications'),
('sms_notification_enabled', 'false', 'boolean', 'Enable SMS notifications'),
('transaction_approval_required_amount', '50000', 'number', 'Amount requiring approval'),
('daily_transaction_limit', '1000000', 'number', 'Daily transaction limit per merchant'),
('backup_retention_days', '90', 'number', 'Number of days to retain backups');

-- =====================================================
-- INDEXES FOR BACKOFFICE PERFORMANCE
-- =====================================================

CREATE INDEX `idx_admin_logs_comprehensive` ON `admin_logs` (`user_id`, `action`, `created_date`);
CREATE INDEX `idx_notifications_comprehensive` ON `notifications` (`user_id`, `is_read`, `priority`, `created_date`);
CREATE INDEX `idx_transaction_approvals_status` ON `transaction_approvals` (`approval_status`, `requested_date`);
CREATE INDEX `idx_merchant_limits_usage` ON `merchant_limits` (`merchant_id`, `limit_type`, `is_active`);
CREATE INDEX `idx_bank_reconciliation_status` ON `bank_reconciliation` (`reconciliation_status`, `reconciliation_date`);
