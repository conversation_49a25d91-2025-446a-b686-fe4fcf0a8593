-- =====================================================
-- SugarPay Double Entry Accounting Database Schema
-- ระบบฐานข้อมูลสำหรับ Double Entry Accounting System
-- =====================================================

-- ตาราง chart_of_accounts - ผังบัญชี
CREATE TABLE chart_of_accounts (
    account_id INT PRIMARY KEY AUTO_INCREMENT,
    
    -- ข้อมูลบัญชี
    account_code VARCHAR(20) UNIQUE NOT NULL COMMENT 'รหัสบัญชี',
    account_name VARCHAR(255) NOT NULL COMMENT 'ชื่อบัญชี',
    account_name_en VARCHAR(255) COMMENT 'ชื่อบัญชีภาษาอังกฤษ',
    
    -- ประเภทบัญชี
    account_type ENUM('ASSET', 'LIABILITY', 'EQUITY', 'REVENUE', 'EXPENSE') NOT NULL COMMENT 'ประเภทบัญชี',
    account_subtype VARCHAR(50) COMMENT 'ประเภทย่อย',
    
    -- โครงสร้างบัญชี
    parent_account_id INT COMMENT 'บัญชีแม่',
    account_level INT DEFAULT 1 COMMENT 'ระดับบัญชี',
    is_header TINYINT(1) DEFAULT 0 COMMENT 'เป็นหัวข้อหรือไม่',
    
    -- การตั้งค่า
    normal_balance ENUM('DEBIT', 'CREDIT') NOT NULL COMMENT 'ยอดปกติ (เดบิต/เครดิต)',
    is_active TINYINT(1) DEFAULT 1 COMMENT 'เปิดใช้งานหรือไม่',
    is_system_account TINYINT(1) DEFAULT 0 COMMENT 'บัญชีระบบหรือไม่',
    
    -- รายละเอียด
    description TEXT COMMENT 'รายละเอียดบัญชี',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่อัปเดต',
    
    FOREIGN KEY (parent_account_id) REFERENCES chart_of_accounts(account_id) ON DELETE SET NULL,
    INDEX idx_account_code (account_code),
    INDEX idx_account_type (account_type),
    INDEX idx_parent_account (parent_account_id),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางผังบัญชี';

-- ตาราง journal_entries - รายการสมุดรายวันทั่วไป
CREATE TABLE journal_entries (
    journal_entry_id INT PRIMARY KEY AUTO_INCREMENT,
    merchant_id INT COMMENT 'รหัสร้านค้า',
    transaction_id INT COMMENT 'รหัสธุรกรรมที่เกี่ยวข้อง',
    
    -- ข้อมูลรายการ
    entry_number VARCHAR(50) UNIQUE NOT NULL COMMENT 'เลขที่รายการ',
    entry_date DATE NOT NULL COMMENT 'วันที่รายการ',
    entry_type ENUM('MANUAL', 'AUTO_DEPOSIT', 'AUTO_WITHDRAW', 'AUTO_TRANSFER', 'AUTO_FEE', 'AUTO_SETTLEMENT') DEFAULT 'MANUAL' COMMENT 'ประเภทรายการ',
    
    -- รายละเอียด
    description TEXT NOT NULL COMMENT 'รายละเอียดรายการ',
    reference_number VARCHAR(100) COMMENT 'เลขที่อ้างอิง',
    
    -- ยอดรวม
    total_debit DECIMAL(15,2) NOT NULL DEFAULT 0.00 COMMENT 'ยอดรวมเดบิต',
    total_credit DECIMAL(15,2) NOT NULL DEFAULT 0.00 COMMENT 'ยอดรวมเครดิต',
    
    -- สถานะ
    status ENUM('DRAFT', 'POSTED', 'CANCELLED') DEFAULT 'DRAFT' COMMENT 'สถานะรายการ',
    posted_by INT COMMENT 'ผู้โพสต์รายการ',
    posted_at TIMESTAMP NULL COMMENT 'วันที่โพสต์',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่อัปเดต',
    
    FOREIGN KEY (merchant_id) REFERENCES merchants(merchant_id) ON DELETE CASCADE,
    FOREIGN KEY (transaction_id) REFERENCES transactions(transaction_id) ON DELETE SET NULL,
    FOREIGN KEY (posted_by) REFERENCES users(user_id) ON DELETE SET NULL,
    INDEX idx_entry_number (entry_number),
    INDEX idx_merchant_date (merchant_id, entry_date),
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_status (status),
    INDEX idx_entry_date (entry_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางรายการสมุดรายวันทั่วไป';

-- ตาราง journal_entry_lines - รายละเอียดรายการสมุดรายวันทั่วไป
CREATE TABLE journal_entry_lines (
    line_id INT PRIMARY KEY AUTO_INCREMENT,
    journal_entry_id INT NOT NULL COMMENT 'รหัสรายการสมุดรายวันทั่วไป',
    account_id INT NOT NULL COMMENT 'รหัสบัญชี',
    
    -- ข้อมูลรายการ
    line_number INT NOT NULL COMMENT 'ลำดับรายการ',
    description TEXT COMMENT 'รายละเอียดรายการ',
    
    -- จำนวนเงิน
    debit_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT 'จำนวนเงินเดบิต',
    credit_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT 'จำนวนเงินเครดิต',
    
    -- ข้อมูลเพิ่มเติม
    reference_data JSON COMMENT 'ข้อมูลอ้างอิงเพิ่มเติม',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    
    FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(journal_entry_id) ON DELETE CASCADE,
    FOREIGN KEY (account_id) REFERENCES chart_of_accounts(account_id) ON DELETE RESTRICT,
    INDEX idx_journal_entry_id (journal_entry_id),
    INDEX idx_account_id (account_id),
    INDEX idx_line_number (journal_entry_id, line_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางรายละเอียดรายการสมุดรายวันทั่วไป';

-- ตาราง account_balances - ยอดคงเหลือบัญชี
CREATE TABLE account_balances (
    balance_id INT PRIMARY KEY AUTO_INCREMENT,
    account_id INT NOT NULL COMMENT 'รหัสบัญชี',
    merchant_id INT COMMENT 'รหัสร้านค้า (NULL = ระดับระบบ)',
    
    -- ยอดคงเหลือ
    balance_date DATE NOT NULL COMMENT 'วันที่ยอดคงเหลือ',
    opening_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดยกมา',
    debit_total DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดรวมเดบิต',
    credit_total DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดรวมเครดิต',
    closing_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดคงเหลือ',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่อัปเดต',
    
    FOREIGN KEY (account_id) REFERENCES chart_of_accounts(account_id) ON DELETE CASCADE,
    FOREIGN KEY (merchant_id) REFERENCES merchants(merchant_id) ON DELETE CASCADE,
    UNIQUE KEY unique_account_merchant_date (account_id, merchant_id, balance_date),
    INDEX idx_account_date (account_id, balance_date),
    INDEX idx_merchant_date (merchant_id, balance_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางยอดคงเหลือบัญชี';

-- ตาราง balance_reconciliation - การกระทบยอด
CREATE TABLE balance_reconciliation (
    reconciliation_id INT PRIMARY KEY AUTO_INCREMENT,
    merchant_id INT NOT NULL COMMENT 'รหัสร้านค้า',
    
    -- ข้อมูลการกระทบยอด
    reconciliation_date DATE NOT NULL COMMENT 'วันที่กระทบยอด',
    reconciliation_type ENUM('DAILY', 'MONTHLY', 'MANUAL') DEFAULT 'DAILY' COMMENT 'ประเภทการกระทบยอด',
    
    -- ยอดเงินจากระบบ
    system_deposit_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดฝากจากระบบ',
    system_withdraw_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดถอนจากระบบ',
    system_frozen_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดระงับจากระบบ',
    
    -- ยอดเงินจากบัญชี
    accounting_deposit_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดฝากจากบัญชี',
    accounting_withdraw_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดถอนจากบัญชี',
    accounting_frozen_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดระงับจากบัญชี',
    
    -- ผลต่าง
    deposit_difference DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ผลต่างยอดฝาก',
    withdraw_difference DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ผลต่างยอดถอน',
    frozen_difference DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ผลต่างยอดระงับ',
    
    -- สถานะ
    status ENUM('BALANCED', 'UNBALANCED', 'INVESTIGATING') DEFAULT 'BALANCED' COMMENT 'สถานะการกระทบยอด',
    notes TEXT COMMENT 'หมายเหตุ',
    
    -- ผู้ทำการกระทบยอด
    reconciled_by INT COMMENT 'ผู้ทำการกระทบยอด',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่อัปเดต',
    
    FOREIGN KEY (merchant_id) REFERENCES merchants(merchant_id) ON DELETE CASCADE,
    FOREIGN KEY (reconciled_by) REFERENCES users(user_id) ON DELETE SET NULL,
    UNIQUE KEY unique_merchant_date_type (merchant_id, reconciliation_date, reconciliation_type),
    INDEX idx_merchant_date (merchant_id, reconciliation_date),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางการกระทบยอด';

-- ตาราง fee_calculations - การคำนวณค่าธรรมเนียม
CREATE TABLE fee_calculations (
    fee_calculation_id INT PRIMARY KEY AUTO_INCREMENT,
    transaction_id INT NOT NULL COMMENT 'รหัสธุรกรรม',
    merchant_id INT NOT NULL COMMENT 'รหัสร้านค้า',

    -- ข้อมูลการคำนวณ
    calculation_type ENUM('DEPOSIT_MDR', 'WITHDRAW_FEE', 'TOPUP_MDR', 'SETTLEMENT_FEE', 'TRANSFER_FEE') NOT NULL COMMENT 'ประเภทการคำนวณ',
    base_amount DECIMAL(15,2) NOT NULL COMMENT 'จำนวนเงินฐาน',

    -- อัตราและค่าธรรมเนียม
    fee_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT 'อัตราค่าธรรมเนียม (%)',
    fixed_fee DECIMAL(10,2) DEFAULT 0.00 COMMENT 'ค่าธรรมเนียมคงที่',
    calculated_fee DECIMAL(15,2) NOT NULL COMMENT 'ค่าธรรมเนียมที่คำนวณได้',

    -- ข้อมูลเพิ่มเติม
    calculation_formula TEXT COMMENT 'สูตรการคำนวณ',
    calculation_details JSON COMMENT 'รายละเอียดการคำนวณ',

    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',

    FOREIGN KEY (transaction_id) REFERENCES transactions(transaction_id) ON DELETE CASCADE,
    FOREIGN KEY (merchant_id) REFERENCES merchants(merchant_id) ON DELETE CASCADE,
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_merchant_type (merchant_id, calculation_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางการคำนวณค่าธรรมเนียม';

-- ตาราง revenue_recognition - การรับรู้รายได้
CREATE TABLE revenue_recognition (
    revenue_id INT PRIMARY KEY AUTO_INCREMENT,
    merchant_id INT COMMENT 'รหัสร้านค้า (NULL = รายได้ระบบ)',
    transaction_id INT COMMENT 'รหัสธุรกรรมที่เกี่ยวข้อง',
    journal_entry_id INT COMMENT 'รหัสรายการสมุดรายวันทั่วไป',

    -- ข้อมูลรายได้
    revenue_type ENUM('MDR_DEPOSIT', 'FEE_WITHDRAW', 'FEE_TOPUP', 'FEE_SETTLEMENT', 'FEE_TRANSFER', 'OTHER') NOT NULL COMMENT 'ประเภทรายได้',
    revenue_date DATE NOT NULL COMMENT 'วันที่รับรู้รายได้',

    -- จำนวนเงิน
    gross_amount DECIMAL(15,2) NOT NULL COMMENT 'จำนวนเงินรวม',
    tax_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ภาษี',
    net_amount DECIMAL(15,2) NOT NULL COMMENT 'จำนวนเงินสุทธิ',

    -- รายละเอียด
    description TEXT COMMENT 'รายละเอียดรายได้',
    reference_data JSON COMMENT 'ข้อมูลอ้างอิง',

    -- สถานะ
    status ENUM('PENDING', 'RECOGNIZED', 'CANCELLED') DEFAULT 'PENDING' COMMENT 'สถานะการรับรู้รายได้',

    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่อัปเดต',

    FOREIGN KEY (merchant_id) REFERENCES merchants(merchant_id) ON DELETE CASCADE,
    FOREIGN KEY (transaction_id) REFERENCES transactions(transaction_id) ON DELETE SET NULL,
    FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(journal_entry_id) ON DELETE SET NULL,
    INDEX idx_merchant_date (merchant_id, revenue_date),
    INDEX idx_revenue_type (revenue_type),
    INDEX idx_status (status),
    INDEX idx_revenue_date (revenue_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางการรับรู้รายได้';

-- ตาราง accounting_periods - งวดบัญชี
CREATE TABLE accounting_periods (
    period_id INT PRIMARY KEY AUTO_INCREMENT,

    -- ข้อมูลงวด
    period_name VARCHAR(100) NOT NULL COMMENT 'ชื่องวด',
    period_type ENUM('MONTHLY', 'QUARTERLY', 'YEARLY') NOT NULL COMMENT 'ประเภทงวด',
    start_date DATE NOT NULL COMMENT 'วันที่เริ่มต้น',
    end_date DATE NOT NULL COMMENT 'วันที่สิ้นสุด',

    -- สถานะ
    status ENUM('OPEN', 'CLOSED', 'LOCKED') DEFAULT 'OPEN' COMMENT 'สถานะงวด',
    closed_by INT COMMENT 'ผู้ปิดงวด',
    closed_at TIMESTAMP NULL COMMENT 'วันที่ปิดงวด',

    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่อัปเดต',

    FOREIGN KEY (closed_by) REFERENCES users(user_id) ON DELETE SET NULL,
    UNIQUE KEY unique_period_dates (start_date, end_date),
    INDEX idx_period_type (period_type),
    INDEX idx_status (status),
    INDEX idx_date_range (start_date, end_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางงวดบัญชี';
