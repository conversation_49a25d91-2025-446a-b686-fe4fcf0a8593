-- =====================================================
-- SugarPay Simplified Accounting Database Schema
-- ระบบฐานข้อมูลสำหรับการเทียบยอดธุรกรรมกับบัญชีธนาคาร
-- =====================================================

-- ตาราง bank_statements - รายการเดินบัญชีธนาคาร
CREATE TABLE bank_statements (
    statement_id INT PRIMARY KEY AUTO_INCREMENT,
    bank_account_id INT NOT NULL COMMENT 'รหัสบัญชีธนาคาร',
    merchant_id INT NOT NULL COMMENT 'รหัสร้านค้า',

    -- ข้อมูลรายการธนาคาร
    statement_date DATE NOT NULL COMMENT 'วันที่รายการ',
    statement_time TIME COMMENT 'เวลารายการ',
    transaction_type ENUM('CREDIT', 'DEBIT') NOT NULL COMMENT 'ประเภทรายการ (เข้า/ออก)',

    -- จำนวนเงิน
    amount DECIMAL(15,2) NOT NULL COMMENT 'จำนวนเงิน',
    balance_after DECIMAL(15,2) COMMENT 'ยอดคงเหลือหลังรายการ',

    -- ข้อมูลรายการ
    description TEXT COMMENT 'รายละเอียดรายการ',
    reference_number VARCHAR(100) COMMENT 'เลขที่อ้างอิงธนาคาร',
    channel VARCHAR(50) COMMENT 'ช่องทางการทำรายการ',

    -- ข้อมูลผู้ทำรายการ
    from_account VARCHAR(50) COMMENT 'บัญชีต้นทาง',
    from_bank VARCHAR(100) COMMENT 'ธนาคารต้นทาง',
    to_account VARCHAR(50) COMMENT 'บัญชีปลายทาง',
    to_bank VARCHAR(100) COMMENT 'ธนาคารปลายทาง',

    -- การจับคู่กับธุรกรรม
    matched_transaction_id INT COMMENT 'รหัสธุรกรรมที่จับคู่',
    match_status ENUM('UNMATCHED', 'MATCHED', 'PARTIAL', 'DISPUTED') DEFAULT 'UNMATCHED' COMMENT 'สถานะการจับคู่',
    match_confidence DECIMAL(5,2) DEFAULT 0.00 COMMENT 'ความมั่นใจในการจับคู่ (%)',
    matched_by INT COMMENT 'ผู้จับคู่',
    matched_at TIMESTAMP NULL COMMENT 'วันที่จับคู่',

    -- ข้อมูลการนำเข้า
    import_batch_id VARCHAR(100) COMMENT 'รหัสชุดการนำเข้า',
    import_source ENUM('API', 'FILE', 'MANUAL', 'SLIP') DEFAULT 'API' COMMENT 'แหล่งที่มาข้อมูล',
    raw_data JSON COMMENT 'ข้อมูลดิบจากธนาคาร',

    -- สถานะ
    is_verified TINYINT(1) DEFAULT 0 COMMENT 'ตรวจสอบแล้วหรือไม่',
    verified_by INT COMMENT 'ผู้ตรวจสอบ',
    verified_at TIMESTAMP NULL COMMENT 'วันที่ตรวจสอบ',

    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่อัปเดต',

    FOREIGN KEY (bank_account_id) REFERENCES bank_accounts(bank_account_id) ON DELETE CASCADE,
    FOREIGN KEY (merchant_id) REFERENCES merchants(merchant_id) ON DELETE CASCADE,
    FOREIGN KEY (matched_transaction_id) REFERENCES transactions(transaction_id) ON DELETE SET NULL,
    FOREIGN KEY (matched_by) REFERENCES users(user_id) ON DELETE SET NULL,
    FOREIGN KEY (verified_by) REFERENCES users(user_id) ON DELETE SET NULL,
    INDEX idx_bank_account_date (bank_account_id, statement_date),
    INDEX idx_merchant_date (merchant_id, statement_date),
    INDEX idx_match_status (match_status),
    INDEX idx_amount (amount),
    INDEX idx_reference_number (reference_number),
    INDEX idx_import_batch (import_batch_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางรายการเดินบัญชีธนาคาร';

-- ตาราง transaction_reconciliation - การกระทบยอดธุรกรรม
CREATE TABLE transaction_reconciliation (
    reconciliation_id INT PRIMARY KEY AUTO_INCREMENT,
    merchant_id INT NOT NULL COMMENT 'รหัสร้านค้า',
    bank_account_id INT NOT NULL COMMENT 'รหัสบัญชีธนาคาร',

    -- ช่วงเวลากระทบยอด
    reconciliation_date DATE NOT NULL COMMENT 'วันที่กระทบยอด',
    start_date DATE NOT NULL COMMENT 'วันที่เริ่มต้น',
    end_date DATE NOT NULL COMMENT 'วันที่สิ้นสุด',

    -- สรุปธุรกรรมระบบ
    system_total_in DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดเงินเข้าจากระบบ',
    system_total_out DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดเงินออกจากระบบ',
    system_transaction_count INT DEFAULT 0 COMMENT 'จำนวนธุรกรรมระบบ',

    -- สรุปรายการธนาคาร
    bank_total_in DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดเงินเข้าจากธนาคาร',
    bank_total_out DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดเงินออกจากธนาคาร',
    bank_statement_count INT DEFAULT 0 COMMENT 'จำนวนรายการธนาคาร',

    -- ผลต่าง
    difference_in DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ผลต่างเงินเข้า',
    difference_out DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ผลต่างเงินออก',
    unmatched_transactions INT DEFAULT 0 COMMENT 'จำนวนธุรกรรมที่ไม่จับคู่',
    unmatched_statements INT DEFAULT 0 COMMENT 'จำนวนรายการธนาคารที่ไม่จับคู่',

    -- สถานะ
    status ENUM('BALANCED', 'UNBALANCED', 'INVESTIGATING', 'RESOLVED') DEFAULT 'BALANCED' COMMENT 'สถานะการกระทบยอด',
    notes TEXT COMMENT 'หมายเหตุ',

    -- ผู้ทำการกระทบยอด
    reconciled_by INT COMMENT 'ผู้ทำการกระทบยอด',

    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่อัปเดต',

    FOREIGN KEY (merchant_id) REFERENCES merchants(merchant_id) ON DELETE CASCADE,
    FOREIGN KEY (bank_account_id) REFERENCES bank_accounts(bank_account_id) ON DELETE CASCADE,
    FOREIGN KEY (reconciled_by) REFERENCES users(user_id) ON DELETE SET NULL,
    UNIQUE KEY unique_merchant_bank_date (merchant_id, bank_account_id, reconciliation_date),
    INDEX idx_merchant_date (merchant_id, reconciliation_date),
    INDEX idx_bank_account_date (bank_account_id, reconciliation_date),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางการกระทบยอดธุรกรรม';

-- ตาราง matching_rules - กฎการจับคู่ธุรกรรม
CREATE TABLE matching_rules (
    rule_id INT PRIMARY KEY AUTO_INCREMENT,
    merchant_id INT COMMENT 'รหัสร้านค้า (NULL = ระดับระบบ)',
    bank_account_id INT COMMENT 'รหัสบัญชีธนาคาร (NULL = ทุกบัญชี)',

    -- ข้อมูลกฎ
    rule_name VARCHAR(255) NOT NULL COMMENT 'ชื่อกฎ',
    rule_description TEXT COMMENT 'รายละเอียดกฎ',
    rule_type ENUM('EXACT_AMOUNT', 'AMOUNT_RANGE', 'TIME_WINDOW', 'REFERENCE_MATCH', 'CUSTOM') NOT NULL COMMENT 'ประเภทกฎ',

    -- เงื่อนไขการจับคู่
    amount_tolerance DECIMAL(10,2) DEFAULT 0.00 COMMENT 'ความคลาดเคลื่อนจำนวนเงิน',
    time_window_minutes INT DEFAULT 60 COMMENT 'ช่วงเวลาการจับคู่ (นาที)',
    match_criteria JSON COMMENT 'เกณฑ์การจับคู่',

    -- การตั้งค่า
    priority_order INT DEFAULT 1 COMMENT 'ลำดับความสำคัญ',
    is_active TINYINT(1) DEFAULT 1 COMMENT 'เปิดใช้งานหรือไม่',
    auto_match TINYINT(1) DEFAULT 1 COMMENT 'จับคู่อัตโนมัติหรือไม่',

    -- สถิติการใช้งาน
    usage_count INT DEFAULT 0 COMMENT 'จำนวนครั้งที่ใช้งาน',
    success_count INT DEFAULT 0 COMMENT 'จำนวนครั้งที่สำเร็จ',

    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่อัปเดต',

    FOREIGN KEY (merchant_id) REFERENCES merchants(merchant_id) ON DELETE CASCADE,
    FOREIGN KEY (bank_account_id) REFERENCES bank_accounts(bank_account_id) ON DELETE CASCADE,
    INDEX idx_merchant_active (merchant_id, is_active),
    INDEX idx_rule_type (rule_type),
    INDEX idx_priority_order (priority_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางกฎการจับคู่ธุรกรรม';

-- ตาราง reconciliation_exceptions - ข้อยกเว้นการกระทบยอด
CREATE TABLE reconciliation_exceptions (
    exception_id INT PRIMARY KEY AUTO_INCREMENT,
    merchant_id INT NOT NULL COMMENT 'รหัสร้านค้า',
    reconciliation_id INT COMMENT 'รหัสการกระทบยอด',

    -- ข้อมูลข้อยกเว้น
    exception_type ENUM('UNMATCHED_TRANSACTION', 'UNMATCHED_STATEMENT', 'AMOUNT_DIFFERENCE', 'DUPLICATE', 'OTHER') NOT NULL COMMENT 'ประเภทข้อยกเว้น',
    transaction_id INT COMMENT 'รหัสธุรกรรมที่เกี่ยวข้อง',
    statement_id INT COMMENT 'รหัสรายการธนาคารที่เกี่ยวข้อง',

    -- รายละเอียด
    description TEXT NOT NULL COMMENT 'รายละเอียดข้อยกเว้น',
    amount DECIMAL(15,2) COMMENT 'จำนวนเงินที่เกี่ยวข้อง',
    expected_amount DECIMAL(15,2) COMMENT 'จำนวนเงินที่คาดหวัง',

    -- การแก้ไข
    resolution_status ENUM('PENDING', 'INVESTIGATING', 'RESOLVED', 'IGNORED') DEFAULT 'PENDING' COMMENT 'สถานะการแก้ไข',
    resolution_notes TEXT COMMENT 'หมายเหตุการแก้ไข',
    resolved_by INT COMMENT 'ผู้แก้ไข',
    resolved_at TIMESTAMP NULL COMMENT 'วันที่แก้ไข',

    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่อัปเดต',

    FOREIGN KEY (merchant_id) REFERENCES merchants(merchant_id) ON DELETE CASCADE,
    FOREIGN KEY (reconciliation_id) REFERENCES transaction_reconciliation(reconciliation_id) ON DELETE CASCADE,
    FOREIGN KEY (transaction_id) REFERENCES transactions(transaction_id) ON DELETE SET NULL,
    FOREIGN KEY (statement_id) REFERENCES bank_statements(statement_id) ON DELETE SET NULL,
    FOREIGN KEY (resolved_by) REFERENCES users(user_id) ON DELETE SET NULL,
    INDEX idx_merchant_status (merchant_id, resolution_status),
    INDEX idx_exception_type (exception_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางข้อยกเว้นการกระทบยอด';
