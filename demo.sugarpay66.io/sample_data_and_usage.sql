-- =====================================================
-- SugarPay Sample Data และตัวอย่างการใช้งาน
-- =====================================================

-- =====================================================
-- 1. INSERT SAMPLE DATA
-- =====================================================

-- Insert Sample Agent
INSERT INTO `agents` (`agent_code`, `agent_name`, `contact_person`, `phone`, `email`, `status`) VALUES
('AGENT001', 'SugarPay Agent 1', '<PERSON>', '0812345678', '<EMAIL>', 'active');

SET @agent_id = LAST_INSERT_ID();

-- Insert Sample Merchants
INSERT INTO `merchants` (`agent_id`, `merchant_code`, `merchant_name`, `business_type`, `contact_person`, `phone`, `email`, `api_endpoint`, `api_key`, `bearer_token`, `x_api_key`, `ip_whitelist`, `status`) VALUES
(@agent_id, 'tiger-001', 'Tiger Restaurant', 'Restaurant', 'Tiger Owner', '0823456789', '<EMAIL>', 'https://tiger-restaurant.com/api/webhook', 'tiger_api_key_123', 'Bearer tiger_token_456', 'tiger_x_api_789', '*************,***********', 'active'),
(@agent_id, 'shop-002', 'Modern Shop', 'Retail', 'Shop Manager', '**********', '<EMAIL>', 'https://modernshop.com/api/callback', 'shop_api_key_abc', 'Bearer shop_token_def', 'shop_x_api_ghi', '*************', 'active');

-- Get merchant IDs
SET @merchant1_id = (SELECT merchant_id FROM merchants WHERE merchant_code = 'tiger-001');
SET @merchant2_id = (SELECT merchant_id FROM merchants WHERE merchant_code = 'shop-002');

-- Insert Sample Bank Accounts (3 types, multiple accounts per type)
INSERT INTO `bank_accounts` (`agent_id`, `bank_type`, `bank_name`, `bank_code`, `bank_acc_no`, `bank_acc_name`, `bank_token`, `priority`, `balance`, `is_enable`, `is_primary_withdraw_bank`) VALUES
-- DEPOSIT Banks
(@agent_id, 'DEPOSIT', 'Kasikorn Bank', 'KBANK', '**********', 'SugarPay Deposit 1', 'kbank_token_001', 1, 50000.00, 1, 0),
(@agent_id, 'DEPOSIT', 'Bangkok Bank', 'BBL', '**********', 'SugarPay Deposit 2', 'bbl_token_002', 2, 30000.00, 1, 0),
(@agent_id, 'DEPOSIT', 'SCB Bank', 'SCB', '**********', 'SugarPay Deposit 3', 'scb_token_003', 3, 25000.00, 1, 0),

-- WITHDRAW Banks
(@agent_id, 'WITHDRAW', 'Kasikorn Bank', 'KBANK', '**********', 'SugarPay Withdraw 1', 'kbank_withdraw_001', 1, 100000.00, 1, 1),
(@agent_id, 'WITHDRAW', 'Bangkok Bank', 'BBL', '**********', 'SugarPay Withdraw 2', 'bbl_withdraw_002', 2, 80000.00, 1, 0),

-- SAVINGS Banks
(@agent_id, 'SAVINGS', 'Kasikorn Bank', 'KBANK', '**********', 'SugarPay Savings 1', 'kbank_savings_001', 1, 500000.00, 1, 0),
(@agent_id, 'SAVINGS', 'Government Savings Bank', 'GSB', '**********', 'SugarPay Savings 2', 'gsb_savings_002', 2, 300000.00, 1, 0);

-- Insert Bank API Configs
INSERT INTO `bank_api_configs` (`bank_account_id`, `api_endpoint`, `api_key`, `api_secret`, `bearer_token`, `x_api_key`, `timeout_seconds`, `retry_attempts`) VALUES
(1, 'https://api.kasikornbank.com/deposit', 'kbank_api_key', 'kbank_secret', 'Bearer kbank_token', 'kbank_x_api', 30, 3),
(2, 'https://api.bangkokbank.com/deposit', 'bbl_api_key', 'bbl_secret', 'Bearer bbl_token', 'bbl_x_api', 30, 3),
(4, 'https://api.kasikornbank.com/withdraw', 'kbank_withdraw_key', 'kbank_withdraw_secret', 'Bearer kbank_withdraw_token', 'kbank_withdraw_x_api', 45, 3);

-- Insert Sample Users
INSERT INTO `users` (`agent_id`, `merchant_id`, `username`, `password`, `name`, `email`, `user_type`, `is_google2fa`) VALUES
(@agent_id, NULL, 'admin_agent', '$2y$10$example_hash', 'Agent Administrator', '<EMAIL>', 'agent', 1),
(@agent_id, @merchant1_id, 'tiger_user', '$2y$10$example_hash', 'Tiger Restaurant User', '<EMAIL>', 'merchant', 0),
(@agent_id, @merchant2_id, 'shop_user', '$2y$10$example_hash', 'Modern Shop User', '<EMAIL>', 'merchant', 1);

-- =====================================================
-- 2. SAMPLE TRANSACTIONS
-- =====================================================

-- Sample Deposit Transaction
INSERT INTO `transactions` (`merchant_id`, `agent_id`, `bank_account_id`, `txn_hash`, `order_id`, `transaction_type`, `deposit_type`, `channel`, `txn_amount`, `mdr_amount`, `withdraw_fee_amount`, `txn_status`, `txn_date`, `txn_bank_name`, `txn_acc_no`, `txn_acc_name`, `customer_name`, `customer_phone`) VALUES
(@merchant1_id, @agent_id, 1, 'TXN001_' || UNIX_TIMESTAMP(), 'ORDER_001', 'deposit', 'QR Code', 'mobile', 1000.00, 15.00, 0.00, 'SUCCESS', NOW(), 'Kasikorn Bank', '**********', 'Customer Name', 'John Customer', '**********');

-- Sample TopUp Transaction
INSERT INTO `transactions` (`merchant_id`, `agent_id`, `bank_account_id`, `txn_hash`, `order_id`, `transaction_type`, `deposit_type`, `channel`, `txn_amount`, `mdr_amount`, `withdraw_fee_amount`, `txn_status`, `txn_date`, `remark`) VALUES
(@merchant1_id, @agent_id, 1, 'TXN002_' || UNIX_TIMESTAMP(), 'TOPUP_001', 'topup', 'Bank Transfer', 'web', 5000.00, 75.00, 0.00, 'SUCCESS', NOW(), 'TopUp to withdraw balance');

-- Sample Transfer Transaction (Free)
INSERT INTO `transactions` (`merchant_id`, `agent_id`, `txn_hash`, `order_id`, `transaction_type`, `txn_amount`, `mdr_amount`, `withdraw_fee_amount`, `txn_status`, `txn_date`, `remark`) VALUES
(@merchant1_id, @agent_id, 'TXN003_' || UNIX_TIMESTAMP(), 'TRANSFER_001', 'transfer', 2000.00, 0.00, 0.00, 'SUCCESS', NOW(), 'Transfer from deposit to withdraw balance');

-- Sample Withdraw Transaction
INSERT INTO `transactions` (`merchant_id`, `agent_id`, `bank_account_id`, `txn_hash`, `order_id`, `transaction_type`, `txn_amount`, `mdr_amount`, `withdraw_fee_amount`, `txn_status`, `txn_date`, `txn_bank_name`, `txn_acc_no`, `txn_acc_name`) VALUES
(@merchant1_id, @agent_id, 4, 'TXN004_' || UNIX_TIMESTAMP(), 'WITHDRAW_001', 'withdraw', 3000.00, 0.00, 10.00, 'SUCCESS', NOW(), 'Kasikorn Bank', '**********', 'Tiger Restaurant Account');

-- Sample Settlement Transaction
INSERT INTO `transactions` (`merchant_id`, `agent_id`, `bank_account_id`, `txn_hash`, `order_id`, `transaction_type`, `txn_amount`, `mdr_amount`, `withdraw_fee_amount`, `txn_status`, `txn_date`, `settlement_date`, `remark`) VALUES
(@merchant1_id, @agent_id, 4, 'TXN005_' || UNIX_TIMESTAMP(), 'SETTLE_001', 'settlement', 10000.00, 0.00, 10.00, 'SUCCESS', NOW(), CURDATE(), 'Daily settlement');

-- =====================================================
-- 3. SAMPLE QUERIES สำหรับการใช้งาน
-- =====================================================

-- Query 1: ดูยอดเงินของร้านค้าทั้งหมด
SELECT * FROM v_merchant_balance_summary;

-- Query 2: ดูธุรกรรมของร้านค้าเฉพาะ
SELECT * FROM v_transaction_details 
WHERE merchant_code = 'tiger-001' 
ORDER BY txn_date DESC;

-- Query 3: ดูค่าธรรมเนียมที่มีผล
SELECT * FROM v_active_mdr_fees;

-- Query 4: สรุปยอดธุรกรรมรายวัน
SELECT 
    DATE(txn_date) as transaction_date,
    transaction_type,
    COUNT(*) as transaction_count,
    SUM(txn_amount) as total_amount,
    SUM(mdr_amount) as total_mdr,
    SUM(withdraw_fee_amount) as total_withdraw_fee,
    SUM(net_amount) as total_net
FROM transactions 
WHERE txn_status = 'SUCCESS'
  AND txn_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY DATE(txn_date), transaction_type
ORDER BY transaction_date DESC, transaction_type;

-- Query 5: ดูประวัติการเปลี่ยนแปลงยอดเงิน
SELECT 
    bl.*,
    m.merchant_code,
    m.merchant_name,
    t.txn_hash,
    t.order_id
FROM balance_logs bl
JOIN merchants m ON bl.merchant_id = m.merchant_id
LEFT JOIN transactions t ON bl.transaction_id = t.transaction_id
WHERE bl.merchant_id = @merchant1_id
ORDER BY bl.created_date DESC
LIMIT 20;

-- Query 6: ดูบัญชีธนาคารที่ใช้งานได้
SELECT 
    ba.bank_type,
    ba.bank_name,
    ba.bank_acc_no,
    ba.priority,
    ba.balance,
    ba.is_enable,
    bac.api_endpoint
FROM bank_accounts ba
LEFT JOIN bank_api_configs bac ON ba.bank_account_id = bac.bank_account_id
WHERE ba.agent_id = @agent_id 
  AND ba.is_enable = 1 
  AND ba.is_delete = 0
ORDER BY ba.bank_type, ba.priority;

-- Query 7: คำนวณรายได้จากค่าธรรมเนียม
SELECT 
    DATE(t.txn_date) as revenue_date,
    SUM(t.mdr_amount) as mdr_revenue,
    SUM(t.withdraw_fee_amount) as withdraw_fee_revenue,
    SUM(t.mdr_amount + t.withdraw_fee_amount) as total_revenue,
    COUNT(*) as transaction_count
FROM transactions t
WHERE t.txn_status = 'SUCCESS'
  AND t.txn_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(t.txn_date)
ORDER BY revenue_date DESC;

-- =====================================================
-- 4. SAMPLE BALANCE UPDATES
-- =====================================================

-- ตัวอย่างการอัพเดทยอดเงินด้วย Stored Procedure
CALL sp_update_merchant_balance(
    @merchant1_id,      -- merchant_id
    1,                  -- transaction_id
    'deposit',          -- balance_type
    'credit',           -- operation
    1000.00,           -- amount
    'Deposit from customer ORDER_001',  -- description
    1                   -- created_by
);

-- ตัวอย่างการโอนเงินจาก deposit ไป withdraw
CALL sp_update_merchant_balance(@merchant1_id, 3, 'deposit', 'transfer_out', 2000.00, 'Transfer to withdraw balance', 1);
CALL sp_update_merchant_balance(@merchant1_id, 3, 'withdraw', 'transfer_in', 2000.00, 'Transfer from deposit balance', 1);

-- =====================================================
-- 5. SAMPLE DOUBLE ENTRY RECORDS
-- =====================================================

-- ตัวอย่าง Double Entry สำหรับ Deposit Transaction
INSERT INTO `double_entry_ledger` (`transaction_id`, `merchant_id`, `account_type`, `account_name`, `debit_amount`, `credit_amount`, `balance_type`, `description`) VALUES
-- เพิ่มเงินในบัญชี Deposit (Asset)
(1, @merchant1_id, 'asset', 'Deposit_Balance', 985.00, 0.00, 'deposit', 'Customer deposit after MDR'),
-- รายได้จากค่าธรรมเนียม (Revenue)
(1, @merchant1_id, 'revenue', 'MDR_Fee_Revenue', 15.00, 0.00, 'fee', 'MDR fee from deposit'),
-- เงินสดที่ได้รับ (Asset)
(1, @merchant1_id, 'asset', 'Cash_Received', 0.00, 1000.00, 'deposit', 'Cash received from customer');

-- =====================================================
-- 6. VERIFICATION QUERIES
-- =====================================================

-- ตรวจสอบความถูกต้องของยอดเงิน
SELECT 
    m.merchant_code,
    mb.deposit_balance,
    mb.withdraw_balance,
    mb.frozen_balance,
    mb.wait_confirm_amount,
    (mb.deposit_balance + mb.withdraw_balance + mb.frozen_balance + mb.wait_confirm_amount) as total_balance
FROM merchants m
JOIN merchant_balances mb ON m.merchant_id = mb.merchant_id
WHERE m.merchant_code IN ('tiger-001', 'shop-002');

-- ตรวจสอบ Double Entry Balance
SELECT 
    transaction_id,
    SUM(debit_amount) as total_debit,
    SUM(credit_amount) as total_credit,
    (SUM(debit_amount) - SUM(credit_amount)) as balance_check
FROM double_entry_ledger
GROUP BY transaction_id
HAVING balance_check != 0;  -- ควรไม่มีผลลัพธ์ (balanced)
