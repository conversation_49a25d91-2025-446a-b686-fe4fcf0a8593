/*
Template Name: V<PERSON><PERSON> - Admin & Dashboard Template
Author: Themesbrand
Website: https://themesbrand.com/
Contact: <EMAIL>
File: Custom Css File
*/
.menu-title span {
  color: #fff;
  padding: 12px 20px;
  display: inline-block;
  font-size: 14px;
  font-weight: 100;
}
.navbar-menu {
  border-right: none !important;
}
.navbar-brand-box {
  /*background-color: #fff;
    border-right: #fff;*/
}
/* btn-danger */
.btn-primary {
  --vz-btn-color: #fff;
  --vz-btn-bg: var(--theme-color_primary);
  --vz-btn-border-color: var(--theme-color_primary);
  --vz-btn-hover-color: #fff;
  --vz-btn-hover-bg: #364574;
  --vz-btn-hover-border-color: #33416e;
  --vz-btn-focus-shadow-rgb: 93, 107, 155;
  --vz-btn-active-color: #fff;
  --vz-btn-active-bg: #33416e;
  --vz-btn-active-border-color: #303d67;
  --vz-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --vz-btn-disabled-color: #fff;
  --vz-btn-disabled-bg: var(--theme-color_primary);
  --vz-btn-disabled-border-color: var(--theme-color_primary);
}
.nav-pills .nav-link {
  text-align: left !important;
}

.table > tbody > tr > td {
  padding: 0.15rem 0.6rem !important;
}
img.previewImage {
  cursor: pointer;
}
/* .page-content{
    padding-right: 0;
    padding-left: 0;
} */
