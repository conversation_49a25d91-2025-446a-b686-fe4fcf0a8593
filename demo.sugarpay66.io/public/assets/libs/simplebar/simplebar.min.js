/**
 * SimpleBar.js - v5.3.8
 * Scrollbars, simpler.
 * https://grsmto.github.io/simplebar/
 *
 * Made by <PERSON><PERSON> from a fork by <PERSON>
 * Under MIT License
 */

!(function (t, e) {
  "object" == typeof exports && "undefined" != typeof module
    ? (module.exports = e())
    : "function" == typeof define && define.amd
    ? define(e)
    : ((t = t || self).SimpleBar = e());
})(this, function () {
  "use strict";
  var t =
    "undefined" != typeof globalThis
      ? globalThis
      : "undefined" != typeof window
      ? window
      : "undefined" != typeof global
      ? global
      : "undefined" != typeof self
      ? self
      : {};
  function e(t, e) {
    return t((e = { exports: {} }), e.exports), e.exports;
  }
  var r,
    n,
    i = function (t) {
      return t && t.Math == Math && t;
    },
    o =
      i("object" == typeof globalThis && globalThis) ||
      i("object" == typeof window && window) ||
      i("object" == typeof self && self) ||
      i("object" == typeof t && t) ||
      (function () {
        return this;
      })() ||
      Function("return this")(),
    s = Object.defineProperty,
    a = function (t, e) {
      try {
        s(o, t, { value: e, configurable: !0, writable: !0 });
      } catch (r) {
        o[t] = e;
      }
      return e;
    },
    c = o["__core-js_shared__"] || a("__core-js_shared__", {}),
    l = e(function (t) {
      (t.exports = function (t, e) {
        return c[t] || (c[t] = void 0 !== e ? e : {});
      })("versions", []).push({
        version: "3.22.6",
        mode: "global",
        copyright: "© 2014-2022 Denis Pushkarev (zloirock.ru)",
        license: "https://github.com/zloirock/core-js/blob/v3.22.6/LICENSE",
        source: "https://github.com/zloirock/core-js",
      });
    }),
    u = function (t) {
      try {
        return !!t();
      } catch (t) {
        return !0;
      }
    },
    f = !u(function () {
      var t = function () {}.bind();
      return "function" != typeof t || t.hasOwnProperty("prototype");
    }),
    h = Function.prototype,
    d = h.bind,
    p = h.call,
    v = f && d.bind(p, p),
    g = f
      ? function (t) {
          return t && v(t);
        }
      : function (t) {
          return (
            t &&
            function () {
              return p.apply(t, arguments);
            }
          );
        },
    b = o.TypeError,
    y = function (t) {
      if (null == t) throw b("Can't call method on " + t);
      return t;
    },
    m = o.Object,
    x = function (t) {
      return m(y(t));
    },
    E = g({}.hasOwnProperty),
    w =
      Object.hasOwn ||
      function (t, e) {
        return E(x(t), e);
      },
    O = 0,
    S = Math.random(),
    A = g((1).toString),
    k = function (t) {
      return "Symbol(" + (void 0 === t ? "" : t) + ")_" + A(++O + S, 36);
    },
    T = function (t) {
      return "function" == typeof t;
    },
    L = function (t) {
      return T(t) ? t : void 0;
    },
    R = function (t, e) {
      return arguments.length < 2 ? L(o[t]) : o[t] && o[t][e];
    },
    _ = R("navigator", "userAgent") || "",
    j = o.process,
    z = o.Deno,
    M = (j && j.versions) || (z && z.version),
    C = M && M.v8;
  C && (n = (r = C.split("."))[0] > 0 && r[0] < 4 ? 1 : +(r[0] + r[1])),
    !n &&
      _ &&
      (!(r = _.match(/Edge\/(\d+)/)) || r[1] >= 74) &&
      (r = _.match(/Chrome\/(\d+)/)) &&
      (n = +r[1]);
  var N = n,
    W =
      !!Object.getOwnPropertySymbols &&
      !u(function () {
        var t = Symbol();
        return (
          !String(t) ||
          !(Object(t) instanceof Symbol) ||
          (!Symbol.sham && N && N < 41)
        );
      }),
    I = W && !Symbol.sham && "symbol" == typeof Symbol.iterator,
    P = l("wks"),
    B = o.Symbol,
    D = B && B.for,
    F = I ? B : (B && B.withoutSetter) || k,
    V = function (t) {
      if (!w(P, t) || (!W && "string" != typeof P[t])) {
        var e = "Symbol." + t;
        W && w(B, t) ? (P[t] = B[t]) : (P[t] = I && D ? D(e) : F(e));
      }
      return P[t];
    },
    $ = {};
  $[V("toStringTag")] = "z";
  var X = "[object z]" === String($),
    H = !u(function () {
      return (
        7 !=
        Object.defineProperty({}, 1, {
          get: function () {
            return 7;
          },
        })[1]
      );
    }),
    q = function (t) {
      return "object" == typeof t ? null !== t : T(t);
    },
    Y = o.document,
    G = q(Y) && q(Y.createElement),
    U = function (t) {
      return G ? Y.createElement(t) : {};
    },
    K =
      !H &&
      !u(function () {
        return (
          7 !=
          Object.defineProperty(U("div"), "a", {
            get: function () {
              return 7;
            },
          }).a
        );
      }),
    J =
      H &&
      u(function () {
        return (
          42 !=
          Object.defineProperty(function () {}, "prototype", {
            value: 42,
            writable: !1,
          }).prototype
        );
      }),
    Q = o.String,
    Z = o.TypeError,
    tt = function (t) {
      if (q(t)) return t;
      throw Z(Q(t) + " is not an object");
    },
    et = Function.prototype.call,
    rt = f
      ? et.bind(et)
      : function () {
          return et.apply(et, arguments);
        },
    nt = g({}.isPrototypeOf),
    it = o.Object,
    ot = I
      ? function (t) {
          return "symbol" == typeof t;
        }
      : function (t) {
          var e = R("Symbol");
          return T(e) && nt(e.prototype, it(t));
        },
    st = o.String,
    at = function (t) {
      try {
        return st(t);
      } catch (t) {
        return "Object";
      }
    },
    ct = o.TypeError,
    lt = function (t) {
      if (T(t)) return t;
      throw ct(at(t) + " is not a function");
    },
    ut = function (t, e) {
      var r = t[e];
      return null == r ? void 0 : lt(r);
    },
    ft = o.TypeError,
    ht = o.TypeError,
    dt = V("toPrimitive"),
    pt = function (t, e) {
      if (!q(t) || ot(t)) return t;
      var r,
        n = ut(t, dt);
      if (n) {
        if (
          (void 0 === e && (e = "default"), (r = rt(n, t, e)), !q(r) || ot(r))
        )
          return r;
        throw ht("Can't convert object to primitive value");
      }
      return (
        void 0 === e && (e = "number"),
        (function (t, e) {
          var r, n;
          if ("string" === e && T((r = t.toString)) && !q((n = rt(r, t))))
            return n;
          if (T((r = t.valueOf)) && !q((n = rt(r, t)))) return n;
          if ("string" !== e && T((r = t.toString)) && !q((n = rt(r, t))))
            return n;
          throw ft("Can't convert object to primitive value");
        })(t, e)
      );
    },
    vt = function (t) {
      var e = pt(t, "string");
      return ot(e) ? e : e + "";
    },
    gt = o.TypeError,
    bt = Object.defineProperty,
    yt = Object.getOwnPropertyDescriptor,
    mt = {
      f: H
        ? J
          ? function (t, e, r) {
              if (
                (tt(t),
                (e = vt(e)),
                tt(r),
                "function" == typeof t &&
                  "prototype" === e &&
                  "value" in r &&
                  "writable" in r &&
                  !r.writable)
              ) {
                var n = yt(t, e);
                n &&
                  n.writable &&
                  ((t[e] = r.value),
                  (r = {
                    configurable:
                      "configurable" in r ? r.configurable : n.configurable,
                    enumerable: "enumerable" in r ? r.enumerable : n.enumerable,
                    writable: !1,
                  }));
              }
              return bt(t, e, r);
            }
          : bt
        : function (t, e, r) {
            if ((tt(t), (e = vt(e)), tt(r), K))
              try {
                return bt(t, e, r);
              } catch (t) {}
            if ("get" in r || "set" in r) throw gt("Accessors not supported");
            return "value" in r && (t[e] = r.value), t;
          },
    },
    xt = function (t, e) {
      return {
        enumerable: !(1 & t),
        configurable: !(2 & t),
        writable: !(4 & t),
        value: e,
      };
    },
    Et = H
      ? function (t, e, r) {
          return mt.f(t, e, xt(1, r));
        }
      : function (t, e, r) {
          return (t[e] = r), t;
        },
    wt = Function.prototype,
    Ot = H && Object.getOwnPropertyDescriptor,
    St = w(wt, "name"),
    At = {
      EXISTS: St,
      PROPER: St && "something" === function () {}.name,
      CONFIGURABLE: St && (!H || (H && Ot(wt, "name").configurable)),
    },
    kt = g(Function.toString);
  T(c.inspectSource) ||
    (c.inspectSource = function (t) {
      return kt(t);
    });
  var Tt,
    Lt,
    Rt,
    _t = c.inspectSource,
    jt = o.WeakMap,
    zt = T(jt) && /native code/.test(_t(jt)),
    Mt = l("keys"),
    Ct = function (t) {
      return Mt[t] || (Mt[t] = k(t));
    },
    Nt = {},
    Wt = o.TypeError,
    It = o.WeakMap;
  if (zt || c.state) {
    var Pt = c.state || (c.state = new It()),
      Bt = g(Pt.get),
      Dt = g(Pt.has),
      Ft = g(Pt.set);
    (Tt = function (t, e) {
      if (Dt(Pt, t)) throw new Wt("Object already initialized");
      return (e.facade = t), Ft(Pt, t, e), e;
    }),
      (Lt = function (t) {
        return Bt(Pt, t) || {};
      }),
      (Rt = function (t) {
        return Dt(Pt, t);
      });
  } else {
    var Vt = Ct("state");
    (Nt[Vt] = !0),
      (Tt = function (t, e) {
        if (w(t, Vt)) throw new Wt("Object already initialized");
        return (e.facade = t), Et(t, Vt, e), e;
      }),
      (Lt = function (t) {
        return w(t, Vt) ? t[Vt] : {};
      }),
      (Rt = function (t) {
        return w(t, Vt);
      });
  }
  var $t = {
      set: Tt,
      get: Lt,
      has: Rt,
      enforce: function (t) {
        return Rt(t) ? Lt(t) : Tt(t, {});
      },
      getterFor: function (t) {
        return function (e) {
          var r;
          if (!q(e) || (r = Lt(e)).type !== t)
            throw Wt("Incompatible receiver, " + t + " required");
          return r;
        };
      },
    },
    Xt = e(function (t) {
      var e = At.CONFIGURABLE,
        r = $t.enforce,
        n = $t.get,
        i = Object.defineProperty,
        o =
          H &&
          !u(function () {
            return 8 !== i(function () {}, "length", { value: 8 }).length;
          }),
        s = String(String).split("String"),
        a = (t.exports = function (t, n, a) {
          if (
            ("Symbol(" === String(n).slice(0, 7) &&
              (n = "[" + String(n).replace(/^Symbol\(([^)]*)\)/, "$1") + "]"),
            a && a.getter && (n = "get " + n),
            a && a.setter && (n = "set " + n),
            (!w(t, "name") || (e && t.name !== n)) &&
              i(t, "name", { value: n, configurable: !0 }),
            o &&
              a &&
              w(a, "arity") &&
              t.length !== a.arity &&
              i(t, "length", { value: a.arity }),
            a && w(a, "constructor") && a.constructor)
          ) {
            if (H)
              try {
                i(t, "prototype", { writable: !1 });
              } catch (t) {}
          } else t.prototype = void 0;
          var c = r(t);
          return (
            w(c, "source") ||
              (c.source = s.join("string" == typeof n ? n : "")),
            t
          );
        });
      Function.prototype.toString = a(function () {
        return (T(this) && n(this).source) || _t(this);
      }, "toString");
    }),
    Ht = function (t, e, r, n) {
      n || (n = {});
      var i = n.enumerable,
        o = void 0 !== n.name ? n.name : e;
      return (
        T(r) && Xt(r, o, n),
        n.global
          ? i
            ? (t[e] = r)
            : a(e, r)
          : (n.unsafe ? t[e] && (i = !0) : delete t[e],
            i ? (t[e] = r) : Et(t, e, r)),
        t
      );
    },
    qt = g({}.toString),
    Yt = g("".slice),
    Gt = function (t) {
      return Yt(qt(t), 8, -1);
    },
    Ut = V("toStringTag"),
    Kt = o.Object,
    Jt =
      "Arguments" ==
      Gt(
        (function () {
          return arguments;
        })()
      ),
    Qt = X
      ? Gt
      : function (t) {
          var e, r, n;
          return void 0 === t
            ? "Undefined"
            : null === t
            ? "Null"
            : "string" ==
              typeof (r = (function (t, e) {
                try {
                  return t[e];
                } catch (t) {}
              })((e = Kt(t)), Ut))
            ? r
            : Jt
            ? Gt(e)
            : "Object" == (n = Gt(e)) && T(e.callee)
            ? "Arguments"
            : n;
        },
    Zt = X
      ? {}.toString
      : function () {
          return "[object " + Qt(this) + "]";
        };
  X || Ht(Object.prototype, "toString", Zt, { unsafe: !0 });
  var te = {
      CSSRuleList: 0,
      CSSStyleDeclaration: 0,
      CSSValueList: 0,
      ClientRectList: 0,
      DOMRectList: 0,
      DOMStringList: 0,
      DOMTokenList: 1,
      DataTransferItemList: 0,
      FileList: 0,
      HTMLAllCollection: 0,
      HTMLCollection: 0,
      HTMLFormElement: 0,
      HTMLSelectElement: 0,
      MediaList: 0,
      MimeTypeArray: 0,
      NamedNodeMap: 0,
      NodeList: 1,
      PaintRequestList: 0,
      Plugin: 0,
      PluginArray: 0,
      SVGLengthList: 0,
      SVGNumberList: 0,
      SVGPathSegList: 0,
      SVGPointList: 0,
      SVGStringList: 0,
      SVGTransformList: 0,
      SourceBufferList: 0,
      StyleSheetList: 0,
      TextTrackCueList: 0,
      TextTrackList: 0,
      TouchList: 0,
    },
    ee = U("span").classList,
    re = ee && ee.constructor && ee.constructor.prototype,
    ne = re === Object.prototype ? void 0 : re,
    ie = g(g.bind),
    oe = function (t, e) {
      return (
        lt(t),
        void 0 === e
          ? t
          : f
          ? ie(t, e)
          : function () {
              return t.apply(e, arguments);
            }
      );
    },
    se = o.Object,
    ae = g("".split),
    ce = u(function () {
      return !se("z").propertyIsEnumerable(0);
    })
      ? function (t) {
          return "String" == Gt(t) ? ae(t, "") : se(t);
        }
      : se,
    le = Math.ceil,
    ue = Math.floor,
    fe =
      Math.trunc ||
      function (t) {
        var e = +t;
        return (e > 0 ? ue : le)(e);
      },
    he = function (t) {
      var e = +t;
      return e != e || 0 === e ? 0 : fe(e);
    },
    de = Math.min,
    pe = function (t) {
      return t > 0 ? de(he(t), 9007199254740991) : 0;
    },
    ve = function (t) {
      return pe(t.length);
    },
    ge =
      Array.isArray ||
      function (t) {
        return "Array" == Gt(t);
      },
    be = function () {},
    ye = [],
    me = R("Reflect", "construct"),
    xe = /^\s*(?:class|function)\b/,
    Ee = g(xe.exec),
    we = !xe.exec(be),
    Oe = function (t) {
      if (!T(t)) return !1;
      try {
        return me(be, ye, t), !0;
      } catch (t) {
        return !1;
      }
    },
    Se = function (t) {
      if (!T(t)) return !1;
      switch (Qt(t)) {
        case "AsyncFunction":
        case "GeneratorFunction":
        case "AsyncGeneratorFunction":
          return !1;
      }
      try {
        return we || !!Ee(xe, _t(t));
      } catch (t) {
        return !0;
      }
    };
  Se.sham = !0;
  var Ae =
      !me ||
      u(function () {
        var t;
        return (
          Oe(Oe.call) ||
          !Oe(Object) ||
          !Oe(function () {
            t = !0;
          }) ||
          t
        );
      })
        ? Se
        : Oe,
    ke = V("species"),
    Te = o.Array,
    Le = function (t, e) {
      return new ((function (t) {
        var e;
        return (
          ge(t) &&
            ((e = t.constructor),
            ((Ae(e) && (e === Te || ge(e.prototype))) ||
              (q(e) && null === (e = e[ke]))) &&
              (e = void 0)),
          void 0 === e ? Te : e
        );
      })(t))(0 === e ? 0 : e);
    },
    Re = g([].push),
    _e = function (t) {
      var e = 1 == t,
        r = 2 == t,
        n = 3 == t,
        i = 4 == t,
        o = 6 == t,
        s = 7 == t,
        a = 5 == t || o;
      return function (c, l, u, f) {
        for (
          var h,
            d,
            p = x(c),
            v = ce(p),
            g = oe(l, u),
            b = ve(v),
            y = 0,
            m = f || Le,
            E = e ? m(c, b) : r || s ? m(c, 0) : void 0;
          b > y;
          y++
        )
          if ((a || y in v) && ((d = g((h = v[y]), y, p)), t))
            if (e) E[y] = d;
            else if (d)
              switch (t) {
                case 3:
                  return !0;
                case 5:
                  return h;
                case 6:
                  return y;
                case 2:
                  Re(E, h);
              }
            else
              switch (t) {
                case 4:
                  return !1;
                case 7:
                  Re(E, h);
              }
        return o ? -1 : n || i ? i : E;
      };
    },
    je = {
      forEach: _e(0),
      map: _e(1),
      filter: _e(2),
      some: _e(3),
      every: _e(4),
      find: _e(5),
      findIndex: _e(6),
      filterReject: _e(7),
    },
    ze = function (t, e) {
      var r = [][t];
      return (
        !!r &&
        u(function () {
          r.call(
            null,
            e ||
              function () {
                return 1;
              },
            1
          );
        })
      );
    },
    Me = je.forEach,
    Ce = ze("forEach")
      ? [].forEach
      : function (t) {
          return Me(this, t, arguments.length > 1 ? arguments[1] : void 0);
        },
    Ne = function (t) {
      if (t && t.forEach !== Ce)
        try {
          Et(t, "forEach", Ce);
        } catch (e) {
          t.forEach = Ce;
        }
    };
  for (var We in te) te[We] && Ne(o[We] && o[We].prototype);
  Ne(ne);
  var Ie = !(
      "undefined" == typeof window ||
      !window.document ||
      !window.document.createElement
    ),
    Pe = {}.propertyIsEnumerable,
    Be = Object.getOwnPropertyDescriptor,
    De = {
      f:
        Be && !Pe.call({ 1: 2 }, 1)
          ? function (t) {
              var e = Be(this, t);
              return !!e && e.enumerable;
            }
          : Pe,
    },
    Fe = function (t) {
      return ce(y(t));
    },
    Ve = Object.getOwnPropertyDescriptor,
    $e = {
      f: H
        ? Ve
        : function (t, e) {
            if (((t = Fe(t)), (e = vt(e)), K))
              try {
                return Ve(t, e);
              } catch (t) {}
            if (w(t, e)) return xt(!rt(De.f, t, e), t[e]);
          },
    },
    Xe = Math.max,
    He = Math.min,
    qe = function (t, e) {
      var r = he(t);
      return r < 0 ? Xe(r + e, 0) : He(r, e);
    },
    Ye = function (t) {
      return function (e, r, n) {
        var i,
          o = Fe(e),
          s = ve(o),
          a = qe(n, s);
        if (t && r != r) {
          for (; s > a; ) if ((i = o[a++]) != i) return !0;
        } else
          for (; s > a; a++)
            if ((t || a in o) && o[a] === r) return t || a || 0;
        return !t && -1;
      };
    },
    Ge = { includes: Ye(!0), indexOf: Ye(!1) }.indexOf,
    Ue = g([].push),
    Ke = function (t, e) {
      var r,
        n = Fe(t),
        i = 0,
        o = [];
      for (r in n) !w(Nt, r) && w(n, r) && Ue(o, r);
      for (; e.length > i; ) w(n, (r = e[i++])) && (~Ge(o, r) || Ue(o, r));
      return o;
    },
    Je = [
      "constructor",
      "hasOwnProperty",
      "isPrototypeOf",
      "propertyIsEnumerable",
      "toLocaleString",
      "toString",
      "valueOf",
    ],
    Qe = Je.concat("length", "prototype"),
    Ze = {
      f:
        Object.getOwnPropertyNames ||
        function (t) {
          return Ke(t, Qe);
        },
    },
    tr = { f: Object.getOwnPropertySymbols },
    er = g([].concat),
    rr =
      R("Reflect", "ownKeys") ||
      function (t) {
        var e = Ze.f(tt(t)),
          r = tr.f;
        return r ? er(e, r(t)) : e;
      },
    nr = function (t, e, r) {
      for (var n = rr(e), i = mt.f, o = $e.f, s = 0; s < n.length; s++) {
        var a = n[s];
        w(t, a) || (r && w(r, a)) || i(t, a, o(e, a));
      }
    },
    ir = /#|\.prototype\./,
    or = function (t, e) {
      var r = ar[sr(t)];
      return r == lr || (r != cr && (T(e) ? u(e) : !!e));
    },
    sr = (or.normalize = function (t) {
      return String(t).replace(ir, ".").toLowerCase();
    }),
    ar = (or.data = {}),
    cr = (or.NATIVE = "N"),
    lr = (or.POLYFILL = "P"),
    ur = or,
    fr = $e.f,
    hr = function (t, e) {
      var r,
        n,
        i,
        s,
        c,
        l = t.target,
        u = t.global,
        f = t.stat;
      if ((r = u ? o : f ? o[l] || a(l, {}) : (o[l] || {}).prototype))
        for (n in e) {
          if (
            ((s = e[n]),
            (i = t.dontCallGetSet ? (c = fr(r, n)) && c.value : r[n]),
            !ur(u ? n : l + (f ? "." : "#") + n, t.forced) && void 0 !== i)
          ) {
            if (typeof s == typeof i) continue;
            nr(s, i);
          }
          (t.sham || (i && i.sham)) && Et(s, "sham", !0), Ht(r, n, s, t);
        }
    },
    dr = o.String,
    pr = function (t) {
      if ("Symbol" === Qt(t))
        throw TypeError("Cannot convert a Symbol value to a string");
      return dr(t);
    },
    vr = "\t\n\v\f\r                　\u2028\u2029\ufeff",
    gr = g("".replace),
    br = "[" + vr + "]",
    yr = RegExp("^" + br + br + "*"),
    mr = RegExp(br + br + "*$"),
    xr = function (t) {
      return function (e) {
        var r = pr(y(e));
        return 1 & t && (r = gr(r, yr, "")), 2 & t && (r = gr(r, mr, "")), r;
      };
    },
    Er = { start: xr(1), end: xr(2), trim: xr(3) }.trim,
    wr = o.parseInt,
    Or = o.Symbol,
    Sr = Or && Or.iterator,
    Ar = /^[+-]?0x/i,
    kr = g(Ar.exec),
    Tr =
      8 !== wr(vr + "08") ||
      22 !== wr(vr + "0x16") ||
      (Sr &&
        !u(function () {
          wr(Object(Sr));
        }))
        ? function (t, e) {
            var r = Er(pr(t));
            return wr(r, e >>> 0 || (kr(Ar, r) ? 16 : 10));
          }
        : wr;
  hr({ global: !0, forced: parseInt != Tr }, { parseInt: Tr });
  var Lr =
      Object.keys ||
      function (t) {
        return Ke(t, Je);
      },
    Rr = Object.assign,
    _r = Object.defineProperty,
    jr = g([].concat),
    zr =
      !Rr ||
      u(function () {
        if (
          H &&
          1 !==
            Rr(
              { b: 1 },
              Rr(
                _r({}, "a", {
                  enumerable: !0,
                  get: function () {
                    _r(this, "b", { value: 3, enumerable: !1 });
                  },
                }),
                { b: 2 }
              )
            ).b
        )
          return !0;
        var t = {},
          e = {},
          r = Symbol();
        return (
          (t[r] = 7),
          "abcdefghijklmnopqrst".split("").forEach(function (t) {
            e[t] = t;
          }),
          7 != Rr({}, t)[r] || "abcdefghijklmnopqrst" != Lr(Rr({}, e)).join("")
        );
      })
        ? function (t, e) {
            for (
              var r = x(t), n = arguments.length, i = 1, o = tr.f, s = De.f;
              n > i;

            )
              for (
                var a,
                  c = ce(arguments[i++]),
                  l = o ? jr(Lr(c), o(c)) : Lr(c),
                  u = l.length,
                  f = 0;
                u > f;

              )
                (a = l[f++]), (H && !rt(s, c, a)) || (r[a] = c[a]);
            return r;
          }
        : Rr;
  hr(
    { target: "Object", stat: !0, arity: 2, forced: Object.assign !== zr },
    { assign: zr }
  );
  var Mr,
    Cr = V("species"),
    Nr = je.filter,
    Wr =
      ((Mr = "filter"),
      N >= 51 ||
        !u(function () {
          var t = [];
          return (
            ((t.constructor = {})[Cr] = function () {
              return { foo: 1 };
            }),
            1 !== t[Mr](Boolean).foo
          );
        }));
  hr(
    { target: "Array", proto: !0, forced: !Wr },
    {
      filter: function (t) {
        return Nr(this, t, arguments.length > 1 ? arguments[1] : void 0);
      },
    }
  );
  var Ir,
    Pr = {
      f:
        H && !J
          ? Object.defineProperties
          : function (t, e) {
              tt(t);
              for (var r, n = Fe(e), i = Lr(e), o = i.length, s = 0; o > s; )
                mt.f(t, (r = i[s++]), n[r]);
              return t;
            },
    },
    Br = R("document", "documentElement"),
    Dr = Ct("IE_PROTO"),
    Fr = function () {},
    Vr = function (t) {
      return "<script>" + t + "</script>";
    },
    $r = function (t) {
      t.write(Vr("")), t.close();
      var e = t.parentWindow.Object;
      return (t = null), e;
    },
    Xr = function () {
      try {
        Ir = new ActiveXObject("htmlfile");
      } catch (t) {}
      var t, e;
      Xr =
        "undefined" != typeof document
          ? document.domain && Ir
            ? $r(Ir)
            : (((e = U("iframe")).style.display = "none"),
              Br.appendChild(e),
              (e.src = String("javascript:")),
              (t = e.contentWindow.document).open(),
              t.write(Vr("document.F=Object")),
              t.close(),
              t.F)
          : $r(Ir);
      for (var r = Je.length; r--; ) delete Xr.prototype[Je[r]];
      return Xr();
    };
  Nt[Dr] = !0;
  var Hr =
      Object.create ||
      function (t, e) {
        var r;
        return (
          null !== t
            ? ((Fr.prototype = tt(t)),
              (r = new Fr()),
              (Fr.prototype = null),
              (r[Dr] = t))
            : (r = Xr()),
          void 0 === e ? r : Pr.f(r, e)
        );
      },
    qr = mt.f,
    Yr = V("unscopables"),
    Gr = Array.prototype;
  null == Gr[Yr] && qr(Gr, Yr, { configurable: !0, value: Hr(null) });
  var Ur,
    Kr,
    Jr,
    Qr = function (t) {
      Gr[Yr][t] = !0;
    },
    Zr = {},
    tn = !u(function () {
      function t() {}
      return (
        (t.prototype.constructor = null),
        Object.getPrototypeOf(new t()) !== t.prototype
      );
    }),
    en = Ct("IE_PROTO"),
    rn = o.Object,
    nn = rn.prototype,
    on = tn
      ? rn.getPrototypeOf
      : function (t) {
          var e = x(t);
          if (w(e, en)) return e[en];
          var r = e.constructor;
          return T(r) && e instanceof r
            ? r.prototype
            : e instanceof rn
            ? nn
            : null;
        },
    sn = V("iterator"),
    an = !1;
  [].keys &&
    ("next" in (Jr = [].keys())
      ? (Kr = on(on(Jr))) !== Object.prototype && (Ur = Kr)
      : (an = !0)),
    (null == Ur ||
      u(function () {
        var t = {};
        return Ur[sn].call(t) !== t;
      })) &&
      (Ur = {}),
    T(Ur[sn]) ||
      Ht(Ur, sn, function () {
        return this;
      });
  var cn = { IteratorPrototype: Ur, BUGGY_SAFARI_ITERATORS: an },
    ln = mt.f,
    un = V("toStringTag"),
    fn = function (t, e, r) {
      t && !r && (t = t.prototype),
        t && !w(t, un) && ln(t, un, { configurable: !0, value: e });
    },
    hn = cn.IteratorPrototype,
    dn = function () {
      return this;
    },
    pn = o.String,
    vn = o.TypeError,
    gn =
      Object.setPrototypeOf ||
      ("__proto__" in {}
        ? (function () {
            var t,
              e = !1,
              r = {};
            try {
              (t = g(
                Object.getOwnPropertyDescriptor(Object.prototype, "__proto__")
                  .set
              ))(r, []),
                (e = r instanceof Array);
            } catch (t) {}
            return function (r, n) {
              return (
                tt(r),
                (function (t) {
                  if ("object" == typeof t || T(t)) return t;
                  throw vn("Can't set " + pn(t) + " as a prototype");
                })(n),
                e ? t(r, n) : (r.__proto__ = n),
                r
              );
            };
          })()
        : void 0),
    bn = At.PROPER,
    yn = At.CONFIGURABLE,
    mn = cn.IteratorPrototype,
    xn = cn.BUGGY_SAFARI_ITERATORS,
    En = V("iterator"),
    wn = function () {
      return this;
    },
    On = function (t, e, r, n, i, o, s) {
      !(function (t, e, r, n) {
        var i = e + " Iterator";
        (t.prototype = Hr(hn, { next: xt(+!n, r) })),
          fn(t, i, !1),
          (Zr[i] = dn);
      })(r, e, n);
      var a,
        c,
        l,
        u = function (t) {
          if (t === i && v) return v;
          if (!xn && t in d) return d[t];
          switch (t) {
            case "keys":
            case "values":
            case "entries":
              return function () {
                return new r(this, t);
              };
          }
          return function () {
            return new r(this);
          };
        },
        f = e + " Iterator",
        h = !1,
        d = t.prototype,
        p = d[En] || d["@@iterator"] || (i && d[i]),
        v = (!xn && p) || u(i),
        g = ("Array" == e && d.entries) || p;
      if (
        (g &&
          (a = on(g.call(new t()))) !== Object.prototype &&
          a.next &&
          (on(a) !== mn && (gn ? gn(a, mn) : T(a[En]) || Ht(a, En, wn)),
          fn(a, f, !0)),
        bn &&
          "values" == i &&
          p &&
          "values" !== p.name &&
          (yn
            ? Et(d, "name", "values")
            : ((h = !0),
              (v = function () {
                return rt(p, this);
              }))),
        i)
      )
        if (
          ((c = {
            values: u("values"),
            keys: o ? v : u("keys"),
            entries: u("entries"),
          }),
          s)
        )
          for (l in c) (xn || h || !(l in d)) && Ht(d, l, c[l]);
        else hr({ target: e, proto: !0, forced: xn || h }, c);
      return d[En] !== v && Ht(d, En, v, { name: i }), (Zr[e] = v), c;
    },
    Sn = mt.f,
    An = $t.set,
    kn = $t.getterFor("Array Iterator"),
    Tn = On(
      Array,
      "Array",
      function (t, e) {
        An(this, { type: "Array Iterator", target: Fe(t), index: 0, kind: e });
      },
      function () {
        var t = kn(this),
          e = t.target,
          r = t.kind,
          n = t.index++;
        return !e || n >= e.length
          ? ((t.target = void 0), { value: void 0, done: !0 })
          : "keys" == r
          ? { value: n, done: !1 }
          : "values" == r
          ? { value: e[n], done: !1 }
          : { value: [n, e[n]], done: !1 };
      },
      "values"
    ),
    Ln = (Zr.Arguments = Zr.Array);
  if ((Qr("keys"), Qr("values"), Qr("entries"), H && "values" !== Ln.name))
    try {
      Sn(Ln, "name", { value: "values" });
    } catch (t) {}
  var Rn = g("".charAt),
    _n = g("".charCodeAt),
    jn = g("".slice),
    zn = function (t) {
      return function (e, r) {
        var n,
          i,
          o = pr(y(e)),
          s = he(r),
          a = o.length;
        return s < 0 || s >= a
          ? t
            ? ""
            : void 0
          : (n = _n(o, s)) < 55296 ||
            n > 56319 ||
            s + 1 === a ||
            (i = _n(o, s + 1)) < 56320 ||
            i > 57343
          ? t
            ? Rn(o, s)
            : n
          : t
          ? jn(o, s, s + 2)
          : i - 56320 + ((n - 55296) << 10) + 65536;
      };
    },
    Mn = { codeAt: zn(!1), charAt: zn(!0) },
    Cn = Mn.charAt,
    Nn = $t.set,
    Wn = $t.getterFor("String Iterator");
  On(
    String,
    "String",
    function (t) {
      Nn(this, { type: "String Iterator", string: pr(t), index: 0 });
    },
    function () {
      var t,
        e = Wn(this),
        r = e.string,
        n = e.index;
      return n >= r.length
        ? { value: void 0, done: !0 }
        : ((t = Cn(r, n)), (e.index += t.length), { value: t, done: !1 });
    }
  );
  var In = function (t, e, r) {
      for (var n in e) Ht(t, n, e[n], r);
      return t;
    },
    Pn = o.Array,
    Bn = Math.max,
    Dn = Ze.f,
    Fn =
      "object" == typeof window && window && Object.getOwnPropertyNames
        ? Object.getOwnPropertyNames(window)
        : [],
    Vn = function (t) {
      try {
        return Dn(t);
      } catch (t) {
        return (function (t, e, r) {
          for (
            var n,
              i,
              o,
              s,
              a = ve(t),
              c = qe(e, a),
              l = qe(void 0 === r ? a : r, a),
              u = Pn(Bn(l - c, 0)),
              f = 0;
            c < l;
            c++, f++
          )
            (n = u),
              (i = f),
              (o = t[c]),
              (s = void 0),
              (s = vt(i)) in n ? mt.f(n, s, xt(0, o)) : (n[s] = o);
          return (u.length = f), u;
        })(Fn);
      }
    },
    $n = {
      f: function (t) {
        return Fn && "Window" == Gt(t) ? Vn(t) : Dn(Fe(t));
      },
    },
    Xn = u(function () {
      if ("function" == typeof ArrayBuffer) {
        var t = new ArrayBuffer(8);
        Object.isExtensible(t) && Object.defineProperty(t, "a", { value: 8 });
      }
    }),
    Hn = Object.isExtensible,
    qn =
      u(function () {
        Hn(1);
      }) || Xn
        ? function (t) {
            return !!q(t) && (!Xn || "ArrayBuffer" != Gt(t)) && (!Hn || Hn(t));
          }
        : Hn,
    Yn = !u(function () {
      return Object.isExtensible(Object.preventExtensions({}));
    }),
    Gn = e(function (t) {
      var e = mt.f,
        r = !1,
        n = k("meta"),
        i = 0,
        o = function (t) {
          e(t, n, { value: { objectID: "O" + i++, weakData: {} } });
        },
        s = (t.exports = {
          enable: function () {
            (s.enable = function () {}), (r = !0);
            var t = Ze.f,
              e = g([].splice),
              i = {};
            (i[n] = 1),
              t(i).length &&
                ((Ze.f = function (r) {
                  for (var i = t(r), o = 0, s = i.length; o < s; o++)
                    if (i[o] === n) {
                      e(i, o, 1);
                      break;
                    }
                  return i;
                }),
                hr(
                  { target: "Object", stat: !0, forced: !0 },
                  { getOwnPropertyNames: $n.f }
                ));
          },
          fastKey: function (t, e) {
            if (!q(t))
              return "symbol" == typeof t
                ? t
                : ("string" == typeof t ? "S" : "P") + t;
            if (!w(t, n)) {
              if (!qn(t)) return "F";
              if (!e) return "E";
              o(t);
            }
            return t[n].objectID;
          },
          getWeakData: function (t, e) {
            if (!w(t, n)) {
              if (!qn(t)) return !0;
              if (!e) return !1;
              o(t);
            }
            return t[n].weakData;
          },
          onFreeze: function (t) {
            return Yn && r && qn(t) && !w(t, n) && o(t), t;
          },
        });
      Nt[n] = !0;
    }),
    Un = (Gn.enable, Gn.fastKey, Gn.getWeakData, Gn.onFreeze, V("iterator")),
    Kn = Array.prototype,
    Jn = V("iterator"),
    Qn = function (t) {
      if (null != t) return ut(t, Jn) || ut(t, "@@iterator") || Zr[Qt(t)];
    },
    Zn = o.TypeError,
    ti = function (t, e, r) {
      var n, i;
      tt(t);
      try {
        if (!(n = ut(t, "return"))) {
          if ("throw" === e) throw r;
          return r;
        }
        n = rt(n, t);
      } catch (t) {
        (i = !0), (n = t);
      }
      if ("throw" === e) throw r;
      if (i) throw n;
      return tt(n), r;
    },
    ei = o.TypeError,
    ri = function (t, e) {
      (this.stopped = t), (this.result = e);
    },
    ni = ri.prototype,
    ii = function (t, e, r) {
      var n,
        i,
        o,
        s,
        a,
        c,
        l,
        u,
        f = r && r.that,
        h = !(!r || !r.AS_ENTRIES),
        d = !(!r || !r.IS_ITERATOR),
        p = !(!r || !r.INTERRUPTED),
        v = oe(e, f),
        g = function (t) {
          return n && ti(n, "normal", t), new ri(!0, t);
        },
        b = function (t) {
          return h
            ? (tt(t), p ? v(t[0], t[1], g) : v(t[0], t[1]))
            : p
            ? v(t, g)
            : v(t);
        };
      if (d) n = t;
      else {
        if (!(i = Qn(t))) throw ei(at(t) + " is not iterable");
        if (void 0 !== (u = i) && (Zr.Array === u || Kn[Un] === u)) {
          for (o = 0, s = ve(t); s > o; o++)
            if ((a = b(t[o])) && nt(ni, a)) return a;
          return new ri(!1);
        }
        n = (function (t, e) {
          var r = arguments.length < 2 ? Qn(t) : e;
          if (lt(r)) return tt(rt(r, t));
          throw Zn(at(t) + " is not iterable");
        })(t, i);
      }
      for (c = n.next; !(l = rt(c, n)).done; ) {
        try {
          a = b(l.value);
        } catch (t) {
          ti(n, "throw", t);
        }
        if ("object" == typeof a && a && nt(ni, a)) return a;
      }
      return new ri(!1);
    },
    oi = o.TypeError,
    si = function (t, e) {
      if (nt(e, t)) return t;
      throw oi("Incorrect invocation");
    },
    ai = V("iterator"),
    ci = !1;
  try {
    var li = 0,
      ui = {
        next: function () {
          return { done: !!li++ };
        },
        return: function () {
          ci = !0;
        },
      };
    (ui[ai] = function () {
      return this;
    }),
      Array.from(ui, function () {
        throw 2;
      });
  } catch (t) {}
  var fi = Gn.getWeakData,
    hi = $t.set,
    di = $t.getterFor,
    pi = je.find,
    vi = je.findIndex,
    gi = g([].splice),
    bi = 0,
    yi = function (t) {
      return t.frozen || (t.frozen = new mi());
    },
    mi = function () {
      this.entries = [];
    },
    xi = function (t, e) {
      return pi(t.entries, function (t) {
        return t[0] === e;
      });
    };
  mi.prototype = {
    get: function (t) {
      var e = xi(this, t);
      if (e) return e[1];
    },
    has: function (t) {
      return !!xi(this, t);
    },
    set: function (t, e) {
      var r = xi(this, t);
      r ? (r[1] = e) : this.entries.push([t, e]);
    },
    delete: function (t) {
      var e = vi(this.entries, function (e) {
        return e[0] === t;
      });
      return ~e && gi(this.entries, e, 1), !!~e;
    },
  };
  var Ei,
    wi = {
      getConstructor: function (t, e, r, n) {
        var i = t(function (t, i) {
            si(t, o),
              hi(t, { type: e, id: bi++, frozen: void 0 }),
              null != i && ii(i, t[n], { that: t, AS_ENTRIES: r });
          }),
          o = i.prototype,
          s = di(e),
          a = function (t, e, r) {
            var n = s(t),
              i = fi(tt(e), !0);
            return !0 === i ? yi(n).set(e, r) : (i[n.id] = r), t;
          };
        return (
          In(o, {
            delete: function (t) {
              var e = s(this);
              if (!q(t)) return !1;
              var r = fi(t);
              return !0 === r
                ? yi(e).delete(t)
                : r && w(r, e.id) && delete r[e.id];
            },
            has: function (t) {
              var e = s(this);
              if (!q(t)) return !1;
              var r = fi(t);
              return !0 === r ? yi(e).has(t) : r && w(r, e.id);
            },
          }),
          In(
            o,
            r
              ? {
                  get: function (t) {
                    var e = s(this);
                    if (q(t)) {
                      var r = fi(t);
                      return !0 === r ? yi(e).get(t) : r ? r[e.id] : void 0;
                    }
                  },
                  set: function (t, e) {
                    return a(this, t, e);
                  },
                }
              : {
                  add: function (t) {
                    return a(this, t, !0);
                  },
                }
          ),
          i
        );
      },
    },
    Oi = $t.enforce,
    Si = !o.ActiveXObject && "ActiveXObject" in o,
    Ai = function (t) {
      return function () {
        return t(this, arguments.length ? arguments[0] : void 0);
      };
    },
    ki = (function (t, e, r) {
      var n = -1 !== t.indexOf("Map"),
        i = -1 !== t.indexOf("Weak"),
        s = n ? "set" : "add",
        a = o[t],
        c = a && a.prototype,
        l = a,
        f = {},
        h = function (t) {
          var e = g(c[t]);
          Ht(
            c,
            t,
            "add" == t
              ? function (t) {
                  return e(this, 0 === t ? 0 : t), this;
                }
              : "delete" == t
              ? function (t) {
                  return !(i && !q(t)) && e(this, 0 === t ? 0 : t);
                }
              : "get" == t
              ? function (t) {
                  return i && !q(t) ? void 0 : e(this, 0 === t ? 0 : t);
                }
              : "has" == t
              ? function (t) {
                  return !(i && !q(t)) && e(this, 0 === t ? 0 : t);
                }
              : function (t, r) {
                  return e(this, 0 === t ? 0 : t, r), this;
                }
          );
        };
      if (
        ur(
          t,
          !T(a) ||
            !(
              i ||
              (c.forEach &&
                !u(function () {
                  new a().entries().next();
                }))
            )
        )
      )
        (l = r.getConstructor(e, t, n, s)), Gn.enable();
      else if (ur(t, !0)) {
        var d = new l(),
          p = d[s](i ? {} : -0, 1) != d,
          v = u(function () {
            d.has(1);
          }),
          b = (function (t, e) {
            if (!e && !ci) return !1;
            var r = !1;
            try {
              var n = {};
              (n[ai] = function () {
                return {
                  next: function () {
                    return { done: (r = !0) };
                  },
                };
              }),
                t(n);
            } catch (t) {}
            return r;
          })(function (t) {
            new a(t);
          }),
          y =
            !i &&
            u(function () {
              for (var t = new a(), e = 5; e--; ) t[s](e, e);
              return !t.has(-0);
            });
        b ||
          (((l = e(function (t, e) {
            si(t, c);
            var r = (function (t, e, r) {
              var n, i;
              return (
                gn &&
                  T((n = e.constructor)) &&
                  n !== r &&
                  q((i = n.prototype)) &&
                  i !== r.prototype &&
                  gn(t, i),
                t
              );
            })(new a(), t, l);
            return null != e && ii(e, r[s], { that: r, AS_ENTRIES: n }), r;
          })).prototype = c),
          (c.constructor = l)),
          (v || y) && (h("delete"), h("has"), n && h("get")),
          (y || p) && h(s),
          i && c.clear && delete c.clear;
      }
      return (
        (f[t] = l),
        hr({ global: !0, constructor: !0, forced: l != a }, f),
        fn(l, t),
        i || r.setStrong(l, t, n),
        l
      );
    })("WeakMap", Ai, wi);
  if (zt && Si) {
    (Ei = wi.getConstructor(Ai, "WeakMap", !0)), Gn.enable();
    var Ti = ki.prototype,
      Li = g(Ti.delete),
      Ri = g(Ti.has),
      _i = g(Ti.get),
      ji = g(Ti.set);
    In(Ti, {
      delete: function (t) {
        if (q(t) && !qn(t)) {
          var e = Oi(this);
          return (
            e.frozen || (e.frozen = new Ei()), Li(this, t) || e.frozen.delete(t)
          );
        }
        return Li(this, t);
      },
      has: function (t) {
        if (q(t) && !qn(t)) {
          var e = Oi(this);
          return (
            e.frozen || (e.frozen = new Ei()), Ri(this, t) || e.frozen.has(t)
          );
        }
        return Ri(this, t);
      },
      get: function (t) {
        if (q(t) && !qn(t)) {
          var e = Oi(this);
          return (
            e.frozen || (e.frozen = new Ei()),
            Ri(this, t) ? _i(this, t) : e.frozen.get(t)
          );
        }
        return _i(this, t);
      },
      set: function (t, e) {
        if (q(t) && !qn(t)) {
          var r = Oi(this);
          r.frozen || (r.frozen = new Ei()),
            Ri(this, t) ? ji(this, t, e) : r.frozen.set(t, e);
        } else ji(this, t, e);
        return this;
      },
    });
  }
  var zi = V("iterator"),
    Mi = V("toStringTag"),
    Ci = Tn.values,
    Ni = function (t, e) {
      if (t) {
        if (t[zi] !== Ci)
          try {
            Et(t, zi, Ci);
          } catch (e) {
            t[zi] = Ci;
          }
        if ((t[Mi] || Et(t, Mi, e), te[e]))
          for (var r in Tn)
            if (t[r] !== Tn[r])
              try {
                Et(t, r, Tn[r]);
              } catch (e) {
                t[r] = Tn[r];
              }
      }
    };
  for (var Wi in te) Ni(o[Wi] && o[Wi].prototype, Wi);
  Ni(ne, "DOMTokenList");
  var Ii = /^\s+|\s+$/g,
    Pi = /^[-+]0x[0-9a-f]+$/i,
    Bi = /^0b[01]+$/i,
    Di = /^0o[0-7]+$/i,
    Fi = parseInt,
    Vi = "object" == typeof t && t && t.Object === Object && t,
    $i = "object" == typeof self && self && self.Object === Object && self,
    Xi = Vi || $i || Function("return this")(),
    Hi = Object.prototype.toString,
    qi = Math.max,
    Yi = Math.min,
    Gi = function () {
      return Xi.Date.now();
    };
  function Ui(t, e, r) {
    var n,
      i,
      o,
      s,
      a,
      c,
      l = 0,
      u = !1,
      f = !1,
      h = !0;
    if ("function" != typeof t) throw new TypeError("Expected a function");
    function d(e) {
      var r = n,
        o = i;
      return (n = i = void 0), (l = e), (s = t.apply(o, r));
    }
    function p(t) {
      return (l = t), (a = setTimeout(g, e)), u ? d(t) : s;
    }
    function v(t) {
      var r = t - c;
      return void 0 === c || r >= e || r < 0 || (f && t - l >= o);
    }
    function g() {
      var t = Gi();
      if (v(t)) return b(t);
      a = setTimeout(
        g,
        (function (t) {
          var r = e - (t - c);
          return f ? Yi(r, o - (t - l)) : r;
        })(t)
      );
    }
    function b(t) {
      return (a = void 0), h && n ? d(t) : ((n = i = void 0), s);
    }
    function y() {
      var t = Gi(),
        r = v(t);
      if (((n = arguments), (i = this), (c = t), r)) {
        if (void 0 === a) return p(c);
        if (f) return (a = setTimeout(g, e)), d(c);
      }
      return void 0 === a && (a = setTimeout(g, e)), s;
    }
    return (
      (e = Ji(e) || 0),
      Ki(r) &&
        ((u = !!r.leading),
        (o = (f = "maxWait" in r) ? qi(Ji(r.maxWait) || 0, e) : o),
        (h = "trailing" in r ? !!r.trailing : h)),
      (y.cancel = function () {
        void 0 !== a && clearTimeout(a), (l = 0), (n = c = i = a = void 0);
      }),
      (y.flush = function () {
        return void 0 === a ? s : b(Gi());
      }),
      y
    );
  }
  function Ki(t) {
    var e = typeof t;
    return !!t && ("object" == e || "function" == e);
  }
  function Ji(t) {
    if ("number" == typeof t) return t;
    if (
      (function (t) {
        return (
          "symbol" == typeof t ||
          ((function (t) {
            return !!t && "object" == typeof t;
          })(t) &&
            "[object Symbol]" == Hi.call(t))
        );
      })(t)
    )
      return NaN;
    if (Ki(t)) {
      var e = "function" == typeof t.valueOf ? t.valueOf() : t;
      t = Ki(e) ? e + "" : e;
    }
    if ("string" != typeof t) return 0 === t ? t : +t;
    t = t.replace(Ii, "");
    var r = Bi.test(t);
    return r || Di.test(t) ? Fi(t.slice(2), r ? 2 : 8) : Pi.test(t) ? NaN : +t;
  }
  var Qi = function (t, e, r) {
      var n = !0,
        i = !0;
      if ("function" != typeof t) throw new TypeError("Expected a function");
      return (
        Ki(r) &&
          ((n = "leading" in r ? !!r.leading : n),
          (i = "trailing" in r ? !!r.trailing : i)),
        Ui(t, e, { leading: n, maxWait: e, trailing: i })
      );
    },
    Zi = /^\s+|\s+$/g,
    to = /^[-+]0x[0-9a-f]+$/i,
    eo = /^0b[01]+$/i,
    ro = /^0o[0-7]+$/i,
    no = parseInt,
    io = "object" == typeof t && t && t.Object === Object && t,
    oo = "object" == typeof self && self && self.Object === Object && self,
    so = io || oo || Function("return this")(),
    ao = Object.prototype.toString,
    co = Math.max,
    lo = Math.min,
    uo = function () {
      return so.Date.now();
    };
  function fo(t) {
    var e = typeof t;
    return !!t && ("object" == e || "function" == e);
  }
  function ho(t) {
    if ("number" == typeof t) return t;
    if (
      (function (t) {
        return (
          "symbol" == typeof t ||
          ((function (t) {
            return !!t && "object" == typeof t;
          })(t) &&
            "[object Symbol]" == ao.call(t))
        );
      })(t)
    )
      return NaN;
    if (fo(t)) {
      var e = "function" == typeof t.valueOf ? t.valueOf() : t;
      t = fo(e) ? e + "" : e;
    }
    if ("string" != typeof t) return 0 === t ? t : +t;
    t = t.replace(Zi, "");
    var r = eo.test(t);
    return r || ro.test(t) ? no(t.slice(2), r ? 2 : 8) : to.test(t) ? NaN : +t;
  }
  var po = function (t, e, r) {
      var n,
        i,
        o,
        s,
        a,
        c,
        l = 0,
        u = !1,
        f = !1,
        h = !0;
      if ("function" != typeof t) throw new TypeError("Expected a function");
      function d(e) {
        var r = n,
          o = i;
        return (n = i = void 0), (l = e), (s = t.apply(o, r));
      }
      function p(t) {
        return (l = t), (a = setTimeout(g, e)), u ? d(t) : s;
      }
      function v(t) {
        var r = t - c;
        return void 0 === c || r >= e || r < 0 || (f && t - l >= o);
      }
      function g() {
        var t = uo();
        if (v(t)) return b(t);
        a = setTimeout(
          g,
          (function (t) {
            var r = e - (t - c);
            return f ? lo(r, o - (t - l)) : r;
          })(t)
        );
      }
      function b(t) {
        return (a = void 0), h && n ? d(t) : ((n = i = void 0), s);
      }
      function y() {
        var t = uo(),
          r = v(t);
        if (((n = arguments), (i = this), (c = t), r)) {
          if (void 0 === a) return p(c);
          if (f) return (a = setTimeout(g, e)), d(c);
        }
        return void 0 === a && (a = setTimeout(g, e)), s;
      }
      return (
        (e = ho(e) || 0),
        fo(r) &&
          ((u = !!r.leading),
          (o = (f = "maxWait" in r) ? co(ho(r.maxWait) || 0, e) : o),
          (h = "trailing" in r ? !!r.trailing : h)),
        (y.cancel = function () {
          void 0 !== a && clearTimeout(a), (l = 0), (n = c = i = a = void 0);
        }),
        (y.flush = function () {
          return void 0 === a ? s : b(uo());
        }),
        y
      );
    },
    vo = /^\[object .+?Constructor\]$/,
    go = "object" == typeof t && t && t.Object === Object && t,
    bo = "object" == typeof self && self && self.Object === Object && self,
    yo = go || bo || Function("return this")();
  var mo = Array.prototype,
    xo = Function.prototype,
    Eo = Object.prototype,
    wo = yo["__core-js_shared__"],
    Oo = (function () {
      var t = /[^.]+$/.exec((wo && wo.keys && wo.keys.IE_PROTO) || "");
      return t ? "Symbol(src)_1." + t : "";
    })(),
    So = xo.toString,
    Ao = Eo.hasOwnProperty,
    ko = Eo.toString,
    To = RegExp(
      "^" +
        So.call(Ao)
          .replace(/[\\^$.*+?()[\]{}|]/g, "\\$&")
          .replace(
            /hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,
            "$1.*?"
          ) +
        "$"
    ),
    Lo = mo.splice,
    Ro = Io(yo, "Map"),
    _o = Io(Object, "create");
  function jo(t) {
    var e = -1,
      r = t ? t.length : 0;
    for (this.clear(); ++e < r; ) {
      var n = t[e];
      this.set(n[0], n[1]);
    }
  }
  function zo(t) {
    var e = -1,
      r = t ? t.length : 0;
    for (this.clear(); ++e < r; ) {
      var n = t[e];
      this.set(n[0], n[1]);
    }
  }
  function Mo(t) {
    var e = -1,
      r = t ? t.length : 0;
    for (this.clear(); ++e < r; ) {
      var n = t[e];
      this.set(n[0], n[1]);
    }
  }
  function Co(t, e) {
    for (var r, n, i = t.length; i--; )
      if ((r = t[i][0]) === (n = e) || (r != r && n != n)) return i;
    return -1;
  }
  function No(t) {
    return (
      !(!Bo(t) || ((e = t), Oo && Oo in e)) &&
      ((function (t) {
        var e = Bo(t) ? ko.call(t) : "";
        return "[object Function]" == e || "[object GeneratorFunction]" == e;
      })(t) ||
      (function (t) {
        var e = !1;
        if (null != t && "function" != typeof t.toString)
          try {
            e = !!(t + "");
          } catch (t) {}
        return e;
      })(t)
        ? To
        : vo
      ).test(
        (function (t) {
          if (null != t) {
            try {
              return So.call(t);
            } catch (t) {}
            try {
              return t + "";
            } catch (t) {}
          }
          return "";
        })(t)
      )
    );
    var e;
  }
  function Wo(t, e) {
    var r,
      n,
      i = t.__data__;
    return (
      "string" == (n = typeof (r = e)) ||
      "number" == n ||
      "symbol" == n ||
      "boolean" == n
        ? "__proto__" !== r
        : null === r
    )
      ? i["string" == typeof e ? "string" : "hash"]
      : i.map;
  }
  function Io(t, e) {
    var r = (function (t, e) {
      return null == t ? void 0 : t[e];
    })(t, e);
    return No(r) ? r : void 0;
  }
  function Po(t, e) {
    if ("function" != typeof t || (e && "function" != typeof e))
      throw new TypeError("Expected a function");
    var r = function () {
      var n = arguments,
        i = e ? e.apply(this, n) : n[0],
        o = r.cache;
      if (o.has(i)) return o.get(i);
      var s = t.apply(this, n);
      return (r.cache = o.set(i, s)), s;
    };
    return (r.cache = new (Po.Cache || Mo)()), r;
  }
  function Bo(t) {
    var e = typeof t;
    return !!t && ("object" == e || "function" == e);
  }
  (jo.prototype.clear = function () {
    this.__data__ = _o ? _o(null) : {};
  }),
    (jo.prototype.delete = function (t) {
      return this.has(t) && delete this.__data__[t];
    }),
    (jo.prototype.get = function (t) {
      var e = this.__data__;
      if (_o) {
        var r = e[t];
        return "__lodash_hash_undefined__" === r ? void 0 : r;
      }
      return Ao.call(e, t) ? e[t] : void 0;
    }),
    (jo.prototype.has = function (t) {
      var e = this.__data__;
      return _o ? void 0 !== e[t] : Ao.call(e, t);
    }),
    (jo.prototype.set = function (t, e) {
      return (
        (this.__data__[t] =
          _o && void 0 === e ? "__lodash_hash_undefined__" : e),
        this
      );
    }),
    (zo.prototype.clear = function () {
      this.__data__ = [];
    }),
    (zo.prototype.delete = function (t) {
      var e = this.__data__,
        r = Co(e, t);
      return !(r < 0) && (r == e.length - 1 ? e.pop() : Lo.call(e, r, 1), !0);
    }),
    (zo.prototype.get = function (t) {
      var e = this.__data__,
        r = Co(e, t);
      return r < 0 ? void 0 : e[r][1];
    }),
    (zo.prototype.has = function (t) {
      return Co(this.__data__, t) > -1;
    }),
    (zo.prototype.set = function (t, e) {
      var r = this.__data__,
        n = Co(r, t);
      return n < 0 ? r.push([t, e]) : (r[n][1] = e), this;
    }),
    (Mo.prototype.clear = function () {
      this.__data__ = {
        hash: new jo(),
        map: new (Ro || zo)(),
        string: new jo(),
      };
    }),
    (Mo.prototype.delete = function (t) {
      return Wo(this, t).delete(t);
    }),
    (Mo.prototype.get = function (t) {
      return Wo(this, t).get(t);
    }),
    (Mo.prototype.has = function (t) {
      return Wo(this, t).has(t);
    }),
    (Mo.prototype.set = function (t, e) {
      return Wo(this, t).set(t, e), this;
    }),
    (Po.Cache = Mo);
  var Do,
    Fo = Po,
    Vo = [],
    $o = "ResizeObserver loop completed with undelivered notifications.";
  !(function (t) {
    (t.BORDER_BOX = "border-box"),
      (t.CONTENT_BOX = "content-box"),
      (t.DEVICE_PIXEL_CONTENT_BOX = "device-pixel-content-box");
  })(Do || (Do = {}));
  var Xo,
    Ho = function (t) {
      return Object.freeze(t);
    },
    qo = function (t, e) {
      (this.inlineSize = t), (this.blockSize = e), Ho(this);
    },
    Yo = (function () {
      function t(t, e, r, n) {
        return (
          (this.x = t),
          (this.y = e),
          (this.width = r),
          (this.height = n),
          (this.top = this.y),
          (this.left = this.x),
          (this.bottom = this.top + this.height),
          (this.right = this.left + this.width),
          Ho(this)
        );
      }
      return (
        (t.prototype.toJSON = function () {
          var t = this;
          return {
            x: t.x,
            y: t.y,
            top: t.top,
            right: t.right,
            bottom: t.bottom,
            left: t.left,
            width: t.width,
            height: t.height,
          };
        }),
        (t.fromRect = function (e) {
          return new t(e.x, e.y, e.width, e.height);
        }),
        t
      );
    })(),
    Go = function (t) {
      return t instanceof SVGElement && "getBBox" in t;
    },
    Uo = function (t) {
      if (Go(t)) {
        var e = t.getBBox(),
          r = e.width,
          n = e.height;
        return !r && !n;
      }
      var i = t,
        o = i.offsetWidth,
        s = i.offsetHeight;
      return !(o || s || t.getClientRects().length);
    },
    Ko = function (t) {
      var e, r;
      if (t instanceof Element) return !0;
      var n =
        null ===
          (r = null === (e = t) || void 0 === e ? void 0 : e.ownerDocument) ||
        void 0 === r
          ? void 0
          : r.defaultView;
      return !!(n && t instanceof n.Element);
    },
    Jo = "undefined" != typeof window ? window : {},
    Qo = new WeakMap(),
    Zo = /auto|scroll/,
    ts = /^tb|vertical/,
    es = /msie|trident/i.test(Jo.navigator && Jo.navigator.userAgent),
    rs = function (t) {
      return parseFloat(t || "0");
    },
    ns = function (t, e, r) {
      return (
        void 0 === t && (t = 0),
        void 0 === e && (e = 0),
        void 0 === r && (r = !1),
        new qo((r ? e : t) || 0, (r ? t : e) || 0)
      );
    },
    is = Ho({
      devicePixelContentBoxSize: ns(),
      borderBoxSize: ns(),
      contentBoxSize: ns(),
      contentRect: new Yo(0, 0, 0, 0),
    }),
    os = function (t, e) {
      if ((void 0 === e && (e = !1), Qo.has(t) && !e)) return Qo.get(t);
      if (Uo(t)) return Qo.set(t, is), is;
      var r = getComputedStyle(t),
        n = Go(t) && t.ownerSVGElement && t.getBBox(),
        i = !es && "border-box" === r.boxSizing,
        o = ts.test(r.writingMode || ""),
        s = !n && Zo.test(r.overflowY || ""),
        a = !n && Zo.test(r.overflowX || ""),
        c = n ? 0 : rs(r.paddingTop),
        l = n ? 0 : rs(r.paddingRight),
        u = n ? 0 : rs(r.paddingBottom),
        f = n ? 0 : rs(r.paddingLeft),
        h = n ? 0 : rs(r.borderTopWidth),
        d = n ? 0 : rs(r.borderRightWidth),
        p = n ? 0 : rs(r.borderBottomWidth),
        v = f + l,
        g = c + u,
        b = (n ? 0 : rs(r.borderLeftWidth)) + d,
        y = h + p,
        m = a ? t.offsetHeight - y - t.clientHeight : 0,
        x = s ? t.offsetWidth - b - t.clientWidth : 0,
        E = i ? v + b : 0,
        w = i ? g + y : 0,
        O = n ? n.width : rs(r.width) - E - x,
        S = n ? n.height : rs(r.height) - w - m,
        A = O + v + x + b,
        k = S + g + m + y,
        T = Ho({
          devicePixelContentBoxSize: ns(
            Math.round(O * devicePixelRatio),
            Math.round(S * devicePixelRatio),
            o
          ),
          borderBoxSize: ns(A, k, o),
          contentBoxSize: ns(O, S, o),
          contentRect: new Yo(f, c, O, S),
        });
      return Qo.set(t, T), T;
    },
    ss = function (t, e, r) {
      var n = os(t, r),
        i = n.borderBoxSize,
        o = n.contentBoxSize,
        s = n.devicePixelContentBoxSize;
      switch (e) {
        case Do.DEVICE_PIXEL_CONTENT_BOX:
          return s;
        case Do.BORDER_BOX:
          return i;
        default:
          return o;
      }
    },
    as = function (t) {
      var e = os(t);
      (this.target = t),
        (this.contentRect = e.contentRect),
        (this.borderBoxSize = Ho([e.borderBoxSize])),
        (this.contentBoxSize = Ho([e.contentBoxSize])),
        (this.devicePixelContentBoxSize = Ho([e.devicePixelContentBoxSize]));
    },
    cs = function (t) {
      if (Uo(t)) return 1 / 0;
      for (var e = 0, r = t.parentNode; r; ) (e += 1), (r = r.parentNode);
      return e;
    },
    ls = function () {
      var t = 1 / 0,
        e = [];
      Vo.forEach(function (r) {
        if (0 !== r.activeTargets.length) {
          var n = [];
          r.activeTargets.forEach(function (e) {
            var r = new as(e.target),
              i = cs(e.target);
            n.push(r),
              (e.lastReportedSize = ss(e.target, e.observedBox)),
              i < t && (t = i);
          }),
            e.push(function () {
              r.callback.call(r.observer, n, r.observer);
            }),
            r.activeTargets.splice(0, r.activeTargets.length);
        }
      });
      for (var r = 0, n = e; r < n.length; r++) {
        (0, n[r])();
      }
      return t;
    },
    us = function (t) {
      Vo.forEach(function (e) {
        e.activeTargets.splice(0, e.activeTargets.length),
          e.skippedTargets.splice(0, e.skippedTargets.length),
          e.observationTargets.forEach(function (r) {
            r.isActive() &&
              (cs(r.target) > t
                ? e.activeTargets.push(r)
                : e.skippedTargets.push(r));
          });
      });
    },
    fs = function () {
      var t,
        e = 0;
      for (
        us(e);
        Vo.some(function (t) {
          return t.activeTargets.length > 0;
        });

      )
        (e = ls()), us(e);
      return (
        Vo.some(function (t) {
          return t.skippedTargets.length > 0;
        }) &&
          ("function" == typeof ErrorEvent
            ? (t = new ErrorEvent("error", { message: $o }))
            : ((t = document.createEvent("Event")).initEvent("error", !1, !1),
              (t.message = $o)),
          window.dispatchEvent(t)),
        e > 0
      );
    },
    hs = [],
    ds = function (t) {
      if (!Xo) {
        var e = 0,
          r = document.createTextNode("");
        new MutationObserver(function () {
          return hs.splice(0).forEach(function (t) {
            return t();
          });
        }).observe(r, { characterData: !0 }),
          (Xo = function () {
            r.textContent = "" + (e ? e-- : e++);
          });
      }
      hs.push(t), Xo();
    },
    ps = 0,
    vs = { attributes: !0, characterData: !0, childList: !0, subtree: !0 },
    gs = [
      "resize",
      "load",
      "transitionend",
      "animationend",
      "animationstart",
      "animationiteration",
      "keyup",
      "keydown",
      "mouseup",
      "mousedown",
      "mouseover",
      "mouseout",
      "blur",
      "focus",
    ],
    bs = function (t) {
      return void 0 === t && (t = 0), Date.now() + t;
    },
    ys = !1,
    ms = new ((function () {
      function t() {
        var t = this;
        (this.stopped = !0),
          (this.listener = function () {
            return t.schedule();
          });
      }
      return (
        (t.prototype.run = function (t) {
          var e = this;
          if ((void 0 === t && (t = 250), !ys)) {
            ys = !0;
            var r,
              n = bs(t);
            (r = function () {
              var r = !1;
              try {
                r = fs();
              } finally {
                if (((ys = !1), (t = n - bs()), !ps)) return;
                r ? e.run(1e3) : t > 0 ? e.run(t) : e.start();
              }
            }),
              ds(function () {
                requestAnimationFrame(r);
              });
          }
        }),
        (t.prototype.schedule = function () {
          this.stop(), this.run();
        }),
        (t.prototype.observe = function () {
          var t = this,
            e = function () {
              return t.observer && t.observer.observe(document.body, vs);
            };
          document.body ? e() : Jo.addEventListener("DOMContentLoaded", e);
        }),
        (t.prototype.start = function () {
          var t = this;
          this.stopped &&
            ((this.stopped = !1),
            (this.observer = new MutationObserver(this.listener)),
            this.observe(),
            gs.forEach(function (e) {
              return Jo.addEventListener(e, t.listener, !0);
            }));
        }),
        (t.prototype.stop = function () {
          var t = this;
          this.stopped ||
            (this.observer && this.observer.disconnect(),
            gs.forEach(function (e) {
              return Jo.removeEventListener(e, t.listener, !0);
            }),
            (this.stopped = !0));
        }),
        t
      );
    })())(),
    xs = function (t) {
      !ps && t > 0 && ms.start(), !(ps += t) && ms.stop();
    },
    Es = (function () {
      function t(t, e) {
        (this.target = t),
          (this.observedBox = e || Do.CONTENT_BOX),
          (this.lastReportedSize = { inlineSize: 0, blockSize: 0 });
      }
      return (
        (t.prototype.isActive = function () {
          var t,
            e = ss(this.target, this.observedBox, !0);
          return (
            (t = this.target),
            Go(t) ||
              (function (t) {
                switch (t.tagName) {
                  case "INPUT":
                    if ("image" !== t.type) break;
                  case "VIDEO":
                  case "AUDIO":
                  case "EMBED":
                  case "OBJECT":
                  case "CANVAS":
                  case "IFRAME":
                  case "IMG":
                    return !0;
                }
                return !1;
              })(t) ||
              "inline" !== getComputedStyle(t).display ||
              (this.lastReportedSize = e),
            this.lastReportedSize.inlineSize !== e.inlineSize ||
              this.lastReportedSize.blockSize !== e.blockSize
          );
        }),
        t
      );
    })(),
    ws = function (t, e) {
      (this.activeTargets = []),
        (this.skippedTargets = []),
        (this.observationTargets = []),
        (this.observer = t),
        (this.callback = e);
    },
    Os = new WeakMap(),
    Ss = function (t, e) {
      for (var r = 0; r < t.length; r += 1) if (t[r].target === e) return r;
      return -1;
    },
    As = (function () {
      function t() {}
      return (
        (t.connect = function (t, e) {
          var r = new ws(t, e);
          Os.set(t, r);
        }),
        (t.observe = function (t, e, r) {
          var n = Os.get(t),
            i = 0 === n.observationTargets.length;
          Ss(n.observationTargets, e) < 0 &&
            (i && Vo.push(n),
            n.observationTargets.push(new Es(e, r && r.box)),
            xs(1),
            ms.schedule());
        }),
        (t.unobserve = function (t, e) {
          var r = Os.get(t),
            n = Ss(r.observationTargets, e),
            i = 1 === r.observationTargets.length;
          n >= 0 &&
            (i && Vo.splice(Vo.indexOf(r), 1),
            r.observationTargets.splice(n, 1),
            xs(-1));
        }),
        (t.disconnect = function (t) {
          var e = this,
            r = Os.get(t);
          r.observationTargets.slice().forEach(function (r) {
            return e.unobserve(t, r.target);
          }),
            r.activeTargets.splice(0, r.activeTargets.length);
        }),
        t
      );
    })(),
    ks = (function () {
      function t(t) {
        if (0 === arguments.length)
          throw new TypeError(
            "Failed to construct 'ResizeObserver': 1 argument required, but only 0 present."
          );
        if ("function" != typeof t)
          throw new TypeError(
            "Failed to construct 'ResizeObserver': The callback provided as parameter 1 is not a function."
          );
        As.connect(this, t);
      }
      return (
        (t.prototype.observe = function (t, e) {
          if (0 === arguments.length)
            throw new TypeError(
              "Failed to execute 'observe' on 'ResizeObserver': 1 argument required, but only 0 present."
            );
          if (!Ko(t))
            throw new TypeError(
              "Failed to execute 'observe' on 'ResizeObserver': parameter 1 is not of type 'Element"
            );
          As.observe(this, t, e);
        }),
        (t.prototype.unobserve = function (t) {
          if (0 === arguments.length)
            throw new TypeError(
              "Failed to execute 'unobserve' on 'ResizeObserver': 1 argument required, but only 0 present."
            );
          if (!Ko(t))
            throw new TypeError(
              "Failed to execute 'unobserve' on 'ResizeObserver': parameter 1 is not of type 'Element"
            );
          As.unobserve(this, t);
        }),
        (t.prototype.disconnect = function () {
          As.disconnect(this);
        }),
        (t.toString = function () {
          return "function ResizeObserver () { [polyfill code] }";
        }),
        t
      );
    })(),
    Ts = o.TypeError,
    Ls = function (t) {
      return function (e, r, n, i) {
        lt(r);
        var o = x(e),
          s = ce(o),
          a = ve(o),
          c = t ? a - 1 : 0,
          l = t ? -1 : 1;
        if (n < 2)
          for (;;) {
            if (c in s) {
              (i = s[c]), (c += l);
              break;
            }
            if (((c += l), t ? c < 0 : a <= c))
              throw Ts("Reduce of empty array with no initial value");
          }
        for (; t ? c >= 0 : a > c; c += l) c in s && (i = r(i, s[c], c, o));
        return i;
      };
    },
    Rs = { left: Ls(!1), right: Ls(!0) },
    _s = "process" == Gt(o.process),
    js = Rs.left,
    zs = ze("reduce");
  hr(
    { target: "Array", proto: !0, forced: !zs || (!_s && N > 79 && N < 83) },
    {
      reduce: function (t) {
        var e = arguments.length;
        return js(this, t, e, e > 1 ? arguments[1] : void 0);
      },
    }
  );
  var Ms,
    Cs,
    Ns = function () {
      var t = tt(this),
        e = "";
      return (
        t.hasIndices && (e += "d"),
        t.global && (e += "g"),
        t.ignoreCase && (e += "i"),
        t.multiline && (e += "m"),
        t.dotAll && (e += "s"),
        t.unicode && (e += "u"),
        t.sticky && (e += "y"),
        e
      );
    },
    Ws = o.RegExp,
    Is = u(function () {
      var t = Ws("a", "y");
      return (t.lastIndex = 2), null != t.exec("abcd");
    }),
    Ps =
      Is ||
      u(function () {
        return !Ws("a", "y").sticky;
      }),
    Bs = {
      BROKEN_CARET:
        Is ||
        u(function () {
          var t = Ws("^r", "gy");
          return (t.lastIndex = 2), null != t.exec("str");
        }),
      MISSED_STICKY: Ps,
      UNSUPPORTED_Y: Is,
    },
    Ds = o.RegExp,
    Fs = u(function () {
      var t = Ds(".", "s");
      return !(t.dotAll && t.exec("\n") && "s" === t.flags);
    }),
    Vs = o.RegExp,
    $s = u(function () {
      var t = Vs("(?<a>b)", "g");
      return "b" !== t.exec("b").groups.a || "bc" !== "b".replace(t, "$<a>c");
    }),
    Xs = $t.get,
    Hs = l("native-string-replace", String.prototype.replace),
    qs = RegExp.prototype.exec,
    Ys = qs,
    Gs = g("".charAt),
    Us = g("".indexOf),
    Ks = g("".replace),
    Js = g("".slice),
    Qs =
      ((Cs = /b*/g),
      rt(qs, (Ms = /a/), "a"),
      rt(qs, Cs, "a"),
      0 !== Ms.lastIndex || 0 !== Cs.lastIndex),
    Zs = Bs.BROKEN_CARET,
    ta = void 0 !== /()??/.exec("")[1];
  (Qs || ta || Zs || Fs || $s) &&
    (Ys = function (t) {
      var e,
        r,
        n,
        i,
        o,
        s,
        a,
        c = this,
        l = Xs(c),
        u = pr(t),
        f = l.raw;
      if (f)
        return (
          (f.lastIndex = c.lastIndex),
          (e = rt(Ys, f, u)),
          (c.lastIndex = f.lastIndex),
          e
        );
      var h = l.groups,
        d = Zs && c.sticky,
        p = rt(Ns, c),
        v = c.source,
        g = 0,
        b = u;
      if (
        (d &&
          ((p = Ks(p, "y", "")),
          -1 === Us(p, "g") && (p += "g"),
          (b = Js(u, c.lastIndex)),
          c.lastIndex > 0 &&
            (!c.multiline ||
              (c.multiline && "\n" !== Gs(u, c.lastIndex - 1))) &&
            ((v = "(?: " + v + ")"), (b = " " + b), g++),
          (r = new RegExp("^(?:" + v + ")", p))),
        ta && (r = new RegExp("^" + v + "$(?!\\s)", p)),
        Qs && (n = c.lastIndex),
        (i = rt(qs, d ? r : c, b)),
        d
          ? i
            ? ((i.input = Js(i.input, g)),
              (i[0] = Js(i[0], g)),
              (i.index = c.lastIndex),
              (c.lastIndex += i[0].length))
            : (c.lastIndex = 0)
          : Qs && i && (c.lastIndex = c.global ? i.index + i[0].length : n),
        ta &&
          i &&
          i.length > 1 &&
          rt(Hs, i[0], r, function () {
            for (o = 1; o < arguments.length - 2; o++)
              void 0 === arguments[o] && (i[o] = void 0);
          }),
        i && h)
      )
        for (i.groups = s = Hr(null), o = 0; o < h.length; o++)
          s[(a = h[o])[0]] = i[a[1]];
      return i;
    });
  var ea = Ys;
  hr({ target: "RegExp", proto: !0, forced: /./.exec !== ea }, { exec: ea });
  var ra = V("species"),
    na = RegExp.prototype,
    ia = function (t, e, r, n) {
      var i = V(t),
        o = !u(function () {
          var e = {};
          return (
            (e[i] = function () {
              return 7;
            }),
            7 != ""[t](e)
          );
        }),
        s =
          o &&
          !u(function () {
            var e = !1,
              r = /a/;
            return (
              "split" === t &&
                (((r = {}).constructor = {}),
                (r.constructor[ra] = function () {
                  return r;
                }),
                (r.flags = ""),
                (r[i] = /./[i])),
              (r.exec = function () {
                return (e = !0), null;
              }),
              r[i](""),
              !e
            );
          });
      if (!o || !s || r) {
        var a = g(/./[i]),
          c = e(i, ""[t], function (t, e, r, n, i) {
            var s = g(t),
              c = e.exec;
            return c === ea || c === na.exec
              ? o && !i
                ? { done: !0, value: a(e, r, n) }
                : { done: !0, value: s(r, e, n) }
              : { done: !1 };
          });
        Ht(String.prototype, t, c[0]), Ht(na, i, c[1]);
      }
      n && Et(na[i], "sham", !0);
    },
    oa = Mn.charAt,
    sa = function (t, e, r) {
      return e + (r ? oa(t, e).length : 1);
    },
    aa = o.TypeError,
    ca = function (t, e) {
      var r = t.exec;
      if (T(r)) {
        var n = rt(r, t, e);
        return null !== n && tt(n), n;
      }
      if ("RegExp" === Gt(t)) return rt(ea, t, e);
      throw aa("RegExp#exec called on incompatible receiver");
    };
  ia("match", function (t, e, r) {
    return [
      function (e) {
        var r = y(this),
          n = null == e ? void 0 : ut(e, t);
        return n ? rt(n, e, r) : new RegExp(e)[t](pr(r));
      },
      function (t) {
        var n = tt(this),
          i = pr(t),
          o = r(e, n, i);
        if (o.done) return o.value;
        if (!n.global) return ca(n, i);
        var s = n.unicode;
        n.lastIndex = 0;
        for (var a, c = [], l = 0; null !== (a = ca(n, i)); ) {
          var u = pr(a[0]);
          (c[l] = u),
            "" === u && (n.lastIndex = sa(i, pe(n.lastIndex), s)),
            l++;
        }
        return 0 === l ? null : c;
      },
    ];
  });
  var la = At.EXISTS,
    ua = mt.f,
    fa = Function.prototype,
    ha = g(fa.toString),
    da = /function\b(?:\s|\/\*[\S\s]*?\*\/|\/\/[^\n\r]*[\n\r]+)*([^\s(/]*)/,
    pa = g(da.exec);
  H &&
    !la &&
    ua(fa, "name", {
      configurable: !0,
      get: function () {
        try {
          return pa(da, ha(this))[1];
        } catch (t) {
          return "";
        }
      },
    });
  var va = Function.prototype,
    ga = va.apply,
    ba = va.call,
    ya =
      ("object" == typeof Reflect && Reflect.apply) ||
      (f
        ? ba.bind(ga)
        : function () {
            return ba.apply(ga, arguments);
          }),
    ma = Math.floor,
    xa = g("".charAt),
    Ea = g("".replace),
    wa = g("".slice),
    Oa = /\$([$&'`]|\d{1,2}|<[^>]*>)/g,
    Sa = /\$([$&'`]|\d{1,2})/g,
    Aa = function (t, e, r, n, i, o) {
      var s = r + t.length,
        a = n.length,
        c = Sa;
      return (
        void 0 !== i && ((i = x(i)), (c = Oa)),
        Ea(o, c, function (o, c) {
          var l;
          switch (xa(c, 0)) {
            case "$":
              return "$";
            case "&":
              return t;
            case "`":
              return wa(e, 0, r);
            case "'":
              return wa(e, s);
            case "<":
              l = i[wa(c, 1, -1)];
              break;
            default:
              var u = +c;
              if (0 === u) return o;
              if (u > a) {
                var f = ma(u / 10);
                return 0 === f
                  ? o
                  : f <= a
                  ? void 0 === n[f - 1]
                    ? xa(c, 1)
                    : n[f - 1] + xa(c, 1)
                  : o;
              }
              l = n[u - 1];
          }
          return void 0 === l ? "" : l;
        })
      );
    },
    ka = V("replace"),
    Ta = Math.max,
    La = Math.min,
    Ra = g([].concat),
    _a = g([].push),
    ja = g("".indexOf),
    za = g("".slice),
    Ma = "$0" === "a".replace(/./, "$0"),
    Ca = !!/./[ka] && "" === /./[ka]("a", "$0");
  ia(
    "replace",
    function (t, e, r) {
      var n = Ca ? "$" : "$0";
      return [
        function (t, r) {
          var n = y(this),
            i = null == t ? void 0 : ut(t, ka);
          return i ? rt(i, t, n, r) : rt(e, pr(n), t, r);
        },
        function (t, i) {
          var o = tt(this),
            s = pr(t);
          if ("string" == typeof i && -1 === ja(i, n) && -1 === ja(i, "$<")) {
            var a = r(e, o, s, i);
            if (a.done) return a.value;
          }
          var c = T(i);
          c || (i = pr(i));
          var l = o.global;
          if (l) {
            var u = o.unicode;
            o.lastIndex = 0;
          }
          for (var f = []; ; ) {
            var h = ca(o, s);
            if (null === h) break;
            if ((_a(f, h), !l)) break;
            "" === pr(h[0]) && (o.lastIndex = sa(s, pe(o.lastIndex), u));
          }
          for (var d, p = "", v = 0, g = 0; g < f.length; g++) {
            for (
              var b = pr((h = f[g])[0]),
                y = Ta(La(he(h.index), s.length), 0),
                m = [],
                x = 1;
              x < h.length;
              x++
            )
              _a(m, void 0 === (d = h[x]) ? d : String(d));
            var E = h.groups;
            if (c) {
              var w = Ra([b], m, y, s);
              void 0 !== E && _a(w, E);
              var O = pr(ya(i, void 0, w));
            } else O = Aa(b, s, y, m, E, i);
            y >= v && ((p += za(s, v, y) + O), (v = y + b.length));
          }
          return p + za(s, v);
        },
      ];
    },
    !!u(function () {
      var t = /./;
      return (
        (t.exec = function () {
          var t = [];
          return (t.groups = { a: "7" }), t;
        }),
        "7" !== "".replace(t, "$<a>")
      );
    }) ||
      !Ma ||
      Ca
  );
  var Na = function (t) {
    return Array.prototype.reduce.call(
      t,
      function (t, e) {
        var r = e.name.match(/data-simplebar-(.+)/);
        if (r) {
          var n = r[1].replace(/\W+(.)/g, function (t, e) {
            return e.toUpperCase();
          });
          switch (e.value) {
            case "true":
              t[n] = !0;
              break;
            case "false":
              t[n] = !1;
              break;
            case void 0:
              t[n] = !0;
              break;
            default:
              t[n] = e.value;
          }
        }
        return t;
      },
      {}
    );
  };
  function Wa(t) {
    return t && t.ownerDocument && t.ownerDocument.defaultView
      ? t.ownerDocument.defaultView
      : window;
  }
  function Ia(t) {
    return t && t.ownerDocument ? t.ownerDocument : document;
  }
  var Pa = null,
    Ba = null;
  function Da(t) {
    if (null === Pa) {
      var e = Ia(t);
      if (void 0 === e) return (Pa = 0);
      var r = e.body,
        n = e.createElement("div");
      n.classList.add("simplebar-hide-scrollbar"), r.appendChild(n);
      var i = n.getBoundingClientRect().right;
      r.removeChild(n), (Pa = i);
    }
    return Pa;
  }
  Ie &&
    window.addEventListener("resize", function () {
      Ba !== window.devicePixelRatio &&
        ((Ba = window.devicePixelRatio), (Pa = null));
    });
  var Fa = (function () {
    function t(e, r) {
      var n = this;
      (this.onScroll = function () {
        var t = Wa(n.el);
        n.scrollXTicking ||
          (t.requestAnimationFrame(n.scrollX), (n.scrollXTicking = !0)),
          n.scrollYTicking ||
            (t.requestAnimationFrame(n.scrollY), (n.scrollYTicking = !0));
      }),
        (this.scrollX = function () {
          n.axis.x.isOverflowing &&
            (n.showScrollbar("x"), n.positionScrollbar("x")),
            (n.scrollXTicking = !1);
        }),
        (this.scrollY = function () {
          n.axis.y.isOverflowing &&
            (n.showScrollbar("y"), n.positionScrollbar("y")),
            (n.scrollYTicking = !1);
        }),
        (this.onMouseEnter = function () {
          n.showScrollbar("x"), n.showScrollbar("y");
        }),
        (this.onMouseMove = function (t) {
          (n.mouseX = t.clientX),
            (n.mouseY = t.clientY),
            (n.axis.x.isOverflowing || n.axis.x.forceVisible) &&
              n.onMouseMoveForAxis("x"),
            (n.axis.y.isOverflowing || n.axis.y.forceVisible) &&
              n.onMouseMoveForAxis("y");
        }),
        (this.onMouseLeave = function () {
          n.onMouseMove.cancel(),
            (n.axis.x.isOverflowing || n.axis.x.forceVisible) &&
              n.onMouseLeaveForAxis("x"),
            (n.axis.y.isOverflowing || n.axis.y.forceVisible) &&
              n.onMouseLeaveForAxis("y"),
            (n.mouseX = -1),
            (n.mouseY = -1);
        }),
        (this.onWindowResize = function () {
          (n.scrollbarWidth = n.getScrollbarWidth()), n.hideNativeScrollbar();
        }),
        (this.hideScrollbars = function () {
          (n.axis.x.track.rect = n.axis.x.track.el.getBoundingClientRect()),
            (n.axis.y.track.rect = n.axis.y.track.el.getBoundingClientRect()),
            n.isWithinBounds(n.axis.y.track.rect) ||
              (n.axis.y.scrollbar.el.classList.remove(n.classNames.visible),
              (n.axis.y.isVisible = !1)),
            n.isWithinBounds(n.axis.x.track.rect) ||
              (n.axis.x.scrollbar.el.classList.remove(n.classNames.visible),
              (n.axis.x.isVisible = !1));
        }),
        (this.onPointerEvent = function (t) {
          var e, r;
          (n.axis.x.track.rect = n.axis.x.track.el.getBoundingClientRect()),
            (n.axis.y.track.rect = n.axis.y.track.el.getBoundingClientRect()),
            (n.axis.x.isOverflowing || n.axis.x.forceVisible) &&
              (e = n.isWithinBounds(n.axis.x.track.rect)),
            (n.axis.y.isOverflowing || n.axis.y.forceVisible) &&
              (r = n.isWithinBounds(n.axis.y.track.rect)),
            (e || r) &&
              (t.preventDefault(),
              t.stopPropagation(),
              "mousedown" === t.type &&
                (e &&
                  ((n.axis.x.scrollbar.rect =
                    n.axis.x.scrollbar.el.getBoundingClientRect()),
                  n.isWithinBounds(n.axis.x.scrollbar.rect)
                    ? n.onDragStart(t, "x")
                    : n.onTrackClick(t, "x")),
                r &&
                  ((n.axis.y.scrollbar.rect =
                    n.axis.y.scrollbar.el.getBoundingClientRect()),
                  n.isWithinBounds(n.axis.y.scrollbar.rect)
                    ? n.onDragStart(t, "y")
                    : n.onTrackClick(t, "y"))));
        }),
        (this.drag = function (e) {
          var r = n.axis[n.draggedAxis].track,
            i = r.rect[n.axis[n.draggedAxis].sizeAttr],
            o = n.axis[n.draggedAxis].scrollbar,
            s = n.contentWrapperEl[n.axis[n.draggedAxis].scrollSizeAttr],
            a = parseInt(n.elStyles[n.axis[n.draggedAxis].sizeAttr], 10);
          e.preventDefault(), e.stopPropagation();
          var c =
            ((("y" === n.draggedAxis ? e.pageY : e.pageX) -
              r.rect[n.axis[n.draggedAxis].offsetAttr] -
              n.axis[n.draggedAxis].dragOffset) /
              (i - o.size)) *
            (s - a);
          "x" === n.draggedAxis &&
            ((c =
              n.isRtl && t.getRtlHelpers().isRtlScrollbarInverted
                ? c - (i + o.size)
                : c),
            (c = n.isRtl && t.getRtlHelpers().isRtlScrollingInverted ? -c : c)),
            (n.contentWrapperEl[n.axis[n.draggedAxis].scrollOffsetAttr] = c);
        }),
        (this.onEndDrag = function (t) {
          var e = Ia(n.el),
            r = Wa(n.el);
          t.preventDefault(),
            t.stopPropagation(),
            n.el.classList.remove(n.classNames.dragging),
            e.removeEventListener("mousemove", n.drag, !0),
            e.removeEventListener("mouseup", n.onEndDrag, !0),
            (n.removePreventClickId = r.setTimeout(function () {
              e.removeEventListener("click", n.preventClick, !0),
                e.removeEventListener("dblclick", n.preventClick, !0),
                (n.removePreventClickId = null);
            }));
        }),
        (this.preventClick = function (t) {
          t.preventDefault(), t.stopPropagation();
        }),
        (this.el = e),
        (this.minScrollbarWidth = 20),
        (this.options = Object.assign({}, t.defaultOptions, r)),
        (this.classNames = Object.assign(
          {},
          t.defaultOptions.classNames,
          this.options.classNames
        )),
        (this.axis = {
          x: {
            scrollOffsetAttr: "scrollLeft",
            sizeAttr: "width",
            scrollSizeAttr: "scrollWidth",
            offsetSizeAttr: "offsetWidth",
            offsetAttr: "left",
            overflowAttr: "overflowX",
            dragOffset: 0,
            isOverflowing: !0,
            isVisible: !1,
            forceVisible: !1,
            track: {},
            scrollbar: {},
          },
          y: {
            scrollOffsetAttr: "scrollTop",
            sizeAttr: "height",
            scrollSizeAttr: "scrollHeight",
            offsetSizeAttr: "offsetHeight",
            offsetAttr: "top",
            overflowAttr: "overflowY",
            dragOffset: 0,
            isOverflowing: !0,
            isVisible: !1,
            forceVisible: !1,
            track: {},
            scrollbar: {},
          },
        }),
        (this.removePreventClickId = null),
        t.instances.has(this.el) ||
          ((this.recalculate = Qi(this.recalculate.bind(this), 64)),
          (this.onMouseMove = Qi(this.onMouseMove.bind(this), 64)),
          (this.hideScrollbars = po(
            this.hideScrollbars.bind(this),
            this.options.timeout
          )),
          (this.onWindowResize = po(this.onWindowResize.bind(this), 64, {
            leading: !0,
          })),
          (t.getRtlHelpers = Fo(t.getRtlHelpers)),
          this.init());
    }
    (t.getRtlHelpers = function () {
      var e = document.createElement("div");
      e.innerHTML =
        '<div class="hs-dummy-scrollbar-size"><div style="height: 200%; width: 200%; margin: 10px 0;"></div></div>';
      var r = e.firstElementChild;
      document.body.appendChild(r);
      var n = r.firstElementChild;
      r.scrollLeft = 0;
      var i = t.getOffset(r),
        o = t.getOffset(n);
      r.scrollLeft = 999;
      var s = t.getOffset(n);
      return {
        isRtlScrollingInverted: i.left !== o.left && o.left - s.left != 0,
        isRtlScrollbarInverted: i.left !== o.left,
      };
    }),
      (t.getOffset = function (t) {
        var e = t.getBoundingClientRect(),
          r = Ia(t),
          n = Wa(t);
        return {
          top: e.top + (n.pageYOffset || r.documentElement.scrollTop),
          left: e.left + (n.pageXOffset || r.documentElement.scrollLeft),
        };
      });
    var e = t.prototype;
    return (
      (e.init = function () {
        t.instances.set(this.el, this),
          Ie &&
            (this.initDOM(),
            this.setAccessibilityAttributes(),
            (this.scrollbarWidth = this.getScrollbarWidth()),
            this.recalculate(),
            this.initListeners());
      }),
      (e.initDOM = function () {
        var t = this;
        if (
          Array.prototype.filter.call(this.el.children, function (e) {
            return e.classList.contains(t.classNames.wrapper);
          }).length
        )
          (this.wrapperEl = this.el.querySelector(
            "." + this.classNames.wrapper
          )),
            (this.contentWrapperEl =
              this.options.scrollableNode ||
              this.el.querySelector("." + this.classNames.contentWrapper)),
            (this.contentEl =
              this.options.contentNode ||
              this.el.querySelector("." + this.classNames.contentEl)),
            (this.offsetEl = this.el.querySelector(
              "." + this.classNames.offset
            )),
            (this.maskEl = this.el.querySelector("." + this.classNames.mask)),
            (this.placeholderEl = this.findChild(
              this.wrapperEl,
              "." + this.classNames.placeholder
            )),
            (this.heightAutoObserverWrapperEl = this.el.querySelector(
              "." + this.classNames.heightAutoObserverWrapperEl
            )),
            (this.heightAutoObserverEl = this.el.querySelector(
              "." + this.classNames.heightAutoObserverEl
            )),
            (this.axis.x.track.el = this.findChild(
              this.el,
              "." + this.classNames.track + "." + this.classNames.horizontal
            )),
            (this.axis.y.track.el = this.findChild(
              this.el,
              "." + this.classNames.track + "." + this.classNames.vertical
            ));
        else {
          for (
            this.wrapperEl = document.createElement("div"),
              this.contentWrapperEl = document.createElement("div"),
              this.offsetEl = document.createElement("div"),
              this.maskEl = document.createElement("div"),
              this.contentEl = document.createElement("div"),
              this.placeholderEl = document.createElement("div"),
              this.heightAutoObserverWrapperEl = document.createElement("div"),
              this.heightAutoObserverEl = document.createElement("div"),
              this.wrapperEl.classList.add(this.classNames.wrapper),
              this.contentWrapperEl.classList.add(
                this.classNames.contentWrapper
              ),
              this.offsetEl.classList.add(this.classNames.offset),
              this.maskEl.classList.add(this.classNames.mask),
              this.contentEl.classList.add(this.classNames.contentEl),
              this.placeholderEl.classList.add(this.classNames.placeholder),
              this.heightAutoObserverWrapperEl.classList.add(
                this.classNames.heightAutoObserverWrapperEl
              ),
              this.heightAutoObserverEl.classList.add(
                this.classNames.heightAutoObserverEl
              );
            this.el.firstChild;

          )
            this.contentEl.appendChild(this.el.firstChild);
          this.contentWrapperEl.appendChild(this.contentEl),
            this.offsetEl.appendChild(this.contentWrapperEl),
            this.maskEl.appendChild(this.offsetEl),
            this.heightAutoObserverWrapperEl.appendChild(
              this.heightAutoObserverEl
            ),
            this.wrapperEl.appendChild(this.heightAutoObserverWrapperEl),
            this.wrapperEl.appendChild(this.maskEl),
            this.wrapperEl.appendChild(this.placeholderEl),
            this.el.appendChild(this.wrapperEl);
        }
        if (!this.axis.x.track.el || !this.axis.y.track.el) {
          var e = document.createElement("div"),
            r = document.createElement("div");
          e.classList.add(this.classNames.track),
            r.classList.add(this.classNames.scrollbar),
            e.appendChild(r),
            (this.axis.x.track.el = e.cloneNode(!0)),
            this.axis.x.track.el.classList.add(this.classNames.horizontal),
            (this.axis.y.track.el = e.cloneNode(!0)),
            this.axis.y.track.el.classList.add(this.classNames.vertical),
            this.el.appendChild(this.axis.x.track.el),
            this.el.appendChild(this.axis.y.track.el);
        }
        (this.axis.x.scrollbar.el = this.axis.x.track.el.querySelector(
          "." + this.classNames.scrollbar
        )),
          (this.axis.y.scrollbar.el = this.axis.y.track.el.querySelector(
            "." + this.classNames.scrollbar
          )),
          this.options.autoHide ||
            (this.axis.x.scrollbar.el.classList.add(this.classNames.visible),
            this.axis.y.scrollbar.el.classList.add(this.classNames.visible)),
          this.el.setAttribute("data-simplebar", "init");
      }),
      (e.setAccessibilityAttributes = function () {
        var t = this.options.ariaLabel || "scrollable content";
        this.contentWrapperEl.setAttribute("tabindex", "0"),
          this.contentWrapperEl.setAttribute("role", "region"),
          this.contentWrapperEl.setAttribute("aria-label", t);
      }),
      (e.initListeners = function () {
        var t = this,
          e = Wa(this.el);
        this.options.autoHide &&
          this.el.addEventListener("mouseenter", this.onMouseEnter),
          ["mousedown", "click", "dblclick"].forEach(function (e) {
            t.el.addEventListener(e, t.onPointerEvent, !0);
          }),
          ["touchstart", "touchend", "touchmove"].forEach(function (e) {
            t.el.addEventListener(e, t.onPointerEvent, {
              capture: !0,
              passive: !0,
            });
          }),
          this.el.addEventListener("mousemove", this.onMouseMove),
          this.el.addEventListener("mouseleave", this.onMouseLeave),
          this.contentWrapperEl.addEventListener("scroll", this.onScroll),
          e.addEventListener("resize", this.onWindowResize);
        var r = !1,
          n = e.ResizeObserver || ks;
        (this.resizeObserver = new n(function () {
          r && t.recalculate();
        })),
          this.resizeObserver.observe(this.el),
          this.resizeObserver.observe(this.contentEl),
          e.requestAnimationFrame(function () {
            r = !0;
          }),
          (this.mutationObserver = new e.MutationObserver(this.recalculate)),
          this.mutationObserver.observe(this.contentEl, {
            childList: !0,
            subtree: !0,
            characterData: !0,
          });
      }),
      (e.recalculate = function () {
        var t = Wa(this.el);
        (this.elStyles = t.getComputedStyle(this.el)),
          (this.isRtl = "rtl" === this.elStyles.direction);
        var e = this.heightAutoObserverEl.offsetHeight <= 1,
          r = this.heightAutoObserverEl.offsetWidth <= 1,
          n = this.contentEl.offsetWidth,
          i = this.contentWrapperEl.offsetWidth,
          o = this.elStyles.overflowX,
          s = this.elStyles.overflowY;
        (this.contentEl.style.padding =
          this.elStyles.paddingTop +
          " " +
          this.elStyles.paddingRight +
          " " +
          this.elStyles.paddingBottom +
          " " +
          this.elStyles.paddingLeft),
          (this.wrapperEl.style.margin =
            "-" +
            this.elStyles.paddingTop +
            " -" +
            this.elStyles.paddingRight +
            " -" +
            this.elStyles.paddingBottom +
            " -" +
            this.elStyles.paddingLeft);
        var a = this.contentEl.scrollHeight,
          c = this.contentEl.scrollWidth;
        (this.contentWrapperEl.style.height = e ? "auto" : "100%"),
          (this.placeholderEl.style.width = r ? n + "px" : "auto"),
          (this.placeholderEl.style.height = a + "px");
        var l = this.contentWrapperEl.offsetHeight;
        (this.axis.x.isOverflowing = c > n),
          (this.axis.y.isOverflowing = a > l),
          (this.axis.x.isOverflowing =
            "hidden" !== o && this.axis.x.isOverflowing),
          (this.axis.y.isOverflowing =
            "hidden" !== s && this.axis.y.isOverflowing),
          (this.axis.x.forceVisible =
            "x" === this.options.forceVisible ||
            !0 === this.options.forceVisible),
          (this.axis.y.forceVisible =
            "y" === this.options.forceVisible ||
            !0 === this.options.forceVisible),
          this.hideNativeScrollbar();
        var u = this.axis.x.isOverflowing ? this.scrollbarWidth : 0,
          f = this.axis.y.isOverflowing ? this.scrollbarWidth : 0;
        (this.axis.x.isOverflowing = this.axis.x.isOverflowing && c > i - f),
          (this.axis.y.isOverflowing = this.axis.y.isOverflowing && a > l - u),
          (this.axis.x.scrollbar.size = this.getScrollbarSize("x")),
          (this.axis.y.scrollbar.size = this.getScrollbarSize("y")),
          (this.axis.x.scrollbar.el.style.width =
            this.axis.x.scrollbar.size + "px"),
          (this.axis.y.scrollbar.el.style.height =
            this.axis.y.scrollbar.size + "px"),
          this.positionScrollbar("x"),
          this.positionScrollbar("y"),
          this.toggleTrackVisibility("x"),
          this.toggleTrackVisibility("y");
      }),
      (e.getScrollbarSize = function (t) {
        if ((void 0 === t && (t = "y"), !this.axis[t].isOverflowing)) return 0;
        var e,
          r = this.contentEl[this.axis[t].scrollSizeAttr],
          n = this.axis[t].track.el[this.axis[t].offsetSizeAttr],
          i = n / r;
        return (
          (e = Math.max(~~(i * n), this.options.scrollbarMinSize)),
          this.options.scrollbarMaxSize &&
            (e = Math.min(e, this.options.scrollbarMaxSize)),
          e
        );
      }),
      (e.positionScrollbar = function (e) {
        if ((void 0 === e && (e = "y"), this.axis[e].isOverflowing)) {
          var r = this.contentWrapperEl[this.axis[e].scrollSizeAttr],
            n = this.axis[e].track.el[this.axis[e].offsetSizeAttr],
            i = parseInt(this.elStyles[this.axis[e].sizeAttr], 10),
            o = this.axis[e].scrollbar,
            s = this.contentWrapperEl[this.axis[e].scrollOffsetAttr],
            a =
              (s =
                "x" === e &&
                this.isRtl &&
                t.getRtlHelpers().isRtlScrollingInverted
                  ? -s
                  : s) /
              (r - i),
            c = ~~((n - o.size) * a);
          (c =
            "x" === e && this.isRtl && t.getRtlHelpers().isRtlScrollbarInverted
              ? c + (n - o.size)
              : c),
            (o.el.style.transform =
              "x" === e
                ? "translate3d(" + c + "px, 0, 0)"
                : "translate3d(0, " + c + "px, 0)");
        }
      }),
      (e.toggleTrackVisibility = function (t) {
        void 0 === t && (t = "y");
        var e = this.axis[t].track.el,
          r = this.axis[t].scrollbar.el;
        this.axis[t].isOverflowing || this.axis[t].forceVisible
          ? ((e.style.visibility = "visible"),
            (this.contentWrapperEl.style[this.axis[t].overflowAttr] = "scroll"))
          : ((e.style.visibility = "hidden"),
            (this.contentWrapperEl.style[this.axis[t].overflowAttr] =
              "hidden")),
          this.axis[t].isOverflowing
            ? (r.style.display = "block")
            : (r.style.display = "none");
      }),
      (e.hideNativeScrollbar = function () {
        (this.offsetEl.style[this.isRtl ? "left" : "right"] =
          this.axis.y.isOverflowing || this.axis.y.forceVisible
            ? "-" + this.scrollbarWidth + "px"
            : 0),
          (this.offsetEl.style.bottom =
            this.axis.x.isOverflowing || this.axis.x.forceVisible
              ? "-" + this.scrollbarWidth + "px"
              : 0);
      }),
      (e.onMouseMoveForAxis = function (t) {
        void 0 === t && (t = "y"),
          (this.axis[t].track.rect =
            this.axis[t].track.el.getBoundingClientRect()),
          (this.axis[t].scrollbar.rect =
            this.axis[t].scrollbar.el.getBoundingClientRect()),
          this.isWithinBounds(this.axis[t].scrollbar.rect)
            ? this.axis[t].scrollbar.el.classList.add(this.classNames.hover)
            : this.axis[t].scrollbar.el.classList.remove(this.classNames.hover),
          this.isWithinBounds(this.axis[t].track.rect)
            ? (this.showScrollbar(t),
              this.axis[t].track.el.classList.add(this.classNames.hover))
            : this.axis[t].track.el.classList.remove(this.classNames.hover);
      }),
      (e.onMouseLeaveForAxis = function (t) {
        void 0 === t && (t = "y"),
          this.axis[t].track.el.classList.remove(this.classNames.hover),
          this.axis[t].scrollbar.el.classList.remove(this.classNames.hover);
      }),
      (e.showScrollbar = function (t) {
        void 0 === t && (t = "y");
        var e = this.axis[t].scrollbar.el;
        this.axis[t].isVisible ||
          (e.classList.add(this.classNames.visible),
          (this.axis[t].isVisible = !0)),
          this.options.autoHide && this.hideScrollbars();
      }),
      (e.onDragStart = function (t, e) {
        void 0 === e && (e = "y");
        var r = Ia(this.el),
          n = Wa(this.el),
          i = this.axis[e].scrollbar,
          o = "y" === e ? t.pageY : t.pageX;
        (this.axis[e].dragOffset = o - i.rect[this.axis[e].offsetAttr]),
          (this.draggedAxis = e),
          this.el.classList.add(this.classNames.dragging),
          r.addEventListener("mousemove", this.drag, !0),
          r.addEventListener("mouseup", this.onEndDrag, !0),
          null === this.removePreventClickId
            ? (r.addEventListener("click", this.preventClick, !0),
              r.addEventListener("dblclick", this.preventClick, !0))
            : (n.clearTimeout(this.removePreventClickId),
              (this.removePreventClickId = null));
      }),
      (e.onTrackClick = function (t, e) {
        var r = this;
        if ((void 0 === e && (e = "y"), this.options.clickOnTrack)) {
          var n = Wa(this.el);
          this.axis[e].scrollbar.rect =
            this.axis[e].scrollbar.el.getBoundingClientRect();
          var i = this.axis[e].scrollbar.rect[this.axis[e].offsetAttr],
            o = parseInt(this.elStyles[this.axis[e].sizeAttr], 10),
            s = this.contentWrapperEl[this.axis[e].scrollOffsetAttr],
            a = ("y" === e ? this.mouseY - i : this.mouseX - i) < 0 ? -1 : 1,
            c = -1 === a ? s - o : s + o;
          !(function t() {
            var i, o;
            -1 === a
              ? s > c &&
                ((s -= r.options.clickOnTrackSpeed),
                r.contentWrapperEl.scrollTo(
                  (((i = {})[r.axis[e].offsetAttr] = s), i)
                ),
                n.requestAnimationFrame(t))
              : s < c &&
                ((s += r.options.clickOnTrackSpeed),
                r.contentWrapperEl.scrollTo(
                  (((o = {})[r.axis[e].offsetAttr] = s), o)
                ),
                n.requestAnimationFrame(t));
          })();
        }
      }),
      (e.getContentElement = function () {
        return this.contentEl;
      }),
      (e.getScrollElement = function () {
        return this.contentWrapperEl;
      }),
      (e.getScrollbarWidth = function () {
        try {
          return "none" ===
            getComputedStyle(this.contentWrapperEl, "::-webkit-scrollbar")
              .display ||
            "scrollbarWidth" in document.documentElement.style ||
            "-ms-overflow-style" in document.documentElement.style
            ? 0
            : Da(this.el);
        } catch (t) {
          return Da(this.el);
        }
      }),
      (e.removeListeners = function () {
        var t = this,
          e = Wa(this.el);
        this.options.autoHide &&
          this.el.removeEventListener("mouseenter", this.onMouseEnter),
          ["mousedown", "click", "dblclick"].forEach(function (e) {
            t.el.removeEventListener(e, t.onPointerEvent, !0);
          }),
          ["touchstart", "touchend", "touchmove"].forEach(function (e) {
            t.el.removeEventListener(e, t.onPointerEvent, {
              capture: !0,
              passive: !0,
            });
          }),
          this.el.removeEventListener("mousemove", this.onMouseMove),
          this.el.removeEventListener("mouseleave", this.onMouseLeave),
          this.contentWrapperEl &&
            this.contentWrapperEl.removeEventListener("scroll", this.onScroll),
          e.removeEventListener("resize", this.onWindowResize),
          this.mutationObserver && this.mutationObserver.disconnect(),
          this.resizeObserver && this.resizeObserver.disconnect(),
          this.recalculate.cancel(),
          this.onMouseMove.cancel(),
          this.hideScrollbars.cancel(),
          this.onWindowResize.cancel();
      }),
      (e.unMount = function () {
        this.removeListeners(), t.instances.delete(this.el);
      }),
      (e.isWithinBounds = function (t) {
        return (
          this.mouseX >= t.left &&
          this.mouseX <= t.left + t.width &&
          this.mouseY >= t.top &&
          this.mouseY <= t.top + t.height
        );
      }),
      (e.findChild = function (t, e) {
        var r =
          t.matches ||
          t.webkitMatchesSelector ||
          t.mozMatchesSelector ||
          t.msMatchesSelector;
        return Array.prototype.filter.call(t.children, function (t) {
          return r.call(t, e);
        })[0];
      }),
      t
    );
  })();
  return (
    (Fa.defaultOptions = {
      autoHide: !0,
      forceVisible: !1,
      clickOnTrack: !0,
      clickOnTrackSpeed: 40,
      classNames: {
        contentEl: "simplebar-content",
        contentWrapper: "simplebar-content-wrapper",
        offset: "simplebar-offset",
        mask: "simplebar-mask",
        wrapper: "simplebar-wrapper",
        placeholder: "simplebar-placeholder",
        scrollbar: "simplebar-scrollbar",
        track: "simplebar-track",
        heightAutoObserverWrapperEl: "simplebar-height-auto-observer-wrapper",
        heightAutoObserverEl: "simplebar-height-auto-observer",
        visible: "simplebar-visible",
        horizontal: "simplebar-horizontal",
        vertical: "simplebar-vertical",
        hover: "simplebar-hover",
        dragging: "simplebar-dragging",
      },
      scrollbarMinSize: 25,
      scrollbarMaxSize: 0,
      timeout: 1e3,
    }),
    (Fa.instances = new WeakMap()),
    (Fa.initDOMLoadedElements = function () {
      document.removeEventListener(
        "DOMContentLoaded",
        this.initDOMLoadedElements
      ),
        window.removeEventListener("load", this.initDOMLoadedElements),
        Array.prototype.forEach.call(
          document.querySelectorAll("[data-simplebar]"),
          function (t) {
            "init" === t.getAttribute("data-simplebar") ||
              Fa.instances.has(t) ||
              new Fa(t, Na(t.attributes));
          }
        );
    }),
    (Fa.removeObserver = function () {
      this.globalObserver.disconnect();
    }),
    (Fa.initHtmlApi = function () {
      (this.initDOMLoadedElements = this.initDOMLoadedElements.bind(this)),
        "undefined" != typeof MutationObserver &&
          ((this.globalObserver = new MutationObserver(Fa.handleMutations)),
          this.globalObserver.observe(document, {
            childList: !0,
            subtree: !0,
          })),
        "complete" === document.readyState ||
        ("loading" !== document.readyState &&
          !document.documentElement.doScroll)
          ? window.setTimeout(this.initDOMLoadedElements)
          : (document.addEventListener(
              "DOMContentLoaded",
              this.initDOMLoadedElements
            ),
            window.addEventListener("load", this.initDOMLoadedElements));
    }),
    (Fa.handleMutations = function (t) {
      t.forEach(function (t) {
        Array.prototype.forEach.call(t.addedNodes, function (t) {
          1 === t.nodeType &&
            (t.hasAttribute("data-simplebar")
              ? !Fa.instances.has(t) &&
                document.documentElement.contains(t) &&
                new Fa(t, Na(t.attributes))
              : Array.prototype.forEach.call(
                  t.querySelectorAll("[data-simplebar]"),
                  function (t) {
                    "init" !== t.getAttribute("data-simplebar") &&
                      !Fa.instances.has(t) &&
                      document.documentElement.contains(t) &&
                      new Fa(t, Na(t.attributes));
                  }
                ));
        }),
          Array.prototype.forEach.call(t.removedNodes, function (t) {
            1 === t.nodeType &&
              ("init" === t.getAttribute("data-simplebar")
                ? Fa.instances.has(t) &&
                  !document.documentElement.contains(t) &&
                  Fa.instances.get(t).unMount()
                : Array.prototype.forEach.call(
                    t.querySelectorAll('[data-simplebar="init"]'),
                    function (t) {
                      Fa.instances.has(t) &&
                        !document.documentElement.contains(t) &&
                        Fa.instances.get(t).unMount();
                    }
                  ));
          });
      });
    }),
    (Fa.getOptions = Na),
    Ie && Fa.initHtmlApi(),
    Fa
  );
});
