<!DOCTYPE html>
<html
  lang="en"
  data-layout="vertical"
  data-topbar="light"
  data-sidebar="dark"
  data-sidebar-size="lg"
  data-sidebar-image="none"
  data-preloader="disable"
>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="sugarpay66 payment" />
    <meta name="keywords" content="sugarpay66 payment" />
    <meta name="author" content="member.sugarpay66.io" />
    <meta property="og:title" content="Merchant sugarpay66 payment" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="/" />
    <meta
      property="og:image"
      content="./public/assets/images/logo/android-chrome-512x512.png"
    />
    <title>ร้าน tiger-001 - Merchant sugarpay66 payment</title>

    <style>
      :root {
        --theme-bg_color_login: linear-gradient(to right, #bfa2a8, #6e5c60);
        --theme-color_primary: #6e5c60;
      }
    </style>

    <!-- For IE6+ -->
    <link
      rel="shortcut icon"
      href="public/assets/images/logo/favicon.ico"
      type="image/x-icon"
    />

    <!-- For all other browsers -->
    <link
      rel="icon"
      href="public/assets/images/logo/favicon.ico"
    />

    <!-- Different sizes -->
    <link
      rel="icon"
      href="public/assets/images/logo/favicon-16x16.png"
      sizes="16x16"
    />
    <link
      rel="icon"
      href="public/assets/images/logo/favicon-32x32.png"
      sizes="32x32"
    />

    <!-- For Modern Browsers with PNG Support -->
    <link
      rel="icon"
      type="image/png"
      href="public/assets/images/logo/apple-touch-icon.png"
    />

    <!-- Works in Firefox, Opera, Chrome and Safari -->
    <link
      rel="icon"
      href="public/assets/images/logo/apple-touch-icon.png"
    />

    <!-- For rounded corners and reflective shine in Apple devices -->
    <link
      rel="apple-touch-icon"
      href="public/assets/images/logo/apple-touch-icon.png"
    />

    <!-- Favicon without reflective shine -->
    <link
      rel="apple-touch-icon-precomposed"
      href="/public/assets/images/logo/apple-touch-icon.png"
    />

    <!-- jsvectormap css -->
    <link
      href="./public/assets/libs/jsvectormap/css/jsvectormap.min.css"
      rel="stylesheet"
      type="text/css"
    />

    <!--Swiper slider css-->
    <link
      href="/public/assets/libs/swiper/swiper-bundle.min.css"
      rel="stylesheet"
      type="text/css"
    />

    <!-- Sweet Alert css-->
    <link
      href="/public/assets/libs/sweetalert2/sweetalert2.min.css"
      rel="stylesheet"
      type="text/css"
    />

    <link
      href="public/assets/libs/flatpickr/flatpickr.min.css"
      rel="stylesheet"
      type="text/css"
    />

    <!-- Layout config Js -->
    <script src="public/assets/js/layout.js"></script>
    <!-- Bootstrap Css -->
    <link
      href="public/assets/css/bootstrap.min.css"
      rel="stylesheet"
      type="text/css"
    />
    <!-- Icons Css -->
    <link
      href="public/assets/css/icons.min.css"
      rel="stylesheet"
      type="text/css"
    />
    <!-- App Css-->
    <link
      href="public/assets/css/app.css?t=1746271789"
      rel="stylesheet"
      type="text/css"
    />

    <!-- custom Css-->
    <link
      href="public/assets/css/custom.css?t=1746271789"
      rel="stylesheet"
      type="text/css"
    />

    <!--datatable css-->
    <link
      rel="stylesheet"
      href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css"
    />
    <!--datatable responsive css-->
    <link
      rel="stylesheet"
      href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap.min.css"
    />
    <link
      rel="stylesheet"
      href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.dataTables.min.css"
    />

    <!-- Filepond css -->
    <link rel="stylesheet" href="public/assets/libs/filepond/filepond.min.css" type="text/css" />
    <link rel="stylesheet" href="public/assets/libs/filepond-plugin-image-preview/filepond-plugin-image-preview.min.css" />
    <style type="text/css">
      .text-bold {
        font-weight: bold;
      }
      .text-right {
        text-align: right;
      }
      .modal-xl {
        max-width: 1300px !important;
      }
      #modalAnnouncement img {
        width: 100%;
      }
      #modalAnnouncement p {
        margin-top: 0;
        margin-bottom: 0.2rem;
      }
    </style>
    <style>
      .text-left {
        text-align: left;
      }
      .text-right {
        text-align: right;
      }
      .text-center {
        text-align: center;
      }
      .bg-op-th {
        --vz-bg-opacity: 0.2;
      }
      [v-cloak] { display: none; }
      th {
        vertical-align: middle;
      }
      .table>tbody>tr>td {
        vertical-align: middle;
      }
      .highlight {
        color: #000;
        font-weight: bold;
        background-color: rgba(41,156,219,.1);
      }
    </style>
  </head>

  <body>
    <!-- Begin page -->
    <div id="layout-wrapper">
      <header id="page-topbar">
        <div class="layout-width">
          <div class="navbar-header">
            <div class="d-flex">
              <!-- LOGO -->
              <div class="navbar-brand-box horizontal-logo">
                <a href="" class="logo logo-dark">
                  <span class="logo-sm">
                    <img
                      src="public/assets/images/logo/logo-sm.png"
                      alt=""
                      height="22"
                    />
                  </span>
                  <span class="logo-lg">
                    <img
                      src="public/assets/images/logo/logo.png"
                      alt=""
                      height="48"
                    />
                  </span>
                </a>

                <a href="index.html" class="logo logo-light">
                  <span class="logo-sm">
                    <img
                      src="public/assets/images/logo/logo-sm.png"
                      alt=""
                      height="22"
                    />
                  </span>
                  <span class="logo-lg">
                    <img
                      src="public/assets/images/logo/logo.png"
                      alt=""
                      height="48"
                    />
                  </span>
                </a>
              </div>

              <button
                type="button"
                class="btn btn-sm px-3 fs-16 header-item vertical-menu-btn topnav-hamburger"
                id="topnav-hamburger-icon"
              >
                <span class="hamburger-icon">
                  <span></span>
                  <span></span>
                  <span></span>
                </span>
              </button>
            </div>

            <div class="d-flex align-items-center">
              <div class="ms-1 header-item d-none d-sm-flex">
                <button
                  type="button"
                  class="btn btn-icon btn-topbar btn-ghost-secondary rounded-circle"
                  data-toggle="fullscreen"
                >
                  <i class="bx bx-fullscreen fs-22"></i>
                </button>
              </div>
              <div class="dropdown ms-sm-3 header-item topbar-user">
                <button
                  type="button"
                  class="btn"
                  id="page-header-user-dropdown"
                  data-bs-toggle="dropdown"
                  aria-haspopup="true"
                  aria-expanded="false"
                >
                  <span class="d-flex align-items-center">
                    <img
                      class="rounded-circle header-profile-user"
                      src="public/assets/images/logo/logo-sm.png"
                      alt="Header Avatar"
                    />
                    <span class="text-start ms-xl-2">
                      <span
                        class="d-none d-xl-inline-block ms-1 fw-medium user-name-text"
                        >tiger-001</span
                      >
                      <span
                        class="d-none d-xl-block ms-1 fs-12 text-muted user-name-sub-text"
                      ></span>
                    </span>
                  </span>
                </button>
                <div class="dropdown-menu dropdown-menu-end">
                  <!-- item-->
                  <h6 class="dropdown-header">ยินดีต้อนรับ tiger-001</h6>
                  <a
                    class="dropdown-item"
                    href="login/logout"
                    ><i
                      class="mdi mdi-logout text-muted fs-16 align-middle me-1"
                    ></i>
                    <span class="align-middle" data-key="t-logout"
                      >ออกจากระบบ</span
                    ></a
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <!-- ========== App Menu ========== -->
      <div class="app-menu navbar-menu">
        <!-- LOGO -->
        <div class="navbar-brand-box">
          <!-- Dark Logo-->
          <a href="" class="logo logo-dark">
            <span class="logo-sm">
              <img
                src="public/assets/images/logo/logo-sm.png"
                alt=""
                height="22"
              />
            </span>
            <span class="logo-lg">
              <img
                src="public/assets/images/logo/logo.png"
                alt=""
                height="48"
              />
            </span>
          </a>
          <!-- Light Logo-->
          <a href="" class="logo logo-light">
            <span class="logo-sm">
              <img
                src="public/assets/images/logo/logo-sm.png"
                alt=""
                height="22"
              />
            </span>
            <span class="logo-lg">
              <img
                src="public/assets/images/logo/logo.png"
                alt=""
                height="48"
              />
            </span>
          </a>
          <button
            type="button"
            class="btn btn-sm p-0 fs-20 header-item float-end btn-vertical-sm-hover"
            id="vertical-hover"
          >
            <i class="ri-record-circle-line"></i>
          </button>
        </div>

        <div id="scrollbar">
          <div class="container-fluid">
            <div id="two-column-menu"></div>
            <ul class="navbar-nav mt-3" id="navbar-nav">
              <li class="nav-item">
                <a
                  class="nav-link menu-link"
                  href="dashboard.html"
                >
                  <i class="ri-article-line"></i>
                  <span data-key="t-users">Dashboard</span>
                </a>
              </li>

              <li class="nav-item">
                <a
                  class="nav-link menu-link"
                  href="transaction.html"
                >
                  <i class="ri-article-line"></i>
                  <span data-key="t-users">Transaction</span>
                </a>
              </li>
              <li class="nav-item">
                <a
                  class="nav-link menu-link"
                  href="withdraw_approve.html"
                >
                  <i class="ri-article-line"></i>
                  <span data-key="t-users">Withdraw Approve</span>
                </a>
              </li>
              <li class="nav-item">
                <a
                  class="nav-link menu-link"
                  href="bank_statement.html"
                >
                  <i class="ri-bank-line"></i>
                  <span data-key="t-users">Statement</span>
                </a>
              </li>
              <li class="nav-item">
                <a
                  class="nav-link menu-link"
                  href="summary_transfer.html"
                >
                  <i class="ri-file-transfer-line"></i>
                  <span data-key="t-users">Settlement</span>
                </a>
              </li>

              <a
                class="nav-link menu-link"
                href="#sidebarReport"
                data-bs-toggle="collapse"
                role="button"
                aria-expanded="false"
                aria-controls="sidebarReport"
              >
                <i class="ri-article-line"></i>
                <span data-key="t-report">Report</span>
              </a>
              <div class="collapse menu-dropdown" id="sidebarReport">
                <ul class="nav nav-sm flex-column">
                  <li class="nav-item">
                    <a
                      href="report_agent_daily_realtime.html"
                      class="nav-link"
                      data-key="t-report"
                      >Daily Summary Report</a
                    >
                  </li>

                  <li class="nav-item">
                    <a
                      href="report_withdraw_channel.html"
                      class="nav-link"
                      data-key="t-users"
                      >Withdraw Channel Report</a
                    >
                  </li>

                  <li class="nav-item">
                    <a
                      href="report_withdraw.html"
                      class="nav-link"
                      data-key="t-report"
                      >Withdraw Activity Report</a
                    >
                  </li>

                  <li class="nav-item">
                    <a
                      href="report_deposit.html"
                      class="nav-link"
                      data-key="t-report"
                      >Deposit Activity Report</a
                    >
                  </li>

                  <!--                            <li class="nav-item">-->
                  <!--                                <a href="--><!--" class="nav-link" data-key="t-report"> Withdraw Slip</a>-->
                  <!--                            </li>-->
                </ul>
              </div>

              <li class="nav-item">
                <a
                  class="nav-link menu-link"
                  href="withdraw_fund.html"
                >
                  <i class="ri-file-transfer-line"></i>
                  <span data-key="t-users">Withdraw Fund</span>
                </a>
              </li>
              <li class="nav-item">
                <a
                  class="nav-link menu-link"
                  href="scanslip-step-import.html"
                >
                  <i class="ri-qr-code-fill"></i>
                  <span data-key="t-users">STEP 1 Import Slip</span>
                </a>
              </li>
              <li class="nav-item">
                <a
                  class="nav-link menu-link"
                  href="scanslip-step-verify.html"
                >
                  <i class="ri-qr-code-fill"></i>
                  <span data-key="t-users">STEP 2 Verify Slip</span>
                </a>
              </li>

              <a
                class="nav-link menu-link"
                href="#sidebarTools"
                data-bs-toggle="collapse"
                role="button"
                aria-expanded="false"
                aria-controls="sidebarTools"
              >
                <i class="ri-article-line"></i>
                <span data-key="t-report">Tools</span>
              </a>
              <div class="collapse menu-dropdown" id="sidebarTools">
                <ul class="nav nav-sm flex-column">
                  <li class="nav-item">
                    <a
                      href="black_list.html"
                      class="nav-link"
                      data-key="t-report"
                      >Blacklist Manage</a
                    >
                  </li>
                </ul>
              </div>

              <li class="nav-item">
                <a
                  class="nav-link menu-link"
                  href="profile.html"
                >
                  <i class="ri-folder-user-line"></i>
                  <span data-key="t-users">Profile</span>
                </a>
              </li>
              <a
                class="nav-link menu-link"
                href="#sidebarSetupUser"
                data-bs-toggle="collapse"
                role="button"
                aria-expanded="false"
                aria-controls="sidebarSetupUser"
              >
                <i class="ri-folder-user-line"></i>
                <span data-key="t-setup-user">Settings</span>
              </a>
              <div class="collapse menu-dropdown" id="sidebarSetupUser">
                <ul class="nav nav-sm flex-column">
                  <li class="nav-item">
                    <a
                      href="user_group.html"
                      class="nav-link"
                      data-key="t-user-group"
                    >
                      User Groups
                    </a>
                  </li>
                  <li class="nav-item">
                    <a
                      href="user_permission.html"
                      class="nav-link"
                      data-key="t-permission"
                    >
                      Group Permission
                    </a>
                  </li>
                  <li class="nav-item">
                    <a
                      href="user.html"
                      class="nav-link"
                      data-key="t-users"
                    >
                      Sub Users
                    </a>
                  </li>
                </ul>
              </div>
              <li class="nav-item">
                <a
                  class="nav-link menu-link"
                  href="login/logout"
                >
                  <i class="ri-logout-box-r-line"></i>
                  <span data-key="t-users">Logout</span>
                </a>
              </li>
              <!--                    <li class="nav-item">-->
              <!--                        <a class="nav-link menu-link " href="https://vizpay.supportnow.me/" target="_blank">-->
              <!--                            <img src="--><!--images/ticket_now.jpg" alt="" height="60">-->
              <!--                        </a>-->
              <!--                    </li>-->
            </ul>
          </div>
          <!-- Sidebar -->
        </div>

        <div class="sidebar-background"></div>
      </div>
      <!-- Left Sidebar End -->
      <!-- Vertical Overlay-->
      <div class="vertical-overlay"></div>

      <!-- ============================================================== -->
      <!-- Start right Content here -->
      <!-- ============================================================== -->
      <div class="main-content">
        <div class="page-content">
          <div class="container-fluid">
            <!-- start page title -->
            <div class="row">
              <div class="col-12">
                <div
                  class="page-title-box d-sm-flex align-items-center justify-content-between"
                >
                  <h4 class="mb-sm-0">Scan Slip - Task</h4>

                  <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                      <li class="breadcrumb-item active">Scan Slip - Task</li>
                    </ol>
                  </div>
                </div>
              </div>
            </div>
            <!-- end page title -->

            <div class="row">
              <div class="col-lg-12">
                <div class="card">
                  <div class="card-header align-items-center d-flex">
                    <h4 class="card-title mb-0 flex-grow-1">Filter</h4>
                    <div class="float-end align-items-end text-right">
                      <button
                        type="button"
                        onClick="setDateAndSearchData('TODAY',this)"
                        class="btn btn-filter-date btn-sm btn-dark w-xs"
                      >
                        วันนี้
                      </button>
                      <button
                        type="button"
                        onClick="setDateAndSearchData('YESTERDAY',this)"
                        class="btn btn-filter-date btn-sm btn-dark w-xs"
                      >
                        เมื่อวาน
                      </button>
                      <button
                        type="button"
                        onClick="setDateAndSearchData('LAST7DAY',this)"
                        class="btn btn-filter-date btn-sm btn-secondary w-xs"
                      >
                        7 วันที่ผ่านมา
                      </button>
                      <button
                        type="button"
                        onClick="setDateAndSearchData('THISWEEK',this)"
                        class="btn btn-filter-date btn-sm btn-dark w-xs"
                      >
                        สัปดาห์นี้
                      </button>
                    </div>
                    <!-- <div class="flex-shrink-0"></div> -->
                  </div>
                  <div class="card-body">
                    <div class="live-preview mb-2">
                      <form action="#" id="form_search" method="post">
                        <div class="row g-3">
                          <div class="col-lg-3">
                            <label for="field1" class="form-label"
                              >Start Date</label
                            >
                            <input
                              type="text"
                              class="form-control"
                              id="start_date"
                              value="2025-04-26 00:00"
                            />
                          </div>
                          <div class="col-lg-3">
                            <label for="field1" class="form-label"
                              >End Date</label
                            >
                            <input
                              type="text"
                              class="form-control"
                              id="end_date"
                              value="2025-05-03 23:59"
                            />
                          </div>
                          <div class="col-lg-6">
                            <label for="txn_amount_search" class="form-label"
                              >จำนวนเงิน</label
                            >
                            <div class="input-group">
                              <input
                                type="text"
                                class="form-control number-only"
                                id="from_txn_amount_search"
                              />
                              <span class="input-group-text"> - </span>
                              <input
                                type="text"
                                class="form-control number-only"
                                id="to_txn_amount_search"
                              />
                            </div>
                          </div>

                          <div class="col-lg-3">
                            <label for="field1" class="form-label"
                              >Status</label
                            >
                            <select id="status_search" class="form-select">
                              <option value="">All</option>

                              <option value="WAIT_CALLBACK">รอ callback</option>
                              <option value="WAIT_CONFIRM">
                                รอยืนยันโยกเข้าร้านค้า
                              </option>

                              <option value="MOVED">
                                โยกเข้าร้านค้าเเล้ว [Confirm]
                              </option>

                              <option value="MOVE_FAILED">
                                สลิปนี้อยู่ในร้านอื่นอยู่ก่อนเเล้ว
                              </option>

                              <option value="TIMEOUT">
                                กรณี รอ Response จากธนาคารจนหมดเวลา
                              </option>
                              <option value="CANNOT_SCAN_QR_BILL">
                                กรณี QrText มีปัญหา ธนาคารไม่สามารถ response
                                กลับมาได้
                              </option>
                              <option value="ACCOUNT_WRONG">
                                สลิปไม่ใช่ของบริษัทฯ
                              </option>

                              <option value="TIMEOUT">Timeout</option>
                              <option value="ERROR">Error</option>
                            </select>
                          </div>

                          <div class="col-lg-3">
                            <label for="orderid_search" class="form-label"
                              >Username/UserId/Order Id</label
                            >
                            <input
                              type="text"
                              class="form-control"
                              id="orderid_search"
                              value=""
                            />
                          </div>

                          <div class="col-lg-6">
                            <label for="field1" class="form-label"
                              >ค้นหาด้วย Slip (สูงสุด 10 ไฟล์)</label
                            >
                            <input
                              type="file"
                              class="form-control"
                              id="inputFileSlipSearch"
                              aria-describedby="inputFileSlipSearchBTN"
                              accept="image/*"
                              aria-label="Upload"
                              multiple
                            />
                          </div>
                        </div>
                        <div>
                          <div class="row g-3 mt-3">
                            <div class="col-12 text-center">
                              <button
                                type="button"
                                class="btn btn-success waves-effect waves-light"
                                onclick="get_datatable_list();"
                              >
                                <i class="mdi mdi-filter-plus-outline"></i>
                                Search
                              </button>
                              <button
                                type="reset"
                                class="btn btn-soft-dark waves-effect waves-light"
                                onclick="setTimeout(window.location.reload(),300)"
                              >
                                <i class="mdi mdi-filter-remove-outline"></i>
                                Clear
                              </button>
                            </div>
                          </div>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-lg-12">
                <div class="card">
                  <div class="card-header align-items-center d-flex">
                    <h4 class="card-title mb-0 flex-grow-1">
                      <button
                        class="btn btn-primary"
                        onClick="handleModalUploadSlip()"
                        type="button"
                      >
                        Upload Slip
                      </button>
                      <span class="text-danger"
                        >*รับเฉพาะสลิปที่มี Qrcode เท่านั้น และสูงสุดครั้งละ 10
                        สลิป
                      </span>
                    </h4>
                  </div>
                  <div class="card-body">
                    <div class="table-responsive">
                      <table
                        id="datatable-list"
                        class="table nowrap dt-responsive align-middle table-hover table-bordered"
                        style="width: 100%"
                      >
                        <thead>
                          <tr>
                            <th class="text-center" rowspan="2">
                              วันที่ทำรายการ<br />วันที่ดำเนินการล่าสุด
                            </th>
                            <th class="text-center" rowspan="2">คำสั่ง</th>

                            <th
                              class="text-center bg-info bg-op-th"
                              colspan="4"
                            >
                              Slip Info
                            </th>
                            <th
                              class="text-center bg-warning bg-op-th"
                              colspan="3"
                            >
                              Transactio Info
                            </th>
                            <th
                              class="text-center bg-primary bg-op-th"
                              colspan="2"
                            >
                              Statement Info
                            </th>

                            <!-- <th class="text-center" rowspan="2">QrText <br />Order Hash</th> -->
                          </tr>
                          <tr>
                            <th class="text-center bg-info bg-op-th">
                              Slip Date
                            </th>
                            <th class="text-center bg-info bg-op-th">Amount</th>
                            <th class="text-left bg-info bg-op-th">
                              From Name <br />
                              From No
                            </th>
                            <th class="text-left bg-info bg-op-th">
                              To Name <br />
                              To No
                            </th>

                            <th class="text-center bg-warning bg-op-th">
                              Created Date
                            </th>
                            <th class="text-center bg-warning bg-op-th">
                              Order Id<br />Username/UserId
                            </th>
                            <th class="text-center bg-warning bg-op-th">
                              Merchant Name
                            </th>

                            <th class="text-center bg-primary bg-op-th">
                              Statement Date
                            </th>
                            <th class="text-center bg-primary bg-op-th">
                              From No
                            </th>
                          </tr>
                        </thead>
                        <tbody></tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div
          class="modal fade"
          id="modalUploadSlipTask"
          data-bs-backdrop="static"
          data-bs-keyboard="false"
          tabindex="-1"
          aria-labelledby="modalUploadSlipTask"
          aria-modal="true"
        >
          <div class="modal-dialog">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title" id="eModalUploadSlipTask">
                  Upload Slip Mulitple
                </h5>
                <button
                  type="button"
                  class="btn-close"
                  data-bs-dismiss="modal"
                  aria-label="Close"
                ></button>
              </div>
              <div class="modal-body">
                <form action="javascript:void(0);" id="formUpdateBankAccount">
                  <input
                    type="hidden"
                    name="updateBankAccountTransactionID"
                    id="updateBankAccountTransactionID"
                    class="form-control"
                    value=""
                  />
                  <input
                    type="hidden"
                    name="updateBankAccountAgentID"
                    id="updateBankAccountAgentID"
                    class="form-control"
                    value=""
                  />
                  <div class="row g-3">
                    <div class="col-md-12">
                      <div>
                        <label for="updateBankAccountName" class="form-label"
                          ><span class="text-danger"
                            >*รับเฉพาะสลิปที่มี Qrcode เท่านั้น และสูงสุดครั้งละ
                            10 สลิป (jpg,jpeg,png)</span
                          ></label
                        >
                        <input
                          type="file"
                          class="form-control"
                          id="inputFileSlips"
                          aria-describedby="inputFileSlipButton"
                          accept="image/*"
                          aria-label="Upload"
                          multiple
                        />
                      </div>
                    </div>
                  </div>
                  <div class="row g-3 mt-2">
                    <div class="col-md-6">&nbsp;</div>
                    <div class="col-md-6">
                      <div class="hstack gap-2 justify-content-end">
                        <button
                          type="submit"
                          class="btn btn-success"
                          onclick="return confirmSlipTask();"
                        >
                          <i class="mdi mdi-check-bold"></i> Confirm
                        </button>
                      </div>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>

        <div
          class="modal fade"
          id="modalMoveToAgent"
          tabindex="-1"
          aria-labelledby="modalMoveToAgent"
          aria-modal="true"
        >
          <div class="modal-dialog modal-xl">
            <div class="modal-content" id="vueContainer">
              <div class="modal-header">
                <h5 class="modal-title" id="">โยกเข้าร้านค้า</h5>
                <button
                  type="button"
                  class="btn-close"
                  data-bs-dismiss="modal"
                  aria-label="Close"
                ></button>
              </div>
              <div class="modal-body">
                <form
                  action="javascript:void(0);"
                  id="formMoveToAgent"
                  enctype="multipart/form-data"
                >
                  <input
                    type="hidden"
                    name="confirmByOrderHash"
                    id="confirmByOrderHash"
                    class="form-control"
                    value=""
                  />
                  <div class="row">
                    <div class="col-md-6">
                      <div class="row g-3">
                        <strong>ข้อมูลรายการฝาก</strong>
                        <div class="col-md-6">
                          <div>
                            <label for="moveToAgentTxnDate" class="form-label"
                              >Statement Date</label
                            >
                            <input
                              type="text"
                              class="form-control"
                              name="moveToAgentTxnDate"
                              id="moveToAgentTxnDate"
                              disabled
                              placeholder=""
                            />
                          </div>
                        </div>
                        <!--end col-->
                        <div class="col-md-6">
                          <div>
                            <label for="moveToAgentTxn4Last" class="form-label"
                              >From Account</label
                            >
                            <input
                              type="text"
                              class="form-control"
                              name="moveToAgentTxn4Last"
                              id="moveToAgentTxn4Last"
                              disabled
                              placeholder=""
                            />
                          </div>
                        </div>
                        <!--end col-->
                        <div class="col-md-6">
                          <div>
                            <label for="moveToAgentTxnAmount" class="form-label"
                              >Amount</label
                            >
                            <input
                              type="text"
                              class="form-control"
                              name="moveToAgentTxnAmount"
                              id="moveToAgentTxnAmount"
                              disabled
                              placeholder=""
                            />
                          </div>
                        </div>
                        <!--end col-->
                        <div class="col-md-6">
                          <div>
                            <label for="moveToAgentTxnRemark" class="form-label"
                              >Remark</label
                            >
                            <textarea
                              class="form-control"
                              name="moveToAgentTxnRemark"
                              id="moveToAgentTxnRemark"
                              disabled
                              rows="3"
                            ></textarea>
                          </div>
                        </div>
                        <!--end col-->
                      </div>

                      <div class="row g-3">
                        <div
                          class="col-md-12"
                          v-if="trans_fail.length > 0"
                          v-clock
                        >
                          <div class="mb-4 table table-responsive">
                            <label for="basiInput" class="form-label"
                              >Transaction ที่เคยมีประวัติ Fail ล่าสุด</label
                            >
                            <table class="table table-nowrap">
                              <tbody>
                                <tr>
                                  <th
                                    style="width: 25%"
                                    class="text-center bg-soft-secondary"
                                  >
                                    Create Date
                                  </th>
                                  <th
                                    style="width: 25%"
                                    class="text-center bg-soft-secondary"
                                  >
                                    OrderId / Username
                                  </th>
                                  <th
                                    style="width: 25%"
                                    class="text-center bg-soft-secondary"
                                  >
                                    Amount
                                  </th>
                                  <th
                                    style="width: 25%"
                                    class="text-center bg-soft-secondary"
                                  >
                                    Status
                                  </th>
                                  <th
                                    style="width: 25%"
                                    class="text-center bg-soft-secondary"
                                  >
                                    API Parameter
                                  </th>
                                </tr>
                                <tr v-for="item in trans_fail">
                                  <th
                                    style="width: 25%"
                                    class="text-left"
                                    v-cloak
                                  >
                                    {{moment(item.created_date).format('DD/MM/YYYY
                                    HH:mm:ss')}}
                                  </th>
                                  <th
                                    style="width: 25%"
                                    class="text-left"
                                    v-cloak
                                  >
                                    {{item.order_id}}<br />{{item.username}}
                                  </th>
                                  <th
                                    style="width: 25%"
                                    class="text-right"
                                    v-cloak
                                  >
                                    {{formatNumber(item.amount,2)}}
                                  </th>
                                  <th class="text-center">
                                    <a
                                      href="javascript:void(0)"
                                      class="badge text-bg-danger"
                                      >Failed</a
                                    >
                                  </th>
                                  <th class="text-left">
                                    {{item.deposit_bank_name}}
                                    {{item.deposit_acc_no}}
                                    <br />{{item.deposit_acc_name}}
                                  </th>
                                </tr>
                              </tbody>
                            </table>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="row g-3">
                        <strong>โยกเข้ารานค้า</strong>
                        <div class="col-md-6">
                          <div>
                            <label for="moveToAgentUsername" class="form-label"
                              >Username/User ID/Order ID
                            </label>
                            <input
                              type="text"
                              class="form-control"
                              name="moveToAgentUsername"
                              id="moveToAgentUsername"
                              maxlength="75"
                            />
                          </div>
                        </div>
                        <div class="col-md-6">
                          <div>
                            <label
                              for="moveToAgentMatchRemark"
                              class="form-label"
                              >หมายเหตุ
                            </label>
                            <textarea
                              class="form-control"
                              name="moveToAgentMatchRemark"
                              id="moveToAgentMatchRemark"
                              rows="3"
                            ></textarea>
                          </div>
                        </div>
                        <!--end col-->
                        <div class="col-md-6"></div>
                        <!--end col-->
                      </div>
                    </div>
                  </div>
                  <div class="row g-3">
                    <div class="col-md-12">
                      <div class="hstack gap-2 justify-content-end">
                        <button
                          type="button"
                          class="btn btn-light"
                          data-bs-dismiss="modal"
                        >
                          Cancel
                        </button>
                        <button
                          type="submit"
                          class="btn btn-primary"
                          onclick="return confirmMoveToAgent();"
                        >
                          Save
                        </button>
                      </div>
                    </div>
                    <!--end col-->
                  </div>
                  <!--end row-->
                </form>
              </div>
            </div>
          </div>
        </div>

        <footer class="footer">
          <div class="container-fluid">
            <div class="row">
              <div class="col-sm-6">
                Copyright
                <script>
                  document.write(new Date().getFullYear());
                </script>
                © member.sugarpay66.io
              </div>
              <div class="col-sm-6"></div>
            </div>
          </div>
        </footer>
      </div>
      <!-- end main content-->
    </div>
    <!-- END layout-wrapper -->

    <!--start back-to-top-->
    <button
      onclick="topFunction()"
      class="btn btn-primary btn-icon"
      id="back-to-top"
    >
      <i class="ri-arrow-up-line"></i>
    </button>
    <!--end back-to-top-->

    <!--preloader-->
    <div id="preloader">
      <div id="status">
        <div class="spinner-border text-primary avatar-sm" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
      </div>
    </div>

    <div
      class="modal fade"
      id="modelPreviewImage"
      tabindex="-1"
      aria-labelledby="modelPreviewImage"
      aria-modal="true"
    >
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="exampleModalgridLabel">
              Preview Image
            </h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body">
            <img src="" id="imgPreviewImage" style="width: 100%" alt="" />
          </div>
        </div>
      </div>
    </div>

    <!-- JAVASCRIPT -->
    <script src="public/assets/libs/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="public/assets/libs/simplebar/simplebar.min.js"></script>
    <script src="public/assets/libs/node-waves/waves.min.js"></script>
    <script src="public/assets/libs/feather-icons/feather.min.js"></script>
    <script src="public/assets/js/pages/plugins/lord-icon-2.1.0.js"></script>
    <script src="public/assets/js/plugins.js?date=202403042044"></script>

    <script src="public/assets/js/pages/notifications.init.js"></script>

    <script
      src="https://code.jquery.com/jquery-3.6.0.min.js"
      integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4="
      crossorigin="anonymous"
    ></script>

    <!--datatable js-->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <!-- <script src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script> -->
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>

    <!-- Sweet Alerts js -->
    <script src="public/assets/libs/sweetalert2/sweetalert2.min.js"></script>

    <script src="public/assets/libs/flatpickr/flatpickr.min.js"></script>

    <!-- Sweet alert init js-->
    <script src="public/assets/js/pages/sweetalerts.init.js"></script>

    <!-- Moment js -->
    <script src="public/assets/libs/moment/moment.js"></script>

    <script
      src="https://cdnjs.cloudflare.com/ajax/libs/jquery.blockUI/2.70/jquery.blockUI.min.js"
      integrity="sha512-eYSzo+20ajZMRsjxB6L7eyqo5kuXuS2+wEbbOkpaur+sA2shQameiJiWEzCIDwJqaB0a4a6tCuEvCOBHUg3Skg=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    ></script>
    <script
      src="https://cdnjs.cloudflare.com/ajax/libs/jquery.blockUI/2.70/jquery.blockUI.js"
      integrity="sha512-QSb5le+VXUEVEQbfljCv8vPnfSbVoBF/iE+c6MqDDqvmzqnr4KL04qdQMCm0fJvC3gCWMpoYhmvKBFqm1Z4c9A=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    ></script>
    <!-- <script src="public/assets/js/pages/datatables.init.js"></script> -->

    <!-- dropzone min -->
    <script src="public/assets/libs/dropzone/dropzone-min.js"></script>
    <!-- filepond js -->
    <script src="public/assets/libs/filepond/filepond.min.js"></script>
    <script src="public/assets/libs/filepond-plugin-image-preview/filepond-plugin-image-preview.min.js"></script>
    <script src="public/assets/libs/filepond-plugin-file-validate-size/filepond-plugin-file-validate-size.min.js"></script>
    <script src="public/assets/libs/filepond-plugin-image-exif-orientation/filepond-plugin-image-exif-orientation.min.js"></script>
    <script src="public/assets/libs/filepond-plugin-file-encode/filepond-plugin-file-encode.min.js"></script>

    <!-- App js -->
    <script src="public/assets/js/app.js?t=1746205200"></script>
    <script>
      var tableDeposit = {
        table: "",
        tableDrawStatus: true,
        tooltipTriggerList: "",
        tooltipList: "",
      };
      document.addEventListener("DOMContentLoaded", function () {
        new DataTable(".alternative-pagination", {});
      });
      $(document).ready(function () {});

      function unshowAnnouncement(id) {
        setCookie("cookie_announcement", id, 7);
        $("#modalAnnouncement").modal("hide");
      }

      function setCookie(name, value, days) {
        var expires = "";
        if (days) {
          var date = new Date();
          date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
          expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + (value || "") + expires + "; path=/";
      }
      function getCookie(name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(";");
        for (var i = 0; i < ca.length; i++) {
          var c = ca[i];
          while (c.charAt(0) === " ") c = c.substring(1, c.length);
          if (c.indexOf(nameEQ) === 0)
            return c.substring(nameEQ.length, c.length);
        }
        return null;
      }

      $(document).on("click", "img.previewImage", function () {
        let src = $(this).attr("src");
        $("#imgPreviewImage").attr("src", src);

        $("#modelPreviewImage").modal("show");
      });

      function formatNumber(value, digit = 2) {
        var val = isNaN(value) ? 0 : value;
        var number = parseFloat(val).toFixed(digit).toLocaleString(undefined, {
          maximumFractionDigits: digit,
        });
        return number.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
      }
      function preview_image(event, obj) {
        var output = document.getElementById("show_" + obj.id);
        output.src = URL.createObjectURL(event.target.files[0]);
      }

      function copyButton(elm, copyText, afterTextButton) {
        copyToClipboard(copyText);
        $(elm).html(afterTextButton);
      }
      function copyToClipboard(text) {
        if (navigator.clipboard) {
          navigator.clipboard.writeText(text).then(
            function () {
              console.log("Text copied to clipboard");
              Swal.fire({
                position: "top-end",
                icon: "success",
                title: "Text copied to clipboard",
                showConfirmButton: false,
                timer: 1500,
              });
            },
            function (err) {
              console.error("Could not copy text: ", err);
            }
          );
        } else {
          let input = document.createElement("textarea");
          input.style.position = "fixed";
          input.style.zIndex = 9999;
          input.value = text;
          document.body.appendChild(input);
          input.select();
          input.focus();
          document.execCommand("copy");
          document.body.removeChild(input);
        }
      }
    </script>

    <!-- <script lang="javascript" src="https://cdn.sheetjs.com/xlsx-latest/package/dist/xlsx.full.min.js"></script> -->
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.js"></script> -->

    <script src="public/assets/libs/jsQr/jimp.min.js"></script>
    <script src="public/assets/libs/jsQr/jsQR.min.js"></script>

    <!-- Vue -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/vue/2.3.4/vue.min.js"></script>

    <script>
      var url_ajax = "/scanslip_task/";
      let start_date = moment()
        .add(-7, "day")
        .format("YYYY-MM-DD 00:00")
        .toString();
      let end_date = moment().format("YYYY-MM-DD 23:59").toString();
      let table = "";
      let tableDrawStatus = true;
      let tooltipTriggerList = "";
      let tooltipList = "";
      let bank = { mdr_rate: 0 };
      let minimum_amount = 10;
      $(document).ready(function () {
        $("#start_date").flatpickr({
          enableTime: true,
          dateFormat: "Y-m-d H:i",
          time_24hr: true,
          defaultDate: [start_date],
          onChange: function (selectedDates, dateStr, instance) {
            start_date = dateStr;
          },
        });
        $("#end_date").flatpickr({
          enableTime: true,
          dateFormat: "Y-m-d H:i",
          time_24hr: true,
          defaultDate: [end_date],
          onChange: function (selectedDates, dateStr, instance) {
            end_date = dateStr;
          },
        });
        get_datatable_list();
      });

      var app = new Vue({
        el: "#vueContainer",
        created() {},
        data: function () {
          return {
            trans_fail: [],
          };
        },
        methods: {
          getTransactionFail: async function (orderHash) {
            var formdata = new FormData();
            formdata.append("order_hash", orderHash);
            var requestOptions = {
              method: "POST",
              body: formdata,
              redirect: "follow",
            };
            var res = await fetch(
              url_ajax + "get_transaction_fail",
              requestOptions
            ).then((response) => response.json());
            if (res.error == 0) {
              app.trans_fail = res.result;
            }
          },
        },
      });

      function setDateAndSearchData(amountDay, elm) {
        if (amountDay == "TODAY") {
          start_date = moment().format("YYYY-MM-DD 00:00").toString();
          end_date = moment().format("YYYY-MM-DD 23:59").toString();
        } else if (amountDay == "YESTERDAY") {
          start_date = moment()
            .subtract(1, "day")
            .format("YYYY-MM-DD 00:00")
            .toString();
          end_date = moment()
            .subtract(1, "day")
            .format("YYYY-MM-DD 23:59")
            .toString();
        } else if (amountDay == "LAST7DAY") {
          start_date = moment()
            .subtract(7, "day")
            .format("YYYY-MM-DD 00:00")
            .toString();
          end_date = moment().format("YYYY-MM-DD 23:59").toString();
        } else if (amountDay == "THISWEEK") {
          start_date = moment()
            .startOf("isoWeek")
            .format("YYYY-MM-DD 00:00")
            .toString();
          end_date = moment()
            .endOf("isoWeek")
            .format("YYYY-MM-DD 23:59")
            .toString();
        } else if (amountDay == "LASTWEEK") {
          start_date = moment()
            .subtract(1, "weeks")
            .startOf("isoWeek")
            .format("YYYY-MM-DD 00:00")
            .toString();
          end_date = moment()
            .subtract(1, "weeks")
            .endOf("isoWeek")
            .format("YYYY-MM-DD 23:59")
            .toString();
        } else if (amountDay == "THISMONTH") {
          start_date = moment()
            .startOf("month")
            .format("YYYY-MM-DD 00:00")
            .toString();
          end_date = moment()
            .endOf("month")
            .format("YYYY-MM-DD 23:59")
            .toString();
        } else if (amountDay == "LASTMONTH") {
          start_date = moment()
            .subtract(1, "months")
            .startOf("month")
            .format("YYYY-MM-DD 00:00")
            .toString();
          end_date = moment()
            .subtract(1, "months")
            .endOf("month")
            .format("YYYY-MM-DD 23:59")
            .toString();
        } else {
          start_date = moment().format("YYYY-MM-DD 00:00").toString();
          end_date = moment().format("YYYY-MM-DD 23:59").toString();
        }
        $("#start_date").val(start_date);
        $("#end_date").val(end_date);

        $(".btn-filter-date").removeClass("btn-secondary");
        $(".btn-filter-date").addClass("btn-dark");
        $(elm).addClass("btn-secondary");
        $(elm).removeClass("btn-dark");

        get_datatable_list();
      }

      $(".number-only").on("input", function () {
        this.value = this.value.replace(/[^0-9.]/g, "");
      });

      async function get_datatable_list() {
        var text_search = "";
        var status_search = $("#status_search option:selected").val().trim();
        var from_txn_amount_search = $("#from_txn_amount_search").val().trim();
        var to_txn_amount_search = $("#to_txn_amount_search").val().trim();
        var transaction_type_search = "";
        var orderid_search = $("#orderid_search").val().trim();
        var agent_id_search = "";
        var qrtext_search = await readqrcodeAll();

        $.fn.dataTable.ext.errMode = "none";
        table = $("#datatable-list").DataTable({
          pageLength: 50,
          destroy: true,
          processing: true,
          serverSide: true,
          searchDelay: 500,
          ajax: {
            url: url_ajax + "getDatatableList",
            type: "POST",
            data: {
              text_search: text_search,
              status_search: status_search,
              orderid_search: orderid_search,
              transaction_type_search: transaction_type_search,
              agent_id: agent_id_search,
              start_date: start_date + ":00",
              end_date: end_date + ":59",
              qrtext_search: qrtext_search,
              from_txn_amount: from_txn_amount_search,
              to_txn_amount: to_txn_amount_search,
            },
          },
          columnDefs: [
            {
              targets: [3],
              className: "text-right",
            },
            {
              targets: [8],
              className: "text-center",
            },
          ],
          columns: [
            {
              data: function (data, type, dataToSet) {
                return `${moment(data.created_date).format(
                  "YYYY-MM-DD HH:mm:ss"
                )}<br />${
                  data.updated_date
                    ? moment(data.updated_date).format("YYYY-MM-DD HH:mm:ss")
                    : "-"
                }`;
              },
            },
            {
              data: function (data, type, dataToSet) {
                console.log("data,aagent_status", data.agent_status);
                if (data.agent_status != "") {
                  return `<b><span class="text-danger">${data.agent_status}</span></b><br />${data.merchant_name}`;
                }
                if (data.status == "MOVED") {
                  // if(data.stm_match_agent_id){
                  if (data.scanslip_callback_date != null) {
                    return `<span class="badge text-dark bg-soft-secondary">Move [API]</span>  <br /> to ${
                      data.merchant_name ?? ""
                    }`;
                  } else {
                    if (
                      (data.stm_status == "MATCH" &&
                        data.stm_moveto != null &&
                        data.stm_moveto != "") ||
                      data.tran_order_id == "โยกเข้าร้านค้า"
                    ) {
                      return `<span class="badge text-dark bg-soft-secondary">Move</span>  <br /> to ${
                        data.merchant_name ?? ""
                      }`;
                    } else if (data.stm_status == "MATCH") {
                      return `<span class="badge text-dark bg-soft-success">Match</span> <small>[${
                        data.tran_code
                      }]</small> <br /> to ${data.merchant_name ?? ""}`;
                    } else if (data.stm_status == "REFUND") {
                      return `<span class="badge text-dark bg-soft-success">โอนคืนสำเร็จ</span> <small></small> <br /> to ${
                        data.from_account_no ?? ""
                      }`;
                    } else {
                      return `<span class="text-dark">${
                        data.stm_status
                      }!!</span> <br /> to ${data.merchant_name ?? ""}`;
                    }
                  }
                  // }
                } else if (data.status == "WAIT_CALLBACK") {
                  if (data.transaction_date_time) {
                    return `<span class="badge text-light bg-primary">Processing</span>`;
                  } else {
                    return `<span class="badge text-dark bg-info">Queq Scan Slip</span>`;
                  }
                } else if (data.status == "WAIT_CONFIRM") {
                  var btn = "";
                  if (
                    data.stm_match_agent_id == null &&
                    data.stm_status == "PENDING" &&
                    data.txn_hash != null
                  ) {
                    btn = `<button type="button" onclick="moveToAgent('${data.order_hash}','${data.stm_date}','${data.stm_from_acc}','${data.amount}','${data.stm_remark}');" class="btn btn-sm btn-success" style="padding: 0 4px;">กดโยกเข้าร้านค้า</button> `;
                    return btn;
                  } else if (
                    data.stm_status == "MATCH" &&
                    data.stm_moveto != null &&
                    data.stm_moveto != ""
                  ) {
                    return `<span class="badge text-dark bg-soft-secondary">Move</span>  <br /> to ${
                      data.merchant_name ?? ""
                    }`;
                  } else {
                    return `<span class="badge text-dark bg-soft-success">Match</span>`;
                  }
                } else if (data.status == "ACCOUNT_WRONG") {
                  return `<span class="text-danger">ไม่พบบัญชีนี้ในระบบ</span>`;
                } else if (data.status == "ADMIN_CHECK") {
                  return '<span class="badge text-dark bg-warning" style="font-size:10px">ให้ Admin ตรวจสอบ Slip</span>';
                } else if (data.status == "TIMEOUT") {
                  let btnDelete = "";
                  let remark = "";
                  if (data.transaction_date_time) {
                    remark = "ไม่พบรายการเกินเวลาที่กำหนด";
                    btnDelete = `<button type="button"
                                onclick="resetToProcess('${data.order_hash}')"
                                class="btn btn-sm btn-info" >
                                Reset Timeout
                            </button> |`;
                  } else {
                    remark =
                      "QrText ไม่ถูกต้อง กรุณาตรวจสอบที่หน้า Bank Statement แทน";
                  }
                  return `${btnDelete}  <span class="text-danger">${remark}</span>`;
                } else if (data.status == "ERROR") {
                  return `<span class="badge text-light bg-danger" style="font-size:12px">Error</span>`;
                } else {
                  return `<span class="badge text-dark bg-soft-dark">${data.status}</span>`;
                }
              },
            },
            {
              data: function (data, type, dataToSet) {
                return `${
                  data.transaction_date_time
                    ? moment(data.transaction_date_time).format(
                        "YYYY-MM-DD HH:mm:ss"
                      )
                    : "-"
                }`;
              },
            },
            {
              data: function (data, type, dataToSet) {
                return `${formatNumber(data.amount ?? 0, 2)}`;
              },
            },
            {
              data: function (data, type, dataToSet) {
                if (data.amount != null && data.amount != 0) {
                  return `${data.from_bank_name ?? ""}<br />${
                    data.from_account_name == "null"
                      ? "-"
                      : data.from_account_name
                  }<br />${data.from_account_no ?? ""}`;
                }
                return "";
              },
            },
            {
              data: function (data, type, dataToSet) {
                if (data.amount != null && data.amount != 0) {
                  return `${data.to_bank_name ?? ""}<br />${
                    data.to_account_name == "null" ? "-" : data.to_account_name
                  }<br />${data.to_proxy_id == "null" ? "" : data.to_proxy_id}`;
                }
                return "";
              },
            },

            {
              data: function (data, type, dataToSet) {
                if (data.tran_created == null || data.tran_created == "null") {
                  return "-";
                }
                return `${moment(data.tran_created).format(
                  "YYYY-MM-DD HH:mm:ss"
                )}`;
              },
            },
            {
              data: function (data, type, dataToSet) {
                if (
                  (data.tran_order_id == null ||
                    data.tran_order_id == "null") &&
                  (data.stm_admin_remark == null ||
                    data.stm_admin_remark == "null")
                ) {
                  return "-";
                }
                return `${data.tran_order_id ?? "-"}<br />${
                  data.stm_admin_remark ?? "-"
                }`;
              },
            },
            {
              data: function (data, type, dataToSet) {
                if (
                  data.merchant_name == null ||
                  data.merchant_name == "null"
                ) {
                  return "-";
                }
                return `${data.merchant_name}`;
              },
            },

            {
              data: function (data, type, dataToSet) {
                return `${
                  data.stm_date
                    ? moment(data.stm_date).format("YYYY-MM-DD HH:mm:ss")
                    : "-"
                }`;
              },
            },
            {
              data: function (data, type, dataToSet) {
                return `${data.stm_from_acc ?? ""}<br />${
                  data.stm_remark ?? ""
                }`;
              },
            },

            // {data: function(data, type, dataToSet) {
            //     return `${data.qrtext}`;
            // }},
          ],
          order: [[0, "desc"]],
        });

        tableDraw();
      }

      function tableDraw() {
        if (tableDrawStatus) {
          table.on("draw", function () {
            tooltipTriggerList = document.querySelectorAll(
              '[data-bs-toggle="tooltip"]'
            );
            tooltipList = [...tooltipTriggerList].map(
              (tooltipTriggerEl) => new bootstrap.Tooltip(tooltipTriggerEl)
            );
          });
          tableDrawStatus = false;
        }
      }

      const inputFileSlipSearch = document.getElementById(
        "inputFileSlipSearch"
      );
      async function readqrcodeAll() {
        const files = inputFileSlipSearch.files;
        const qrtexts = [];
        if (files.length > 10) {
          fileInputs.value = "";
          alert("รับเฉพาะสลิปที่มี Qrcode เท่านั้น และสูงสุดครั้งละ 10 สลิป");
          return false;
        }
        for (const file of files) {
          if (file && file.type.startsWith("image/")) {
            try {
              const buffer = await readFileAsBuffer(file);
              let qrCodeValue = await readQRCodeFromFile(buffer, 800);
              if (!qrCodeValue) {
                qrCodeValue = await readQRCodeFromFile(buffer, 1200);
                if (!qrCodeValue) {
                  qrCodeValue = await readQRCodeFromFile(buffer, 1500);
                  if (!qrCodeValue) {
                    qrCodeValue = await readQRCodeFromFile(buffer, 1980);
                    if (!qrCodeValue) {
                      qrCodeValue = await readQRCodeFromFile(buffer, 3090);
                    }
                  }
                }
              }
              if (qrCodeValue) {
                qrtexts.push(qrCodeValue);
              } else {
                console.log("No QR code detected.");
              }
            } catch (error) {
              console.error("Error reading or decoding QR code:", error);
            }
          }
        }
        if (qrtexts.length < files) {
          alert("พบบ้างรายการไม่สามารถอ่าน Qrcode ได้");
          return qrtexts.toString();
        } else {
          return qrtexts.toString();
        }
      }

      function handleModalUploadSlip() {
        $("#modalUploadSlipTask").modal("show");
      }

      const fileInputs = document.getElementById("inputFileSlips");
      async function confirmSlipTask() {
        const files = fileInputs.files;
        console.log("readQrcodeMulti", files);
        const qrtexts = [];
        if (files.length > 10) {
          fileInputs.value = "";
          alert("รับเฉพาะสลิปที่มี Qrcode เท่านั้น และสูงสุดครั้งละ 10 สลิป");
          return false;
        }
        for (const file of files) {
          if (file && file.type.startsWith("image/")) {
            console.log("file", file);
            try {
              const buffer = await readFileAsBuffer(file);
              let qrCodeValue = await readQRCodeFromFile(buffer, 800);
              if (!qrCodeValue) {
                qrCodeValue = await readQRCodeFromFile(buffer, 1200);
                if (!qrCodeValue) {
                  qrCodeValue = await readQRCodeFromFile(buffer, 1500);
                  if (!qrCodeValue) {
                    qrCodeValue = await readQRCodeFromFile(buffer, 1980);
                    if (!qrCodeValue) {
                      qrCodeValue = await readQRCodeFromFile(buffer, 3090);
                    }
                  }
                }
              }
              if (qrCodeValue) {
                qrtexts.push(qrCodeValue);
              } else {
                console.log("No QR code detected.");
                alert("No QR code detected.");
              }
            } catch (error) {
              alert("Error reading or decoding QR code");
              console.error("Error reading or decoding QR code:", error);
            }
          }
        }

        try {
          if (qrtexts.length < files) {
            Swal.fire({
              title: `จำนวนรายการที่อ่าน Qrcode คือ ${qrtexts.length} รายการ`,
              text: "ต้องการดำเนินการต่อหรือไม่",
              icon: "warning",
              showCancelButton: true,
              confirmButtonText: "ยืนยัน",
              cancelButtonText: "ยกเลิก",
            }).then(async (result) => {
              if (result.isConfirmed) {
                await requestSlipVerify(qrtexts);
              }
            });
          } else {
            await requestSlipVerify(qrtexts);
          }
        } catch (error) {
          alert("Request slip verify error.");
        }

        console.log("qrtexts", qrtexts);
      }

      function requestSlipVerify(arrSlipVerify) {
        $("#modalUploadSlipTask").modal("hide");
        fileInputs.value = "";
        if (arrSlipVerify.length > 0) {
          $.ajax({
            url: url_ajax + "request/slip/verify",
            type: "post",
            async: false,
            data: {
              qrtext: arrSlipVerify,
            },
            dataType: "json",
            success: function (res) {
              console.log("res", res);

              if (res.error == 0) {
                Swal.fire({
                  icon: "success",
                  title: `ตรวจสอบสถานะที่หน้า Scan Slip - Task`,
                  confirmButtonText: "ปิด",
                }).then(async (result) => {
                  await get_datatable_list();
                });
              } else {
                Swal.fire({
                  icon: "warning",
                  title: `บันทึกข้อมูลไม่สำเร็จ`,
                  text: "สาเหตุจาก QrText ซ้ำหรืออื่นๆ",
                  confirmButtonText: "ปิด",
                });
              }
            },
          });
        }

        return false;
      }

      function readFileAsBuffer(file) {
        return new Promise((resolve, reject) => {
          const reader = new FileReader();

          reader.onload = function (e) {
            const buffer = e.target.result;
            resolve(buffer);
          };

          reader.onerror = function (e) {
            reject(e.target.error);
          };

          reader.readAsArrayBuffer(file);
        });
      }

      async function readQRCodeFromFile(buffer, setwidth = 800) {
        try {
          const image = await Jimp.read(buffer);
          const isHorizontal = image.getWidth() > image.getHeight();
          const ratio = isHorizontal
            ? image.getWidth() / image.getHeight()
            : image.getHeight() / image.getWidth();
          const width = setwidth; // set the width you want
          const height = isHorizontal ? width / ratio : width * ratio;

          image.resize(width, height);

          const { data, width: qrWidth, height: qrHeight } = image.bitmap;
          const qrCode = jsQR(data, qrWidth, qrHeight);

          return qrCode ? qrCode.data : null;
        } catch (error) {
          throw new Error("Error reading or decoding QR code");
        }
      }

      async function moveToAgent(
        order_hash,
        stm_date,
        stm_from_acc,
        amount,
        stm_remark
      ) {
        // $.blockUI({ css: { backgroundColor: '#fff', color: '#000' , borderColor : '#fff'  } , message : 'กำลังโหลดข้อมูล' });

        document.getElementById("confirmByOrderHash").value = order_hash;
        document.getElementById("moveToAgentTxnDate").value = stm_date;
        document.getElementById("moveToAgentTxn4Last").value = stm_from_acc;
        document.getElementById("moveToAgentTxnAmount").value = formatNumber(
          parseFloat(amount),
          2
        );
        document.getElementById("moveToAgentTxnRemark").value = stm_remark;

        await app.getTransactionFail(order_hash);

        $("#modalMoveToAgent").modal("show");

        $("#moveToAgentAgentID").select2({
          dropdownParent: $("#modalMoveToAgent"),
        });
        // setTimeout(() => {
        //     $.unblockUI();
        // }, 888);
      }

      async function confirmMoveToAgent() {
        const formData = new FormData($("#formMoveToAgent")[0]);
        Swal.fire({
          title: "ยืนยันโยกเข้าร้านค้าใช่หรือไม่",
          icon: "warning",
          showCancelButton: true,
          confirmButtonText: "ยืนยัน",
          cancelButtonText: "ยกเลิก",
        }).then((result) => {
          if (result.isConfirmed) {
            $.ajax({
              url: url_ajax + "confirm/slip/verify/order_hash",
              type: "post",
              async: false,
              data: formData,
              contentType: false,
              processData: false,
              dataType: "json",
              success: async function (res) {
                if (res.error == 0) {
                  Swal.fire({
                    icon: "success",
                    title: `โยกรายการฝากเข้าร้านค้าเรียบร้อย`,
                    confirmButtonText: "ปิด",
                  }).then((result) => {
                    $("#modalMoveToAgent").modal("hide");
                  });
                } else {
                  Swal.fire({
                    icon: "warning",
                    title: "Warning",
                    text: "เกิดข้อผิดพลาด ไม่พบข้อมูลรายการฝาก",
                    showConfirmButton: true,
                    confirmButtonText: "รับทราบ",
                  });
                }
                $.blockUI({
                  css: {
                    backgroundColor: "#fff",
                    color: "#000",
                    borderColor: "#fff",
                  },
                  message: "กำลังโหลดข้อมูล",
                });
                await get_datatable_list();
                setTimeout(() => {
                  $.unblockUI();
                }, 888);
              },
            });
          }
        });
      }

      function resetToProcess(order_hash) {
        Swal.fire({
          title: "ยืนยันการ Reset รายการ",
          text: "คุณต้องการ Reset รายการ Scan Slip นี้ใช่หรือไม่",
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#d33",
          cancelButtonColor: "#ababab",
          confirmButtonText: "ยืนยัน",
          cancelButtonText: "ยกเลิก",
        }).then((result) => {
          if (result.isConfirmed) {
            $.ajax({
              url: url_ajax + "reset_timeout",
              type: "post",
              async: false,
              data: {
                order_hash: order_hash,
              },
              dataType: "json",
              success: function (res) {
                console.log("res", res);

                if (res.error == 0) {
                  Swal.fire({
                    icon: "success",
                    title: `Reset ข้อมูลเรียบร้อย`,
                    confirmButtonText: "ปิด",
                  }).then(async (result) => {
                    await get_datatable_list();
                  });
                } else {
                  Swal.fire({
                    icon: "warning",
                    title: `บันทึกข้อมูลไม่สำเร็จ`,
                    text: "กรุณานำสลิปเเจ้งทีมพัฒนาฯ",
                    confirmButtonText: "ปิด",
                  });
                }
              },
            });
          }
        });
      }

      function fill_text(elm, text) {
        $(elm).val(text);
      }
    </script>
  </body>
</html>
