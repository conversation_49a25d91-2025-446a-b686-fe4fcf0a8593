-- =====================================================
-- SugarPay Complete Database Schema
-- รองรับระบบ SugarPay ครบถ้วนตามความต้องการ
-- =====================================================

-- 1. ตาราง agents - ข้อมูลเอเจนต์/ผู้ให้บริการ
CREATE TABLE `agents` (
  `agent_id` int(11) NOT NULL AUTO_INCREMENT,
  `agent_code` varchar(50) NOT NULL UNIQUE,
  `agent_name` varchar(255) NOT NULL,
  `contact_person` varchar(255) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `status` enum('active','inactive','suspended') DEFAULT 'active',
  `is_delete` tinyint(1) DEFAULT 0,
  `created_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_date` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`agent_id`),
  KEY `idx_agent_code` (`agent_code`),
  KEY `idx_agent_status` (`status`, `is_delete`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. ตาราง merchants - ข้อมูลร้านค้า
CREATE TABLE `merchants` (
  `merchant_id` int(11) NOT NULL AUTO_INCREMENT,
  `agent_id` int(11) NOT NULL,
  `merchant_code` varchar(50) NOT NULL UNIQUE,
  `merchant_name` varchar(255) NOT NULL,
  `business_type` varchar(100) DEFAULT NULL,
  `contact_person` varchar(255) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `pin_code` varchar(255) DEFAULT NULL,
  `minimum_2fa` decimal(15,2) DEFAULT 1000.00,
  `status` enum('active','inactive','suspended') DEFAULT 'active',
  -- API Integration Fields
  `api_endpoint` varchar(500) DEFAULT NULL,
  `api_key` varchar(255) DEFAULT NULL,
  `bearer_token` varchar(500) DEFAULT NULL,
  `x_api_key` varchar(255) DEFAULT NULL,
  `ip_whitelist` text DEFAULT NULL,
  `webhook_url` varchar(500) DEFAULT NULL,
  `is_delete` tinyint(1) DEFAULT 0,
  `created_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_date` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`merchant_id`),
  KEY `idx_merchant_code` (`merchant_code`),
  KEY `idx_agent_merchant` (`agent_id`, `merchant_code`),
  KEY `idx_merchant_status` (`status`, `is_delete`),
  CONSTRAINT `fk_merchants_agent` FOREIGN KEY (`agent_id`) REFERENCES `agents` (`agent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. ตาราง merchant_balances - ยอดเงินของร้านค้า (4 ประเภท)
CREATE TABLE `merchant_balances` (
  `balance_id` int(11) NOT NULL AUTO_INCREMENT,
  `merchant_id` int(11) NOT NULL UNIQUE,
  `deposit_balance` decimal(15,2) DEFAULT 0.00,
  `withdraw_balance` decimal(15,2) DEFAULT 0.00,
  `frozen_balance` decimal(15,2) DEFAULT 0.00,
  `wait_confirm_amount` decimal(15,2) DEFAULT 0.00,
  `last_updated` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`balance_id`),
  UNIQUE KEY `uk_merchant_balance` (`merchant_id`),
  CONSTRAINT `fk_merchant_balances_merchant` FOREIGN KEY (`merchant_id`) REFERENCES `merchants` (`merchant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. ตาราง bank_accounts - ข้อมูลบัญชีธนาคาร (รองรับ 3 ประเภท, 10 บัญชีต่อประเภท)
CREATE TABLE `bank_accounts` (
  `bank_account_id` int(11) NOT NULL AUTO_INCREMENT,
  `agent_id` int(11) NOT NULL,
  `bank_type` enum('DEPOSIT','WITHDRAW','SAVINGS') NOT NULL,
  `bank_name` varchar(100) NOT NULL,
  `bank_code` varchar(10) DEFAULT NULL,
  `bank_acc_no` varchar(50) NOT NULL,
  `bank_acc_name` varchar(255) NOT NULL,
  `bank_token` varchar(255) DEFAULT NULL,
  `bank_promtpay_no` varchar(20) DEFAULT NULL,
  `priority` int(2) DEFAULT 1,
  `balance` decimal(15,2) DEFAULT 0.00,
  `core_bank_balance` decimal(15,2) DEFAULT NULL,
  `is_enable` tinyint(1) DEFAULT 1,
  `is_delete` tinyint(1) DEFAULT 0,
  `is_primary_withdraw_bank` tinyint(1) DEFAULT 0,
  `is_share_bank_account` tinyint(1) DEFAULT 0,
  `is_break` tinyint(1) DEFAULT 0,
  `last_break` timestamp NULL DEFAULT NULL,
  `last_online` timestamp NULL DEFAULT NULL,
  `admin_remark` text DEFAULT NULL,
  `created_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_date` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`bank_account_id`),
  KEY `idx_agent_bank_type` (`agent_id`, `bank_type`),
  KEY `idx_bank_priority` (`bank_type`, `priority`, `is_enable`),
  KEY `idx_bank_account_no` (`bank_acc_no`),
  CONSTRAINT `fk_bank_accounts_agent` FOREIGN KEY (`agent_id`) REFERENCES `agents` (`agent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 5. ตาราง bank_api_configs - การตั้งค่า Bank API
CREATE TABLE `bank_api_configs` (
  `api_config_id` int(11) NOT NULL AUTO_INCREMENT,
  `bank_account_id` int(11) NOT NULL,
  `api_endpoint` varchar(500) NOT NULL,
  `api_key` varchar(255) DEFAULT NULL,
  `api_secret` varchar(255) DEFAULT NULL,
  `bearer_token` varchar(500) DEFAULT NULL,
  `x_api_key` varchar(255) DEFAULT NULL,
  `timeout_seconds` int(3) DEFAULT 30,
  `retry_attempts` int(2) DEFAULT 3,
  `ip_whitelist` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_date` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`api_config_id`),
  KEY `idx_bank_api_config` (`bank_account_id`),
  CONSTRAINT `fk_bank_api_configs_bank` FOREIGN KEY (`bank_account_id`) REFERENCES `bank_accounts` (`bank_account_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 6. ตาราง mdr_fee_configs - การตั้งค่าค่าธรรมเนียม MDR
CREATE TABLE `mdr_fee_configs` (
  `fee_config_id` int(11) NOT NULL AUTO_INCREMENT,
  `agent_id` int(11) DEFAULT NULL,
  `merchant_id` int(11) DEFAULT NULL,
  `transaction_type` enum('deposit','withdraw','topup','transfer','settlement') NOT NULL,
  `fee_type` enum('percentage','fixed') NOT NULL,
  `fee_value` decimal(8,4) NOT NULL,
  `min_fee` decimal(10,2) DEFAULT NULL,
  `max_fee` decimal(10,2) DEFAULT NULL,
  `effective_from` timestamp DEFAULT CURRENT_TIMESTAMP,
  `effective_to` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_date` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`fee_config_id`),
  KEY `idx_fee_config_lookup` (`agent_id`, `merchant_id`, `transaction_type`, `is_active`),
  KEY `idx_fee_config_effective` (`effective_from`, `effective_to`),
  CONSTRAINT `fk_mdr_fee_configs_agent` FOREIGN KEY (`agent_id`) REFERENCES `agents` (`agent_id`),
  CONSTRAINT `fk_mdr_fee_configs_merchant` FOREIGN KEY (`merchant_id`) REFERENCES `merchants` (`merchant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 7. ตาราง bank_account_mdr_rates - อัตราค่าธรรมเนียมของแต่ละบัญชี
CREATE TABLE `bank_account_mdr_rates` (
  `rate_id` int(11) NOT NULL AUTO_INCREMENT,
  `bank_account_id` int(11) NOT NULL,
  `mdr_rate_qrcode` decimal(5,2) DEFAULT 0.00,
  `mdr_rate_transfer` decimal(5,2) DEFAULT 0.00,
  `mdr_rate_topup` decimal(5,2) DEFAULT 1.50,
  `mdr_rate_movement` decimal(5,2) DEFAULT 0.00,
  `is_withdraw_fund_movement` tinyint(1) DEFAULT 1,
  `is_withdraw_fund_topup` tinyint(1) DEFAULT 1,
  `effective_from` timestamp DEFAULT CURRENT_TIMESTAMP,
  `effective_to` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_date` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`rate_id`),
  KEY `idx_bank_mdr_rates` (`bank_account_id`, `is_active`),
  CONSTRAINT `fk_bank_account_mdr_rates_bank` FOREIGN KEY (`bank_account_id`) REFERENCES `bank_accounts` (`bank_account_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 8. ตาราง transactions - รายการธุรกรรมทั้งหมด (รองรับ Double Entry Accounting)
CREATE TABLE `transactions` (
  `transaction_id` int(11) NOT NULL AUTO_INCREMENT,
  `merchant_id` int(11) NOT NULL,
  `agent_id` int(11) NOT NULL,
  `bank_account_id` int(11) DEFAULT NULL,
  `txn_hash` varchar(100) NOT NULL UNIQUE COMMENT 'STM Ref ID',
  `order_id` varchar(100) DEFAULT NULL,
  `instruction_ref_no` varchar(100) DEFAULT NULL,
  `transaction_type` enum('deposit','withdraw','topup','transfer','settlement') NOT NULL,
  `deposit_type` varchar(50) DEFAULT NULL COMMENT 'QR, Transfer, etc.',
  `channel` varchar(50) DEFAULT NULL,
  `txn_amount` decimal(15,2) NOT NULL,
  `mdr_amount` decimal(10,2) DEFAULT 0.00,
  `withdraw_fee_amount` decimal(10,2) DEFAULT 0.00,
  `net_amount` decimal(15,2) GENERATED ALWAYS AS (`txn_amount` - `mdr_amount` - `withdraw_fee_amount`) STORED,
  `txn_status` enum('PENDING','SUCCESS','FAILED','CANCELLED','PENDING_CHECKSUM','WAIT_APPROVE') DEFAULT 'PENDING',
  `txn_date` timestamp NULL DEFAULT NULL,
  `settlement_date` date DEFAULT NULL,
  -- Bank Information
  `txn_bank_name` varchar(100) DEFAULT NULL,
  `txn_acc_no` varchar(50) DEFAULT NULL,
  `txn_acc_name` varchar(255) DEFAULT NULL,
  `txn_acc_4last` varchar(4) DEFAULT NULL,
  -- Customer Information
  `customer_name` varchar(255) DEFAULT NULL,
  `customer_phone` varchar(20) DEFAULT NULL,
  `customer_email` varchar(255) DEFAULT NULL,
  -- Additional Fields
  `qr_text` text DEFAULT NULL,
  `slip_image_url` varchar(500) DEFAULT NULL,
  `remark` text DEFAULT NULL,
  `admin_remark` text DEFAULT NULL,
  `created_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_date` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`transaction_id`),
  UNIQUE KEY `uk_txn_hash` (`txn_hash`),
  KEY `idx_merchant_txn` (`merchant_id`, `transaction_type`, `txn_date`),
  KEY `idx_agent_txn` (`agent_id`, `txn_date`),
  KEY `idx_txn_status` (`txn_status`, `txn_date`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_settlement_date` (`settlement_date`),
  CONSTRAINT `fk_transactions_merchant` FOREIGN KEY (`merchant_id`) REFERENCES `merchants` (`merchant_id`),
  CONSTRAINT `fk_transactions_agent` FOREIGN KEY (`agent_id`) REFERENCES `agents` (`agent_id`),
  CONSTRAINT `fk_transactions_bank` FOREIGN KEY (`bank_account_id`) REFERENCES `bank_accounts` (`bank_account_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 9. ตาราง balance_logs - ประวัติการเปลี่ยนแปลงยอดเงิน (Audit Trail)
CREATE TABLE `balance_logs` (
  `log_id` int(11) NOT NULL AUTO_INCREMENT,
  `merchant_id` int(11) NOT NULL,
  `transaction_id` int(11) DEFAULT NULL,
  `balance_type` enum('deposit','withdraw','frozen','wait_confirm') NOT NULL,
  `operation` enum('credit','debit','freeze','unfreeze','transfer_in','transfer_out') NOT NULL,
  `amount_before` decimal(15,2) NOT NULL,
  `amount_change` decimal(15,2) NOT NULL,
  `amount_after` decimal(15,2) NOT NULL,
  `description` varchar(500) DEFAULT NULL,
  `reference_id` varchar(100) DEFAULT NULL,
  `created_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`log_id`),
  KEY `idx_merchant_balance_log` (`merchant_id`, `balance_type`, `created_date`),
  KEY `idx_transaction_log` (`transaction_id`),
  CONSTRAINT `fk_balance_logs_merchant` FOREIGN KEY (`merchant_id`) REFERENCES `merchants` (`merchant_id`),
  CONSTRAINT `fk_balance_logs_transaction` FOREIGN KEY (`transaction_id`) REFERENCES `transactions` (`transaction_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 10. ตาราง fee_transactions - รายการค่าธรรมเนียม
CREATE TABLE `fee_transactions` (
  `fee_transaction_id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_id` int(11) NOT NULL,
  `fee_config_id` int(11) DEFAULT NULL,
  `fee_type` enum('mdr','withdraw','settlement') NOT NULL,
  `calculated_fee` decimal(10,2) NOT NULL COMMENT 'ค่าธรรมเนียมที่คำนวณได้',
  `actual_fee` decimal(10,2) NOT NULL COMMENT 'ค่าธรรมเนียมที่เรียกเก็บจริง',
  `fee_rate` decimal(8,4) DEFAULT NULL,
  `base_amount` decimal(15,2) NOT NULL,
  `calculation_method` varchar(100) DEFAULT NULL,
  `created_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`fee_transaction_id`),
  KEY `idx_transaction_fee` (`transaction_id`),
  KEY `idx_fee_config` (`fee_config_id`),
  CONSTRAINT `fk_fee_transactions_transaction` FOREIGN KEY (`transaction_id`) REFERENCES `transactions` (`transaction_id`),
  CONSTRAINT `fk_fee_transactions_config` FOREIGN KEY (`fee_config_id`) REFERENCES `mdr_fee_configs` (`fee_config_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 11. ตาราง double_entry_ledger - บัญชีแยกประเภทสำหรับ Double Entry Accounting
CREATE TABLE `double_entry_ledger` (
  `ledger_id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_id` int(11) NOT NULL,
  `merchant_id` int(11) NOT NULL,
  `account_type` enum('asset','liability','equity','revenue','expense') NOT NULL,
  `account_name` varchar(100) NOT NULL COMMENT 'เช่น Cash, Accounts_Receivable, Fee_Revenue',
  `debit_amount` decimal(15,2) DEFAULT 0.00,
  `credit_amount` decimal(15,2) DEFAULT 0.00,
  `balance_type` enum('deposit','withdraw','frozen','wait_confirm','fee','revenue') NOT NULL,
  `description` varchar(500) DEFAULT NULL,
  `created_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ledger_id`),
  KEY `idx_transaction_ledger` (`transaction_id`),
  KEY `idx_merchant_account` (`merchant_id`, `account_type`, `account_name`),
  KEY `idx_ledger_date` (`created_date`),
  CONSTRAINT `fk_double_entry_ledger_transaction` FOREIGN KEY (`transaction_id`) REFERENCES `transactions` (`transaction_id`),
  CONSTRAINT `fk_double_entry_ledger_merchant` FOREIGN KEY (`merchant_id`) REFERENCES `merchants` (`merchant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 12. ตาราง blacklist_accounts - บัญชีที่ถูกบล็อก
CREATE TABLE `blacklist_accounts` (
  `blacklist_id` int(11) NOT NULL AUTO_INCREMENT,
  `merchant_id` int(11) DEFAULT NULL,
  `agent_id` int(11) DEFAULT NULL,
  `blocked_account_no` varchar(50) NOT NULL,
  `blocked_name` varchar(255) DEFAULT NULL,
  `blocked_ip` varchar(45) DEFAULT NULL,
  `block_reason` varchar(500) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_date` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`blacklist_id`),
  KEY `idx_blocked_account` (`blocked_account_no`),
  KEY `idx_merchant_blacklist` (`merchant_id`),
  KEY `idx_agent_blacklist` (`agent_id`),
  CONSTRAINT `fk_blacklist_accounts_merchant` FOREIGN KEY (`merchant_id`) REFERENCES `merchants` (`merchant_id`),
  CONSTRAINT `fk_blacklist_accounts_agent` FOREIGN KEY (`agent_id`) REFERENCES `agents` (`agent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 13. ตาราง users - ผู้ใช้งานระบบ
CREATE TABLE `users` (
  `user_id` int(11) NOT NULL AUTO_INCREMENT,
  `agent_id` int(11) DEFAULT NULL,
  `merchant_id` int(11) DEFAULT NULL,
  `username` varchar(100) NOT NULL UNIQUE,
  `password` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `user_type` enum('admin','agent','merchant','staff') NOT NULL,
  `is_google2fa` tinyint(1) DEFAULT 0,
  `google2fa_secret` varchar(255) DEFAULT NULL,
  `last_login` timestamp NULL DEFAULT NULL,
  `login_attempts` int(2) DEFAULT 0,
  `is_locked` tinyint(1) DEFAULT 0,
  `locked_until` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `is_delete` tinyint(1) DEFAULT 0,
  `created_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_date` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_user_type` (`user_type`, `is_active`),
  KEY `idx_agent_user` (`agent_id`),
  KEY `idx_merchant_user` (`merchant_id`),
  CONSTRAINT `fk_users_agent` FOREIGN KEY (`agent_id`) REFERENCES `agents` (`agent_id`),
  CONSTRAINT `fk_users_merchant` FOREIGN KEY (`merchant_id`) REFERENCES `merchants` (`merchant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 14. ตาราง user_groups - กลุ่มผู้ใช้งาน
CREATE TABLE `user_groups` (
  `group_id` int(11) NOT NULL AUTO_INCREMENT,
  `agent_id` int(11) NOT NULL,
  `group_name_tha` varchar(255) NOT NULL,
  `group_name_eng` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `permissions` json DEFAULT NULL,
  `is_enable` tinyint(1) DEFAULT 1,
  `is_delete` tinyint(1) DEFAULT 0,
  `created_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_date` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`group_id`),
  KEY `idx_agent_group` (`agent_id`),
  CONSTRAINT `fk_user_groups_agent` FOREIGN KEY (`agent_id`) REFERENCES `agents` (`agent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 15. ตาราง user_group_members - สมาชิกในกลุ่มผู้ใช้งาน
CREATE TABLE `user_group_members` (
  `member_id` int(11) NOT NULL AUTO_INCREMENT,
  `group_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `assigned_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  `assigned_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`member_id`),
  UNIQUE KEY `uk_group_user` (`group_id`, `user_id`),
  KEY `idx_user_group` (`user_id`),
  CONSTRAINT `fk_user_group_members_group` FOREIGN KEY (`group_id`) REFERENCES `user_groups` (`group_id`),
  CONSTRAINT `fk_user_group_members_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- DEFAULT DATA INSERTION
-- =====================================================

-- Insert Default MDR Fee Configurations
INSERT INTO `mdr_fee_configs` (`agent_id`, `merchant_id`, `transaction_type`, `fee_type`, `fee_value`, `min_fee`, `max_fee`, `is_active`) VALUES
(NULL, NULL, 'deposit', 'percentage', 1.5000, NULL, NULL, 1),
(NULL, NULL, 'withdraw', 'fixed', 10.0000, NULL, NULL, 1),
(NULL, NULL, 'topup', 'percentage', 1.5000, NULL, NULL, 1),
(NULL, NULL, 'transfer', 'fixed', 0.0000, NULL, NULL, 1),
(NULL, NULL, 'settlement', 'fixed', 10.0000, NULL, NULL, 1);

-- =====================================================
-- INDEXES FOR PERFORMANCE OPTIMIZATION
-- =====================================================

-- Additional Indexes for better performance
CREATE INDEX `idx_transactions_comprehensive` ON `transactions` (`merchant_id`, `transaction_type`, `txn_status`, `txn_date`);
CREATE INDEX `idx_balance_logs_comprehensive` ON `balance_logs` (`merchant_id`, `balance_type`, `operation`, `created_date`);
CREATE INDEX `idx_fee_transactions_lookup` ON `fee_transactions` (`transaction_id`, `fee_type`);
CREATE INDEX `idx_bank_accounts_lookup` ON `bank_accounts` (`agent_id`, `bank_type`, `is_enable`, `priority`);
CREATE INDEX `idx_mdr_fee_configs_lookup` ON `mdr_fee_configs` (`transaction_type`, `is_active`, `effective_from`, `effective_to`);

-- =====================================================
-- VIEWS FOR COMMON QUERIES
-- =====================================================

-- View สำหรับดูยอดเงินรวมของร้านค้า
CREATE VIEW `v_merchant_balance_summary` AS
SELECT
    m.merchant_id,
    m.merchant_code,
    m.merchant_name,
    a.agent_code,
    a.agent_name,
    mb.deposit_balance,
    mb.withdraw_balance,
    mb.frozen_balance,
    mb.wait_confirm_amount,
    (mb.deposit_balance + mb.withdraw_balance + mb.frozen_balance + mb.wait_confirm_amount) AS total_balance,
    mb.last_updated
FROM merchants m
JOIN agents a ON m.agent_id = a.agent_id
LEFT JOIN merchant_balances mb ON m.merchant_id = mb.merchant_id
WHERE m.is_delete = 0 AND a.is_delete = 0;

-- View สำหรับดูธุรกรรมพร้อมข้อมูลที่เกี่ยวข้อง
CREATE VIEW `v_transaction_details` AS
SELECT
    t.transaction_id,
    t.txn_hash,
    t.order_id,
    t.transaction_type,
    t.txn_amount,
    t.mdr_amount,
    t.withdraw_fee_amount,
    t.net_amount,
    t.txn_status,
    t.txn_date,
    t.settlement_date,
    m.merchant_code,
    m.merchant_name,
    a.agent_code,
    a.agent_name,
    ba.bank_name,
    ba.bank_acc_no,
    t.customer_name,
    t.created_date
FROM transactions t
JOIN merchants m ON t.merchant_id = m.merchant_id
JOIN agents a ON t.agent_id = a.agent_id
LEFT JOIN bank_accounts ba ON t.bank_account_id = ba.bank_account_id;

-- View สำหรับดูค่าธรรมเนียมที่มีผล
CREATE VIEW `v_active_mdr_fees` AS
SELECT
    mfc.fee_config_id,
    a.agent_code,
    m.merchant_code,
    mfc.transaction_type,
    mfc.fee_type,
    mfc.fee_value,
    mfc.min_fee,
    mfc.max_fee,
    mfc.effective_from,
    mfc.effective_to
FROM mdr_fee_configs mfc
LEFT JOIN agents a ON mfc.agent_id = a.agent_id
LEFT JOIN merchants m ON mfc.merchant_id = m.merchant_id
WHERE mfc.is_active = 1
  AND NOW() BETWEEN mfc.effective_from AND COALESCE(mfc.effective_to, '2099-12-31 23:59:59');

-- =====================================================
-- STORED PROCEDURES FOR COMMON OPERATIONS
-- =====================================================

DELIMITER //

-- Procedure สำหรับอัพเดทยอดเงินและสร้าง balance log
CREATE PROCEDURE `sp_update_merchant_balance`(
    IN p_merchant_id INT,
    IN p_transaction_id INT,
    IN p_balance_type ENUM('deposit','withdraw','frozen','wait_confirm'),
    IN p_operation ENUM('credit','debit','freeze','unfreeze','transfer_in','transfer_out'),
    IN p_amount DECIMAL(15,2),
    IN p_description VARCHAR(500),
    IN p_created_by INT
)
BEGIN
    DECLARE v_current_balance DECIMAL(15,2) DEFAULT 0.00;
    DECLARE v_new_balance DECIMAL(15,2) DEFAULT 0.00;

    -- Start transaction
    START TRANSACTION;

    -- Get current balance
    CASE p_balance_type
        WHEN 'deposit' THEN
            SELECT COALESCE(deposit_balance, 0) INTO v_current_balance
            FROM merchant_balances WHERE merchant_id = p_merchant_id;
        WHEN 'withdraw' THEN
            SELECT COALESCE(withdraw_balance, 0) INTO v_current_balance
            FROM merchant_balances WHERE merchant_id = p_merchant_id;
        WHEN 'frozen' THEN
            SELECT COALESCE(frozen_balance, 0) INTO v_current_balance
            FROM merchant_balances WHERE merchant_id = p_merchant_id;
        WHEN 'wait_confirm' THEN
            SELECT COALESCE(wait_confirm_amount, 0) INTO v_current_balance
            FROM merchant_balances WHERE merchant_id = p_merchant_id;
    END CASE;

    -- Calculate new balance
    IF p_operation IN ('credit', 'transfer_in', 'unfreeze') THEN
        SET v_new_balance = v_current_balance + p_amount;
    ELSE
        SET v_new_balance = v_current_balance - p_amount;
    END IF;

    -- Update balance
    CASE p_balance_type
        WHEN 'deposit' THEN
            UPDATE merchant_balances SET deposit_balance = v_new_balance, updated_by = p_created_by
            WHERE merchant_id = p_merchant_id;
        WHEN 'withdraw' THEN
            UPDATE merchant_balances SET withdraw_balance = v_new_balance, updated_by = p_created_by
            WHERE merchant_id = p_merchant_id;
        WHEN 'frozen' THEN
            UPDATE merchant_balances SET frozen_balance = v_new_balance, updated_by = p_created_by
            WHERE merchant_id = p_merchant_id;
        WHEN 'wait_confirm' THEN
            UPDATE merchant_balances SET wait_confirm_amount = v_new_balance, updated_by = p_created_by
            WHERE merchant_id = p_merchant_id;
    END CASE;

    -- Insert balance log
    INSERT INTO balance_logs (
        merchant_id, transaction_id, balance_type, operation,
        amount_before, amount_change, amount_after, description, created_by
    ) VALUES (
        p_merchant_id, p_transaction_id, p_balance_type, p_operation,
        v_current_balance, p_amount, v_new_balance, p_description, p_created_by
    );

    COMMIT;
END //

DELIMITER ;

-- =====================================================
-- TRIGGERS FOR DATA INTEGRITY
-- =====================================================

DELIMITER //

-- Trigger สำหรับสร้าง merchant_balance เมื่อสร้าง merchant ใหม่
CREATE TRIGGER `tr_create_merchant_balance`
AFTER INSERT ON `merchants`
FOR EACH ROW
BEGIN
    INSERT INTO merchant_balances (merchant_id, deposit_balance, withdraw_balance, frozen_balance, wait_confirm_amount)
    VALUES (NEW.merchant_id, 0.00, 0.00, 0.00, 0.00);
END //

DELIMITER ;
