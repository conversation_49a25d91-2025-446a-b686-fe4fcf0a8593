<!DOCTYPE html>
<html
  lang="en"
  data-layout="vertical"
  data-topbar="light"
  data-sidebar="dark"
  data-sidebar-size="lg"
  data-sidebar-image="none"
  data-preloader="disable"
>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="sugarpay66 payment" />
    <meta name="keywords" content="sugarpay66 payment" />
    <meta name="author" content="member.sugarpay66.io" />
    <meta property="og:title" content="Merchant sugarpay66 payment" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="/" />
    <meta
      property="og:image"
      content="/public/assets//images/logo/android-chrome-512x512.png"
    />
    <title>ร้าน tiger-001 - Merchant sugarpay66 payment</title>

    <style>
      :root {
        --theme-bg_color_login: linear-gradient(to right, #bfa2a8, #6e5c60);
        --theme-color_primary: #6e5c60;
      }
    </style>

    <!-- For IE6+ -->
    <link
      rel="shortcut icon"
      href="public/assets/images/logo/favicon.ico"
      type="image/x-icon"
    />

    <!-- For all other browsers -->
    <link
      rel="icon"
      href="public/assets/images/logo/favicon.ico"
    />

    <!-- Different sizes -->
    <link
      rel="icon"
      href="public/assets/images/logo/favicon-16x16.png"
      sizes="16x16"
    />
    <link
      rel="icon"
      href="public/assets/images/logo/favicon-32x32.png"
      sizes="32x32"
    />

    <!-- For Modern Browsers with PNG Support -->
    <link
      rel="icon"
      type="image/png"
      href="public/assets/images/logo/apple-touch-icon.png"
    />

    <!-- Works in Firefox, Opera, Chrome and Safari -->
    <link
      rel="icon"
      href="public/assets/images/logo/apple-touch-icon.png"
    />

    <!-- For rounded corners and reflective shine in Apple devices -->
    <link
      rel="apple-touch-icon"
      href="public/assets/images/logo/apple-touch-icon.png"
    />

    <!-- Favicon without reflective shine -->
    <link
      rel="apple-touch-icon-precomposed"
      href="public/assets/images/logo/apple-touch-icon.png"
    />

    <!-- jsvectormap css -->
    <link
      href="public/assets/libs/jsvectormap/css/jsvectormap.min.css"
      rel="stylesheet"
      type="text/css"
    />

    <!--Swiper slider css-->
    <link
      href="public/assets/libs/swiper/swiper-bundle.min.css"
      rel="stylesheet"
      type="text/css"
    />

    <!-- Sweet Alert css-->
    <link
      href="public/assets/libs/sweetalert2/sweetalert2.min.css"
      rel="stylesheet"
      type="text/css"
    />

    <link
      href="public/assets/libs/flatpickr/flatpickr.min.css"
      rel="stylesheet"
      type="text/css"
    />

    <!-- Layout config Js -->
    <script src="public/assets/js/layout.js"></script>
    <!-- Bootstrap Css -->
    <link
      href="public/assets/css/bootstrap.min.css"
      rel="stylesheet"
      type="text/css"
    />
    <!-- Icons Css -->
    <link
      href="public/assets/css/icons.min.css"
      rel="stylesheet"
      type="text/css"
    />
    <!-- App Css-->
    <link
      href="public/assets/css/app.css?t=1746271831"
      rel="stylesheet"
      type="text/css"
    />

    <!-- custom Css-->
    <link
      href="public/assets/css/custom.css?t=1746271831"
      rel="stylesheet"
      type="text/css"
    />

    <!--datatable css-->
    <link
      rel="stylesheet"
      href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css"
    />
    <!--datatable responsive css-->
    <link
      rel="stylesheet"
      href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap.min.css"
    />
    <link
      rel="stylesheet"
      href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.dataTables.min.css"
    />
    <style type="text/css">
      .text-bold {
        font-weight: bold;
      }
      .text-right {
        text-align: right;
      }
      .modal-xl {
        max-width: 1300px !important;
      }
      #modalAnnouncement img {
        width: 100%;
      }
      #modalAnnouncement p {
        margin-top: 0;
        margin-bottom: 0.2rem;
      }
    </style>
    <style></style>
  </head>

  <body>
    <!-- Begin page -->
    <div id="layout-wrapper">
      <header id="page-topbar">
        <div class="layout-width">
          <div class="navbar-header">
            <div class="d-flex">
              <!-- LOGO -->
              <div class="navbar-brand-box horizontal-logo">
                <a href="" class="logo logo-dark">
                  <span class="logo-sm">
                    <img
                      src="public/assets/images/logo/logo-sm.png"
                      alt=""
                      height="22"
                    />
                  </span>
                  <span class="logo-lg">
                    <img
                      src="public/assets/images/logo/logo.png"
                      alt=""
                      height="48"
                    />
                  </span>
                </a>

                <a href="index.html" class="logo logo-light">
                  <span class="logo-sm">
                    <img
                      src="public/assets/images/logo/logo-sm.png"
                      alt=""
                      height="22"
                    />
                  </span>
                  <span class="logo-lg">
                    <img
                      src="public/assets/images/logo/logo.png"
                      alt=""
                      height="48"
                    />
                  </span>
                </a>
              </div>

              <button
                type="button"
                class="btn btn-sm px-3 fs-16 header-item vertical-menu-btn topnav-hamburger"
                id="topnav-hamburger-icon"
              >
                <span class="hamburger-icon">
                  <span></span>
                  <span></span>
                  <span></span>
                </span>
              </button>
            </div>

            <div class="d-flex align-items-center">
              <div class="ms-1 header-item d-none d-sm-flex">
                <button
                  type="button"
                  class="btn btn-icon btn-topbar btn-ghost-secondary rounded-circle"
                  data-toggle="fullscreen"
                >
                  <i class="bx bx-fullscreen fs-22"></i>
                </button>
              </div>
              <div class="dropdown ms-sm-3 header-item topbar-user">
                <button
                  type="button"
                  class="btn"
                  id="page-header-user-dropdown"
                  data-bs-toggle="dropdown"
                  aria-haspopup="true"
                  aria-expanded="false"
                >
                  <span class="d-flex align-items-center">
                    <img
                      class="rounded-circle header-profile-user"
                      src="public/assets/images/logo/logo-sm.png"
                      alt="Header Avatar"
                    />
                    <span class="text-start ms-xl-2">
                      <span
                        class="d-none d-xl-inline-block ms-1 fw-medium user-name-text"
                        >tiger-001</span
                      >
                      <span
                        class="d-none d-xl-block ms-1 fs-12 text-muted user-name-sub-text"
                      ></span>
                    </span>
                  </span>
                </button>
                <div class="dropdown-menu dropdown-menu-end">
                  <!-- item-->
                  <h6 class="dropdown-header">ยินดีต้อนรับ tiger-001</h6>
                  <a
                    class="dropdown-item"
                    href="login/logout"
                    ><i
                      class="mdi mdi-logout text-muted fs-16 align-middle me-1"
                    ></i>
                    <span class="align-middle" data-key="t-logout"
                      >ออกจากระบบ</span
                    ></a
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <!-- ========== App Menu ========== -->
      <div class="app-menu navbar-menu">
        <!-- LOGO -->
        <div class="navbar-brand-box">
          <!-- Dark Logo-->
          <a href="" class="logo logo-dark">
            <span class="logo-sm">
              <img
                src="public/assets/images/logo/logo-sm.png"
                alt=""
                height="22"
              />
            </span>
            <span class="logo-lg">
              <img
                src="public/assets/images/logo/logo.png"
                alt=""
                height="48"
              />
            </span>
          </a>
          <!-- Light Logo-->
          <a href="" class="logo logo-light">
            <span class="logo-sm">
              <img
                src="public/assets/images/logo/logo-sm.png"
                alt=""
                height="22"
              />
            </span>
            <span class="logo-lg">
              <img
                src="public/assets/images/logo/logo.png"
                alt=""
                height="48"
              />
            </span>
          </a>
          <button
            type="button"
            class="btn btn-sm p-0 fs-20 header-item float-end btn-vertical-sm-hover"
            id="vertical-hover"
          >
            <i class="ri-record-circle-line"></i>
          </button>
        </div>

        <div id="scrollbar">
          <div class="container-fluid">
            <div id="two-column-menu"></div>
            <ul class="navbar-nav mt-3" id="navbar-nav">
              <li class="nav-item">
                <a
                  class="nav-link menu-link"
                  href="dashboard.html"
                >
                  <i class="ri-article-line"></i>
                  <span data-key="t-users">Dashboard</span>
                </a>
              </li>

              <li class="nav-item">
                <a
                  class="nav-link menu-link"
                  href="transaction.html"
                >
                  <i class="ri-article-line"></i>
                  <span data-key="t-users">Transaction</span>
                </a>
              </li>
              <li class="nav-item">
                <a
                  class="nav-link menu-link"
                  href="withdraw_approve.html"
                >
                  <i class="ri-article-line"></i>
                  <span data-key="t-users">Withdraw Approve</span>
                </a>
              </li>
              <li class="nav-item">
                <a
                  class="nav-link menu-link"
                  href="bank_statement.html"
                >
                  <i class="ri-bank-line"></i>
                  <span data-key="t-users">Statement</span>
                </a>
              </li>
              <li class="nav-item">
                <a
                  class="nav-link menu-link"
                  href="summary_transfer.html"
                >
                  <i class="ri-file-transfer-line"></i>
                  <span data-key="t-users">Settlement</span>
                </a>
              </li>

              <a
                class="nav-link menu-link"
                href="#sidebarReport"
                data-bs-toggle="collapse"
                role="button"
                aria-expanded="false"
                aria-controls="sidebarReport"
              >
                <i class="ri-article-line"></i>
                <span data-key="t-report">Report</span>
              </a>
              <div class="collapse menu-dropdown" id="sidebarReport">
                <ul class="nav nav-sm flex-column">
                  <li class="nav-item">
                    <a
                      href="report_agent_daily_realtime.html"
                      class="nav-link"
                      data-key="t-report"
                      >Daily Summary Report</a
                    >
                  </li>

                  <li class="nav-item">
                    <a
                      href="report_withdraw_channel.html"
                      class="nav-link"
                      data-key="t-users"
                      >Withdraw Channel Report</a
                    >
                  </li>

                  <li class="nav-item">
                    <a
                      href="report_withdraw.html"
                      class="nav-link"
                      data-key="t-report"
                      >Withdraw Activity Report</a
                    >
                  </li>

                  <li class="nav-item">
                    <a
                      href="report_deposit.html"
                      class="nav-link"
                      data-key="t-report"
                      >Deposit Activity Report</a
                    >
                  </li>

                  <!--                            <li class="nav-item">-->
                  <!--                                <a href="--><!--" class="nav-link" data-key="t-report"> Withdraw Slip</a>-->
                  <!--                            </li>-->
                </ul>
              </div>

              <li class="nav-item">
                <a
                  class="nav-link menu-link"
                  href="withdraw_fund.html"
                >
                  <i class="ri-file-transfer-line"></i>
                  <span data-key="t-users">Withdraw Fund</span>
                </a>
              </li>
              <li class="nav-item">
                <a
                  class="nav-link menu-link"
                  href="scanslip-step-import.html"
                >
                  <i class="ri-qr-code-fill"></i>
                  <span data-key="t-users">STEP 1 Import Slip</span>
                </a>
              </li>
              <li class="nav-item">
                <a
                  class="nav-link menu-link"
                  href="scanslip-step-verify.html"
                >
                  <i class="ri-qr-code-fill"></i>
                  <span data-key="t-users">STEP 2 Verify Slip</span>
                </a>
              </li>

              <a
                class="nav-link menu-link"
                href="#sidebarTools"
                data-bs-toggle="collapse"
                role="button"
                aria-expanded="false"
                aria-controls="sidebarTools"
              >
                <i class="ri-article-line"></i>
                <span data-key="t-report">Tools</span>
              </a>
              <div class="collapse menu-dropdown" id="sidebarTools">
                <ul class="nav nav-sm flex-column">
                  <li class="nav-item">
                    <a
                      href="black_list.html"
                      class="nav-link"
                      data-key="t-report"
                      >Blacklist Manage</a
                    >
                  </li>
                </ul>
              </div>

              <li class="nav-item">
                <a
                  class="nav-link menu-link"
                  href="profile.html"
                >
                  <i class="ri-folder-user-line"></i>
                  <span data-key="t-users">Profile</span>
                </a>
              </li>
              <a
                class="nav-link menu-link"
                href="#sidebarSetupUser"
                data-bs-toggle="collapse"
                role="button"
                aria-expanded="false"
                aria-controls="sidebarSetupUser"
              >
                <i class="ri-folder-user-line"></i>
                <span data-key="t-setup-user">Settings</span>
              </a>
              <div class="collapse menu-dropdown" id="sidebarSetupUser">
                <ul class="nav nav-sm flex-column">
                  <li class="nav-item">
                    <a
                      href="user_group.html"
                      class="nav-link"
                      data-key="t-user-group"
                    >
                      User Groups
                    </a>
                  </li>
                  <li class="nav-item">
                    <a
                      href="user_permission.html"
                      class="nav-link"
                      data-key="t-permission"
                    >
                      Group Permission
                    </a>
                  </li>
                  <li class="nav-item">
                    <a
                      href="user"
                      class="nav-link"
                      data-key="t-users"
                    >
                      Sub Users
                    </a>
                  </li>
                </ul>
              </div>
              <li class="nav-item">
                <a
                  class="nav-link menu-link"
                  href="login/logout"
                >
                  <i class="ri-logout-box-r-line"></i>
                  <span data-key="t-users">Logout</span>
                </a>
              </li>
              <!--                    <li class="nav-item">-->
              <!--                        <a class="nav-link menu-link " href="https://vizpay.supportnow.me/" target="_blank">-->
              <!--                            <img src="--><!--images/ticket_now.jpg" alt="" height="60">-->
              <!--                        </a>-->
              <!--                    </li>-->
            </ul>
          </div>
          <!-- Sidebar -->
        </div>

        <div class="sidebar-background"></div>
      </div>
      <!-- Left Sidebar End -->
      <!-- Vertical Overlay-->
      <div class="vertical-overlay"></div>

      <!-- ============================================================== -->
      <!-- Start right Content here -->
      <!-- ============================================================== -->
      <div class="main-content">
        <div class="page-content">
          <div class="container-fluid">
            <!-- start page title -->
            <div class="row">
              <div class="col-12">
                <div
                  class="page-title-box d-sm-flex align-items-center justify-content-between"
                >
                  <h4 class="mb-sm-0">&nbsp;</h4>

                  <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                      <li class="breadcrumb-item active">Profile</li>
                    </ol>
                  </div>
                </div>
              </div>
            </div>
            <!-- end page title -->

            <div class="row">
              <div class="col-lg-6">
                <div class="card mt-2">
                  <div class="card-header align-items-center d-flex">
                    <h4 class="card-title mb-0 flex-grow-1">
                      Withdraw Pin Code
                    </h4>
                    <div class="flex-shrink-0"></div>
                  </div>
                  <!-- end card header -->
                  <div class="card-body">
                    <div class="live-preview">
                      <form action="#" id="form_change_password" method="post">
                        <div class="row g-3">
                          <div class="col-lg-12">
                            <label for="pin_code" class="form-label"
                              >Pin Code ตัวเลข 6 หลัก
                              <span class="text-danger">*</span></label
                            >
                            <input
                              type="text"
                              class="form-control number-only"
                              required
                              id="pin_code"
                              maxlength="6"
                              minlength="6"
                            />
                          </div>
                          <div class="col-lg-12">
                            <label for="repeat_pin_code" class="form-label"
                              >Repeat-New Pin Code
                              <span class="text-danger">*</span></label
                            >
                            <input
                              type="text"
                              class="form-control number-only"
                              required
                              id="repeat_pin_code"
                              maxlength="6"
                              minlength="6"
                            />
                          </div>
                          <div class="col-12 text-center">
                            <button
                              type="button"
                              class="btn btn-danger waves-effect waves-light"
                              onclick="confirmSetUpPinCode();"
                            >
                              <i class="ti ti-key"></i> Setup Pin Code
                            </button>
                          </div>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
                <div class="card mt-2">
                  <div class="card-header align-items-center d-flex">
                    <h4 class="card-title mb-0 flex-grow-1">Profile</h4>
                    <div class="flex-shrink-0"></div>
                  </div>
                  <!-- end card header -->
                  <div class="card-body">
                    <div class="live-preview">
                      <form action="#" id="form_search" method="post">
                        <div class="row g-3">
                          <div class="col-lg-6">
                            <label for="field1" class="form-label"
                              >First Name</label
                            >
                            <input
                              type="text"
                              class="form-control"
                              disabled
                              value="tiger-001"
                            />
                          </div>
                          <div class="col-lg-6">
                            <label for="field1" class="form-label"
                              >Last Name</label
                            >
                            <input
                              type="text"
                              class="form-control"
                              disabled
                              value=""
                            />
                          </div>
                          <div class="col-lg-6">
                            <label for="field1" class="form-label"
                              >E-Mail</label
                            >
                            <input
                              type="text"
                              class="form-control"
                              disabled
                              value=""
                            />
                          </div>
                          <div class="col-lg-6">
                            <label for="field1" class="form-label"
                              >Telephone</label
                            >
                            <input
                              type="text"
                              class="form-control"
                              disabled
                              value=""
                            />
                          </div>
                          <div class="col-lg-6">
                            <label for="field1" class="form-label"
                              >Bank Name</label
                            >
                            <input
                              type="text"
                              class="form-control"
                              disabled
                              value="WAIT_BANK_ACCOUNT"
                            />
                          </div>
                          <div class="col-lg-6">
                            <label for="field1" class="form-label"
                              >Bank No</label
                            >
                            <input
                              type="text"
                              class="form-control"
                              disabled
                              value="**********"
                            />
                          </div>
                          <div class="col-lg-6">
                            <label for="field1" class="form-label"
                              >Merchant Name</label
                            >
                            <input
                              type="text"
                              class="form-control"
                              disabled
                              value="tiger-001"
                            />
                          </div>
                          <div class="col-lg-6">
                            <label for="field1" class="form-label"
                              >Merchant Deposit MDR Rate (%)</label
                            >
                            <input
                              type="text"
                              class="form-control"
                              disabled
                              value="1.50"
                            />
                          </div>
                          <div class="col-lg-6">
                            <label for="field1" class="form-label"
                              >Merchant Withdraw MDR Rate (%)</label
                            >
                            <input
                              type="text"
                              class="form-control"
                              disabled
                              value="0.00"
                            />
                          </div>
                          <div class="col-lg-12">
                            <label for="field1" class="form-label"
                              >API KEY</label
                            >
                            <div class="input-group">
                              <input
                                type="text"
                                class="form-control"
                                disabled
                                value="d79edc5c-e89667cb-1210320f-b0059db0"
                              />
                              <button
                                class="btn btn-warning"
                                type="button"
                                onclick="copyButton(this, 'd79edc5c-e89667cb-1210320f-b0059db0', 'Copied!!')"
                              >
                                Copy
                              </button>
                            </div>
                          </div>
                          <div class="col-lg-12">
                            <label for="field1" class="form-label"
                              >SECRET KEY</label
                            >
                            <div class="input-group">
                              <input
                                type="password"
                                class="form-control"
                                id="secretKeyInput"
                                disabled
                                value="c7b5f857-f418c9c6-c9f7e4b7-83c691ae"
                              />
                              <button
                                class="btn btn-primary"
                                type="button"
                                onclick="showSecretKey(this);"
                              >
                                Show
                              </button>
                              <button
                                class="btn btn-warning"
                                type="button"
                                onclick="copyButton(this, 'c7b5f857-f418c9c6-c9f7e4b7-83c691ae', 'Copied!!')"
                              >
                                Copy
                              </button>
                            </div>
                          </div>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
                <div class="card mt-2">
                  <div class="card-header align-items-center d-flex">
                    <h4 class="card-title mb-0 flex-grow-1">
                      Auto cancel withdraw transaction
                    </h4>
                    <div class="flex-shrink-0"></div>
                  </div>
                  <!-- end card header -->
                  <div class="card-body">
                    <div class="live-preview">
                      <form action="#" id="form_change_password" method="post">
                        <div class="row g-3">
                          <div class="col-lg-12">
                            <div class="form-check mb-2">
                              <input
                                class="form-check-input"
                                type="radio"
                                value="0"
                                name="is_auto_cancel_txn_when_close"
                                id="is_auto_cancel_txn_when_close0"
                                checked
                              />
                              <label
                                class="form-check-label"
                                for="is_auto_cancel_txn_when_close0"
                              >
                                Off/ปิด
                              </label>
                            </div>
                            <div class="form-check mb-2">
                              <input
                                class="form-check-input"
                                type="radio"
                                value="1"
                                name="is_auto_cancel_txn_when_close"
                                id="is_auto_cancel_txn_when_close1"
                              />
                              <label
                                class="form-check-label"
                                for="is_auto_cancel_txn_when_close1"
                              >
                                On/เปิด
                              </label>
                            </div>
                          </div>
                          <div class="col-lg-12">
                            <span class="text-danger"
                              >* การตั้งค่า On/เปิด :
                              จะมีผลเมื่อมีการประกาศปิดระบบถอนชั่วคราว
                              ระบบจะทำการปรับรายการถอนสถานะ CREATE
                              ที่ยังไม่ถูกส่งข้อมูลไปธนาคารเป็น FAILED
                              ยกเลิกรายการให้ทันทีโดยอัตโนมัติ</span
                            >
                          </div>
                          <div class="col-12 text-center">
                            <button
                              type="button"
                              class="btn btn-danger waves-effect waves-light"
                              onclick="confirmSaveAutoCancelWithdraw();"
                            >
                              <i class="ti ti-key"></i> Save
                            </button>
                          </div>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-lg-6">
                <div class="card mt-2">
                  <div class="card-header align-items-center d-flex">
                    <h4 class="card-title mb-0 flex-grow-1">
                      Change Password [เปลี่ยนรหัสผ่านครั้งล่าสุดเมื่อ :
                      2025-04-17 13:54]
                    </h4>
                    <div class="flex-shrink-0"></div>
                  </div>
                  <!-- end card header -->
                  <div class="card-body">
                    <div class="live-preview">
                      <form action="#" id="form_change_password" method="post">
                        <div class="row g-3">
                          <div class="col-lg-12">
                            <label for="field1" class="form-label"
                              >Old Password
                              <span class="text-danger">*</span></label
                            >
                            <input
                              type="text"
                              class="form-control"
                              required
                              id="old_password"
                            />
                          </div>
                          <div class="col-lg-12">
                            <label for="field1" class="form-label"
                              >New Password
                              <span class="text-danger">*</span></label
                            >
                            <input
                              type="text"
                              class="form-control"
                              required
                              id="new_password"
                            />
                          </div>
                          <div class="col-lg-12">
                            <label for="field1" class="form-label"
                              >Repeat-New Password
                              <span class="text-danger">*</span></label
                            >
                            <input
                              type="text"
                              class="form-control"
                              required
                              id="repeat_new_password"
                            />
                          </div>
                          <div class="col-12 text-center">
                            <button
                              type="button"
                              class="btn btn-danger waves-effect waves-light"
                              onclick="confirmChangePassword();"
                            >
                              <i class="ti ti-key"></i> Change
                            </button>
                          </div>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
                <div class="card mt-2">
                  <div class="card-header align-items-center d-flex">
                    <h4 class="card-title mb-0 flex-grow-1">
                      Default Callback URL
                    </h4>
                    <div class="flex-shrink-0"></div>
                  </div>
                  <!-- end card header -->
                  <div class="card-body">
                    <div class="live-preview">
                      <form action="#" method="post">
                        <div class="row g-3">
                          <div class="col-lg-12">
                            <label for="field1" class="form-label"
                              >Callback URL</label
                            >
                            <input
                              type="text"
                              class="form-control"
                              id="callback_url"
                              value=""
                            />
                          </div>
                          <div class="col-12 text-center">
                            <button
                              type="button"
                              class="btn btn-danger waves-effect waves-light"
                              onclick="confirmChangeCallbackUrl();"
                            >
                              <i class="ti ti-key"></i> Change
                            </button>
                          </div>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>

                <div class="card mt-2">
                  <div class="card-header align-items-center d-flex">
                    <h4 class="card-title mb-0 flex-grow-1">
                      Setting 2FA Google Authenticator
                    </h4>
                    <div class="flex-shrink-0"></div>
                  </div>
                  <!-- end card header -->
                  <div class="card-body">
                    <div class="live-preview">
                      <form action="#" id="form_change_password" method="post">
                        <div class="row g-3">
                          <div class="col-lg-4">
                            <div class="form-check mb-2">
                              <input
                                class="form-check-input"
                                type="radio"
                                value="0"
                                name="is_google2fa"
                                id="is_google2fa0"
                                checked
                              />
                              <label
                                class="form-check-label"
                                for="is_google2fa"
                              >
                                Off/ปิด
                              </label>
                            </div>
                            <div class="form-check mb-2">
                              <input
                                class="form-check-input"
                                type="radio"
                                value="1"
                                name="is_google2fa"
                                id="is_google2fa1"
                              />
                              <label
                                class="form-check-label"
                                for="is_google2fa1"
                              >
                                On/เปิด
                              </label>
                            </div>
                          </div>

                          <div class="col-12 text-center">
                            <button
                              type="button"
                              class="btn btn-danger waves-effect waves-light"
                              onclick="confirmSave2Fa();"
                            >
                              <i class="ti ti-key"></i> Save
                            </button>
                          </div>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- container-fluid -->
        </div>
        <!-- End Page-content -->
        <footer class="footer">
          <div class="container-fluid">
            <div class="row">
              <div class="col-sm-6">
                Copyright
                <script>
                  document.write(new Date().getFullYear());
                </script>
                © member.sugarpay66.io
              </div>
              <div class="col-sm-6"></div>
            </div>
          </div>
        </footer>
      </div>
      <!-- end main content-->
    </div>
    <!-- END layout-wrapper -->

    <!--start back-to-top-->
    <button
      onclick="topFunction()"
      class="btn btn-primary btn-icon"
      id="back-to-top"
    >
      <i class="ri-arrow-up-line"></i>
    </button>
    <!--end back-to-top-->

    <!--preloader-->
    <div id="preloader">
      <div id="status">
        <div class="spinner-border text-primary avatar-sm" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
      </div>
    </div>

    <div
      class="modal fade"
      id="modelPreviewImage"
      tabindex="-1"
      aria-labelledby="modelPreviewImage"
      aria-modal="true"
    >
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="exampleModalgridLabel">
              Preview Image
            </h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body">
            <img src="" id="imgPreviewImage" style="width: 100%" alt="" />
          </div>
        </div>
      </div>
    </div>

    <!-- JAVASCRIPT -->
    <script src="public/assets/libs/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="public/assets/libs/simplebar/simplebar.min.js"></script>
    <script src="public/assets/libs/node-waves/waves.min.js"></script>
    <script src="public/assets/libs/feather-icons/feather.min.js"></script>
    <script src="public/assets/js/pages/plugins/lord-icon-2.1.0.js"></script>
    <script src="public/assets/js/plugins.js?date=202403042044"></script>

    <script src="public/assets/js/pages/notifications.init.js"></script>

    <script
      src="https://code.jquery.com/jquery-3.6.0.min.js"
      integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4="
      crossorigin="anonymous"
    ></script>

    <!--datatable js-->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <!-- <script src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script> -->
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>

    <!-- Sweet Alerts js -->
    <script src="public/assets/libs/sweetalert2/sweetalert2.min.js"></script>

    <script src="public/assets/libs/flatpickr/flatpickr.min.js"></script>

    <!-- Sweet alert init js-->
    <script src="public/assets/js/pages/sweetalerts.init.js"></script>

    <!-- Moment js -->
    <script src="public/assets/libs/moment/moment.js"></script>

    <script
      src="https://cdnjs.cloudflare.com/ajax/libs/jquery.blockUI/2.70/jquery.blockUI.min.js"
      integrity="sha512-eYSzo+20ajZMRsjxB6L7eyqo5kuXuS2+wEbbOkpaur+sA2shQameiJiWEzCIDwJqaB0a4a6tCuEvCOBHUg3Skg=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    ></script>
    <script
      src="https://cdnjs.cloudflare.com/ajax/libs/jquery.blockUI/2.70/jquery.blockUI.js"
      integrity="sha512-QSb5le+VXUEVEQbfljCv8vPnfSbVoBF/iE+c6MqDDqvmzqnr4KL04qdQMCm0fJvC3gCWMpoYhmvKBFqm1Z4c9A=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    ></script>
    <!-- <script src="public/assets/js/pages/datatables.init.js"></script> -->

    <!-- dropzone min -->
    <script src="public/assets/libs/dropzone/dropzone-min.js"></script>
    <!-- filepond js -->
    <script src="public/assets/libs/filepond/filepond.min.js"></script>
    <script src="public/assets/libs/filepond-plugin-image-preview/filepond-plugin-image-preview.min.js"></script>
    <script src="public/assets/libs/filepond-plugin-file-validate-size/filepond-plugin-file-validate-size.min.js"></script>
    <script src="public/assets/libs/filepond-plugin-image-exif-orientation/filepond-plugin-image-exif-orientation.min.js"></script>
    <script src="public/assets/libs/filepond-plugin-file-encode/filepond-plugin-file-encode.min.js"></script>

    <!-- App js -->
    <script src="public/assets/js/app.js?t=1746205200"></script>
    <script>
      var tableDeposit = {
        table: "",
        tableDrawStatus: true,
        tooltipTriggerList: "",
        tooltipList: "",
      };
      document.addEventListener("DOMContentLoaded", function () {
        new DataTable(".alternative-pagination", {});
      });
      $(document).ready(function () {});

      function unshowAnnouncement(id) {
        setCookie("cookie_announcement", id, 7);
        $("#modalAnnouncement").modal("hide");
      }

      function setCookie(name, value, days) {
        var expires = "";
        if (days) {
          var date = new Date();
          date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
          expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + (value || "") + expires + "; path=/";
      }
      function getCookie(name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(";");
        for (var i = 0; i < ca.length; i++) {
          var c = ca[i];
          while (c.charAt(0) === " ") c = c.substring(1, c.length);
          if (c.indexOf(nameEQ) === 0)
            return c.substring(nameEQ.length, c.length);
        }
        return null;
      }

      $(document).on("click", "img.previewImage", function () {
        let src = $(this).attr("src");
        $("#imgPreviewImage").attr("src", src);

        $("#modelPreviewImage").modal("show");
      });

      function formatNumber(value, digit = 2) {
        var val = isNaN(value) ? 0 : value;
        var number = parseFloat(val).toFixed(digit).toLocaleString(undefined, {
          maximumFractionDigits: digit,
        });
        return number.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
      }
      function preview_image(event, obj) {
        var output = document.getElementById("show_" + obj.id);
        output.src = URL.createObjectURL(event.target.files[0]);
      }

      function copyButton(elm, copyText, afterTextButton) {
        copyToClipboard(copyText);
        $(elm).html(afterTextButton);
      }
      function copyToClipboard(text) {
        if (navigator.clipboard) {
          navigator.clipboard.writeText(text).then(
            function () {
              console.log("Text copied to clipboard");
              Swal.fire({
                position: "top-end",
                icon: "success",
                title: "Text copied to clipboard",
                showConfirmButton: false,
                timer: 1500,
              });
            },
            function (err) {
              console.error("Could not copy text: ", err);
            }
          );
        } else {
          let input = document.createElement("textarea");
          input.style.position = "fixed";
          input.style.zIndex = 9999;
          input.value = text;
          document.body.appendChild(input);
          input.select();
          input.focus();
          document.execCommand("copy");
          document.body.removeChild(input);
        }
      }
    </script>

    <script
      src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.2.0/crypto-js.min.js"
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    ></script>
    <script>
      var url_ajax = "/profile/";

      $(document).ready(function () {});

      function showSecretKey(elm) {
        let inputType = $("#secretKeyInput").attr("type");
        if (inputType == "password") {
          $("#secretKeyInput").attr("type", "text");
          $(elm).text("Hide");
        } else {
          $("#secretKeyInput").attr("type", "password");
          $(elm).text("Show");
        }
      }

      function confirmChangePassword() {
        let old_password = $("#old_password").val().trim();
        let new_password = $("#new_password").val().trim();
        let repeat_new_password = $("#repeat_new_password").val().trim();
        if (
          old_password == "" ||
          new_password == "" ||
          new_password != repeat_new_password
        ) {
          Swal.fire({
            icon: "warning",
            title:
              "New password not match or password was empty. Please type again.",
            confirmButtonText: "Close",
          }).then((result) => {});

          return false;
        }

        Swal.fire({
          title: "Confirm",
          text: "You want to change password login, right?",
          icon: "warning",
          showCancelButton: true,
          confirmButtonText: "Confirm",
          cancelButtonText: "Cancel",
        }).then((result) => {
          if (result.isConfirmed) {
            saveChangePassword();
          }
        });
      }

      function confirmReAPIKey() {
        Swal.fire({
          title: "Confirm",
          text: "You want to Regenerate Key, right?",
          icon: "warning",
          showCancelButton: true,
          confirmButtonText: "Confirm",
          cancelButtonText: "Cancel",
        }).then((result) => {
          if (result.isConfirmed) {
            handleGenerateKey();
          }
        });
      }

      function handleGenerateKey() {
        $.ajax({
          url: url_ajax + "handle_generate_key",
          type: "post",
          async: false,
          data: {},
          dataType: "json",
          success: function (res) {
            if (res.error == "0") {
              Swal.fire({
                icon: "success",
                title: "Regenerate Key success",
                confirmButtonText: "Close",
              }).then((result) => {
                window.location.reload();
              });
            } else {
              Swal.fire({
                icon: "warning",
                title: "Something error.",
                confirmButtonText: "Close",
              }).then((result) => {});
            }
          },
        });
      }

      function confirmChangeCallbackUrl() {
        let callback_url = $("#repeat_new_password").val().trim();

        Swal.fire({
          title: "Confirm",
          text: "You want to change callback url, right?",
          icon: "warning",
          showCancelButton: true,
          confirmButtonText: "Confirm",
          cancelButtonText: "Cancel",
        }).then((result) => {
          if (result.isConfirmed) {
            saveChangeCallbackUrl();
          }
        });
      }

      function saveChangePassword() {
        let old_password = $("#old_password").val().trim();
        let new_password = $("#new_password").val().trim();
        let repeat_new_password = $("#repeat_new_password").val().trim();
        if (new_password != repeat_new_password) {
          Swal.fire({
            icon: "warning",
            title: "New password not match. Please type again.",
            confirmButtonText: "Close",
          }).then((result) => {});
        } else {
          $.ajax({
            url: url_ajax + "change_password",
            type: "post",
            async: false,
            data: {
              old_password: old_password,
              new_password: new_password,
            },
            dataType: "json",
            success: function (res) {
              if (res.error == "0") {
                Swal.fire({
                  icon: "success",
                  title: "Change password success",
                  confirmButtonText: "Close",
                }).then((result) => {
                  window.location.reload();
                });
              } else {
                Swal.fire({
                  icon: "warning",
                  title: "Old password is incorrect.",
                  confirmButtonText: "Close",
                }).then((result) => {});
              }
            },
          });
        }
      }

      function saveChangeCallbackUrl() {
        let callback_url = $("#callback_url").val().trim();

        $.ajax({
          url: url_ajax + "change_callback_url",
          type: "post",
          async: false,
          data: {
            callback_url: callback_url,
          },
          dataType: "json",
          success: function (res) {
            if (res.error == "0") {
              Swal.fire({
                icon: "success",
                title: "Change callback url success",
                confirmButtonText: "Close",
              }).then((result) => {
                window.location.reload();
              });
            } else {
              Swal.fire({
                icon: "warning",
                title: "Something error.",
                confirmButtonText: "Close",
              }).then((result) => {});
            }
          },
        });
      }

      $(".number-only").on("input", function () {
        this.value = this.value.replace(/[^0-9.]/g, "");
      });

      function confirmSetUpPinCode() {
        let pin_code = $("#pin_code").val().trim();
        let repeat_pin_code = $("#repeat_pin_code").val().trim();
        if (
          pin_code.length != 6 ||
          repeat_pin_code.length != 6 ||
          repeat_pin_code != pin_code ||
          !isValidPin(pin_code)
        ) {
          Swal.fire({
            icon: "warning",
            title: `กรุณาระบุ Pin Code ให้ถูกต้อง`,
            confirmButtonText: "Close",
          });

          return false;
        }

        Swal.fire({
          title: "ยืนยันการตั้ง pin ",
          text: `คุณยืนยันในตั้ง Pin [${pin_code}] ใช่หรือไม่?`,
          icon: "warning",
          showCancelButton: true,
          confirmButtonText: "ยืนยัน",
          cancelButtonText: "ยกเลิก",
        }).then((result) => {
          if (result.isConfirmed) {
            $("#pin_code").attr("disabled", "disabled");
            $("#repeat_pin_code").attr("disabled", "disabled");

            $.ajax({
              url: url_ajax + "get_token_key",
              method: "GET",
              data: {},
              async: false,
              dataType: "json",
              success: function (response) {
                if (response.error == 0) {
                  $.ajax({
                    url: url_ajax + "setup_pin_code",
                    type: "post",
                    async: false,
                    data: {
                      encryptedPin: CryptoJS.AES.encrypt(
                        JSON.stringify(pin_code),
                        CryptoJS.MD5(response.result.token).toString(),
                        { format: CryptoJSAesJson }
                      ).toString(),
                      token: response.result.token,
                    },
                    dataType: "json",
                    success: function (res) {
                      console.log(res);
                      if (res.error == "0") {
                        Swal.fire({
                          icon: "success",
                          title: `ตั้งค่า Pin Code สำเร็จ`,
                          confirmButtonText: "ปิด",
                        }).then((result) => {
                          window.location.reload();
                        });
                      } else {
                        $("#pin_code").removeAttr("disabled");
                        $("#repeat_pin_code").removeAttr("disabled");

                        Swal.fire({
                          icon: "warning",
                          title:
                            "ไม่สามารถกำหนด Pin Code ได้ กรุณาลอง logout/login ใหม่อีกครั้งเพื่อทำรายการ",
                          confirmButtonText: "ปิด",
                        }).then((result) => {
                          window.location.reload();
                        });
                      }
                    },
                  });
                } else {
                  Swal.fire({
                    icon: "warning",
                    title: "เกิดข้อผิดพลาดไม่สามาระทำรายการได้",
                    confirmButtonText: "Close",
                  }).then((result) => {
                    window.location.reload();
                  });
                }
              },
            });

            return false;
          }
        });
      }

      function isValidPin(pin) {
        return /^\d+$/.test(pin);
      }
      var CryptoJSAesJson = {
        stringify: function (cipherParams) {
          var j = { ct: cipherParams.ciphertext.toString(CryptoJS.enc.Base64) };
          if (cipherParams.iv) j.iv = cipherParams.iv.toString();
          if (cipherParams.salt) j.s = cipherParams.salt.toString();
          return JSON.stringify(j);
        },
        parse: function (jsonStr) {
          var j = JSON.parse(jsonStr);
          var cipherParams = CryptoJS.lib.CipherParams.create({
            ciphertext: CryptoJS.enc.Base64.parse(j.ct),
          });
          if (j.iv) cipherParams.iv = CryptoJS.enc.Hex.parse(j.iv);
          if (j.s) cipherParams.salt = CryptoJS.enc.Hex.parse(j.s);
          return cipherParams;
        },
      };

      function confirmSaveAutoCancelWithdraw() {
        Swal.fire({
          title: "Confirm",
          text: "You want to Save auto cancel withdraw, right?",
          icon: "warning",
          showCancelButton: true,
          confirmButtonText: "Confirm",
          cancelButtonText: "Cancel",
        }).then((result) => {
          if (result.isConfirmed) {
            saveAutoCancelWithdraw();
          }
        });
      }

      function saveAutoCancelWithdraw() {
        let is_auto_cancel_txn_when_close = $(
          "[name=is_auto_cancel_txn_when_close]:checked"
        )
          .val()
          .trim();

        $.ajax({
          url: url_ajax + "save_auto_cancel_withdraw",
          type: "post",
          async: false,
          data: {
            is_auto_cancel_txn_when_close: is_auto_cancel_txn_when_close,
          },
          dataType: "json",
          success: function (res) {
            if (res.error == "0") {
              Swal.fire({
                icon: "success",
                title: "Save auto cancel withdraw success",
                confirmButtonText: "Close",
              }).then((result) => {
                window.location.reload();
              });
            } else {
              Swal.fire({
                icon: "warning",
                title: "Something error.",
                confirmButtonText: "Close",
              }).then((result) => {});
            }
          },
        });
      }

      function confirmSave2Fa() {
        Swal.fire({
          title: "Confirm",
          text: "Do you want to On/Off 2FA with Google Authenticator?",
          icon: "warning",
          showCancelButton: true,
          confirmButtonText: "Confirm",
          cancelButtonText: "Cancel",
        }).then((result) => {
          if (result.isConfirmed) {
            saveAutoCancelWithdraw();
          }
        });
      }

      function saveAutoCancelWithdraw() {
        let is_google2fa = $("[name=is_google2fa]:checked").val().trim();

        $.ajax({
          url: url_ajax + "save_config_2fa",
          type: "post",
          async: false,
          data: {
            is_google2fa: is_google2fa,
          },
          dataType: "json",
          success: function (res) {
            if (res.error == "0") {
              Swal.fire({
                icon: "success",
                title:
                  "After logging in, you will need to complete two-step authentication using Google Authenticator.",
                confirmButtonText: "Close",
              }).then((result) => {
                window.location.reload();
              });
            } else {
              Swal.fire({
                icon: "warning",
                title: "Something error.",
                confirmButtonText: "Close",
              }).then((result) => {});
            }
          },
        });
      }
    </script>
  </body>
</html>
