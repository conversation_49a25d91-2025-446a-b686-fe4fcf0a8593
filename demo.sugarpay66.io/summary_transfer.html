<!doctype html>
<html lang="en" data-layout="vertical" data-topbar="light" data-sidebar="dark" data-sidebar-size="lg" data-sidebar-image="none" data-preloader="disable">

<head>

    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="sugarpay66 payment">
    <meta name="keywords" content="sugarpay66 payment">
    <meta name="author" content="member.sugarpay66.io">
    <meta property="og:title" content="Merchant sugarpay66 payment" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="/" />
    <meta property="og:image" content="/public/assets//images/logo/android-chrome-512x512.png" />
    <title>ร้าน tiger-001 - Merchant sugarpay66 payment</title>


    <style>
        :root {
            --theme-bg_color_login: linear-gradient(to right, #bfa2a8, #6e5c60);
            --theme-color_primary:#6e5c60;
        }
    </style>
    
    <!-- For IE6+ -->
    <link rel="shortcut icon" href="public/assets/images/logo/favicon.ico" type="image/x-icon">

    <!-- For all other browsers -->
    <link rel="icon" href="public/assets/images/logo/favicon.ico"/>

    <!-- Different sizes -->
    <link rel="icon" href="public/assets/images/logo/favicon-16x16.png" sizes="16x16">
    <link rel="icon" href="public/assets/images/logo/favicon-32x32.png" sizes="32x32">

    <!-- For Modern Browsers with PNG Support -->
    <link rel="icon" type="image/png" href="public/assets/images/logo/apple-touch-icon.png">

    <!-- Works in Firefox, Opera, Chrome and Safari -->
    <link rel="icon" href="public/assets/images/logo/apple-touch-icon.png">

    <!-- For rounded corners and reflective shine in Apple devices -->
    <link rel="apple-touch-icon" href="public/assets/images/logo/apple-touch-icon.png" />

    <!-- Favicon without reflective shine -->
    <link rel="apple-touch-icon-precomposed" href="public/assets/images/logo/apple-touch-icon.png">

    <!-- jsvectormap css -->
    <link href="public/assets/libs/jsvectormap/css/jsvectormap.min.css" rel="stylesheet" type="text/css" />

    <!--Swiper slider css-->
    <link href="public/assets/libs/swiper/swiper-bundle.min.css" rel="stylesheet" type="text/css" />


    <!-- Sweet Alert css-->
    <link href="public/assets/libs/sweetalert2/sweetalert2.min.css" rel="stylesheet" type="text/css" />

    <link href="public/assets/libs/flatpickr/flatpickr.min.css" rel="stylesheet" type="text/css" />

    <!-- Layout config Js -->
    <script src="public/assets/js/layout.js"></script>
    <!-- Bootstrap Css -->
    <link href="public/assets/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <!-- Icons Css -->
    <link href="public/assets/css/icons.min.css" rel="stylesheet" type="text/css" />
    <!-- App Css-->
    <link href="public/assets/css/app.css?t=1746271725" rel="stylesheet" type="text/css" />

    <!-- custom Css-->
    <link href="public/assets/css/custom.css?t=1746271725" rel="stylesheet" type="text/css" />


    <!--datatable css-->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" />
    <!--datatable responsive css-->
    <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.dataTables.min.css">
    <style type="text/css">
        .text-bold{
            font-weight: bold;
        }
        .text-right{
            text-align: right;
        }
        .modal-xl {
            max-width: 1300px !important;
        }
        #modalAnnouncement img{
            width :100%;
        }
        #modalAnnouncement  p {
            margin-top: 0;
            margin-bottom: 0.2rem;
        }
    </style>
    <style></style></head>

<body>

<!-- Begin page -->
<div id="layout-wrapper">

    <header id="page-topbar">
        <div class="layout-width">
            <div class="navbar-header">
                <div class="d-flex">
                    <!-- LOGO -->
                    <div class="navbar-brand-box horizontal-logo">
                        <a href="" class="logo logo-dark">
                                <span class="logo-sm">
                                    <img src="public/assets/images/logo/logo-sm.png" alt="" height="22">
                                </span>
                            <span class="logo-lg">
                                    <img src="public/assets/images/logo/logo.png" alt="" height="48">
                                </span>
                        </a>

                        <a href="index.html" class="logo logo-light">
                                <span class="logo-sm">
                                    <img src="public/assets/images/logo/logo-sm.png" alt="" height="22">
                                </span>
                            <span class="logo-lg">
                                    <img src="public/assets/images/logo/logo.png" alt="" height="48">
                                </span>
                        </a>
                    </div>

                    <button type="button" class="btn btn-sm px-3 fs-16 header-item vertical-menu-btn topnav-hamburger"
                            id="topnav-hamburger-icon">
                            <span class="hamburger-icon">
                                <span></span>
                                <span></span>
                                <span></span>
                            </span>
                    </button>
                </div>

                <div class="d-flex align-items-center">
                                        <div class="ms-1 header-item d-none d-sm-flex">
                        <button type="button" class="btn btn-icon btn-topbar btn-ghost-secondary rounded-circle"
                                data-toggle="fullscreen">
                            <i class='bx bx-fullscreen fs-22'></i>
                        </button>
                    </div>
                    <div class="dropdown ms-sm-3 header-item topbar-user">
                        <button type="button" class="btn" id="page-header-user-dropdown" data-bs-toggle="dropdown"
                                aria-haspopup="true" aria-expanded="false">
                                <span class="d-flex align-items-center">
                                    <img class="rounded-circle header-profile-user" src="public/assets/images/logo/logo-sm.png"
                                         alt="Header Avatar">
                                    <span class="text-start ms-xl-2">
                                        <span class="d-none d-xl-inline-block ms-1 fw-medium user-name-text">tiger-001</span>
                                        <span class="d-none d-xl-block ms-1 fs-12 text-muted user-name-sub-text"></span>
                                    </span>
                                </span>
                        </button>
                        <div class="dropdown-menu dropdown-menu-end">
                            <!-- item-->
                            <h6 class="dropdown-header">ยินดีต้อนรับ tiger-001</h6>
                                                        <a class="dropdown-item" href="login/logout"><i
                                        class="mdi mdi-logout text-muted fs-16 align-middle me-1"></i> <span
                                        class="align-middle" data-key="t-logout">ออกจากระบบ</span></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- ========== App Menu ========== -->
    <div class="app-menu navbar-menu">
        <!-- LOGO -->
        <div class="navbar-brand-box">
            <!-- Dark Logo-->
            <a href="" class="logo logo-dark">
                    <span class="logo-sm">
                        <img src="public/assets/images/logo/logo-sm.png" alt="" height="22">
                    </span>
                <span class="logo-lg">
                        <img src="public/assets/images/logo/logo.png" alt="" height="48">
                    </span>
            </a>
            <!-- Light Logo-->
            <a href="" class="logo logo-light">
                    <span class="logo-sm">
                        <img src="public/assets/images/logo/logo-sm.png" alt="" height="22">
                    </span>
                <span class="logo-lg">
                        <img src="public/assets/images/logo/logo.png" alt="" height="48">
                    </span>
            </a>
            <button type="button" class="btn btn-sm p-0 fs-20 header-item float-end btn-vertical-sm-hover" id="vertical-hover">
                <i class="ri-record-circle-line"></i>
            </button>
        </div>

        <div id="scrollbar">
            <div class="container-fluid">

                <div id="two-column-menu">
                </div>
                <ul class="navbar-nav mt-3" id="navbar-nav">
                                            <li class="nav-item">
                            <a class="nav-link menu-link " href="dashboard.html" >
                                <i class="ri-article-line"></i> <span data-key="t-users">Dashboard</span>
                            </a>
                        </li>
                                        
                                        <li class="nav-item">
                        <a class="nav-link menu-link " href="transaction.html" >
                            <i class="ri-article-line"></i> <span data-key="t-users">Transaction</span>
                        </a>
                    </li>
                                                                <li class="nav-item">
                            <a class="nav-link menu-link " href="withdraw_approve.html" >
                                <i class="ri-article-line"></i> <span data-key="t-users">Withdraw Approve</span>
                            </a>
                        </li>
                                                            <li class="nav-item">
                        <a class="nav-link menu-link " href="bank_statement.html" >
                            <i class="ri-bank-line"></i> <span data-key="t-users">Statement</span>
                        </a>
                    </li>
                                                            <li class="nav-item">
                        <a class="nav-link menu-link " href="summary_transfer.html" >
                            <i class="ri-file-transfer-line"></i> <span data-key="t-users">Settlement</span>
                        </a>
                    </li>
                    
                                        <a class="nav-link menu-link " href="#sidebarReport" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarReport">
                        <i class="ri-article-line"></i> <span data-key="t-report">Report</span>
                    </a>
                    <div class="collapse menu-dropdown" id="sidebarReport">
                        <ul class="nav nav-sm flex-column">

                                                        <li class="nav-item">
                                <a href="report_agent_daily_realtime.html" class="nav-link" data-key="t-report">Daily Summary Report</a>
                            </li>
                             

                                                            <li class="nav-item">
                                    <a href="report_withdraw_channel.html" class="nav-link " data-key="t-users">Withdraw Channel Report</a>
                                </li>
                            
                                                        <li class="nav-item">
                                <a href="report_withdraw.html" class="nav-link" data-key="t-report">Withdraw Activity Report</a>
                            </li>
                             

                                                        <li class="nav-item">
                                <a href="report_deposit.html" class="nav-link" data-key="t-report">Deposit Activity Report</a>
                            </li>
                             
                            
                            <!--                            <li class="nav-item">-->
<!--                                <a href="--><!--" class="nav-link" data-key="t-report"> Withdraw Slip</a>-->
<!--                            </li>-->
                                                    </ul>
                    </div>
                    
                  

                                        <li class="nav-item">
                        <a class="nav-link menu-link " href="withdraw_fund.html" >
                            <i class="ri-file-transfer-line"></i> <span data-key="t-users">Withdraw Fund</span>
                        </a>
                    </li>
                                                            <li class="nav-item">
                        <a class="nav-link menu-link " href="scanslip-step-import.html" >
                            <i class=" ri-qr-code-fill"></i> <span data-key="t-users">STEP 1 Import Slip</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link menu-link " href="scanslip-step-verify.html" >
                            <i class=" ri-qr-code-fill"></i> <span data-key="t-users">STEP 2 Verify Slip</span>
                        </a>
                    </li>
                    

                                        <a class="nav-link menu-link " href="#sidebarTools" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarTools">
                        <i class="ri-article-line"></i> <span data-key="t-report">Tools</span>
                    </a>
                    <div class="collapse menu-dropdown" id="sidebarTools">
                        <ul class="nav nav-sm flex-column">
                             
                                                        <li class="nav-item">
                                <a href="black_list.html" class="nav-link" data-key="t-report">Blacklist Manage</a>
                            </li>
                             
 
                        </ul>
                    </div>
                    

                    <li class="nav-item">
                        <a class="nav-link menu-link " href="profile.html" >
                            <i class="ri-folder-user-line"></i> <span data-key="t-users">Profile</span>
                        </a>
                    </li>
                                            <a class="nav-link menu-link " href="#sidebarSetupUser" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarSetupUser">
                            <i class="ri-folder-user-line"></i> <span data-key="t-setup-user">Settings</span>
                        </a>
                                                <div class="collapse menu-dropdown " id="sidebarSetupUser">
                            <ul class="nav nav-sm flex-column">
                                <li class="nav-item">
                                    <a href="user_group.html" class="nav-link " data-key="t-user-group"> User Groups </a>
                                </li>
                                <li class="nav-item">
                                    <a href="user_permission.html" class="nav-link " data-key="t-permission"> Group Permission </a>
                                </li>
                                <li class="nav-item">
                                    <a href="user.html" class="nav-link " data-key="t-users"> Sub Users </a>
                                </li>
                            </ul>
                        </div>
                                        <li class="nav-item">
                        <a class="nav-link menu-link " href="login/logout" >
                            <i class="ri-logout-box-r-line"></i> <span data-key="t-users">Logout</span>
                        </a>
                    </li>
<!--                    <li class="nav-item">-->
<!--                        <a class="nav-link menu-link " href="https://vizpay.supportnow.me/" target="_blank">-->
<!--                            <img src="--><!--images/ticket_now.jpg" alt="" height="60">-->
<!--                        </a>-->
<!--                    </li>-->
                </ul>
            </div>
            <!-- Sidebar -->
        </div>

        <div class="sidebar-background"></div>
    </div>
    <!-- Left Sidebar End -->
    <!-- Vertical Overlay-->
    <div class="vertical-overlay"></div>

    <!-- ============================================================== -->
    <!-- Start right Content here -->
    <!-- ============================================================== -->
    <div class="main-content">

<div class="page-content">
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                    <h4 class="mb-sm-0">Settlement</h4>

                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item active">Settlement</li>
                        </ol>
                    </div>

                </div>
            </div>
        </div>
        <!-- end page title -->


        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header align-items-center d-flex">
                        <h4 class="card-title mb-0 flex-grow-1">Filter</h4>
                        <div class="flex-shrink-0">
                        </div>
                    </div><!-- end card header -->
                    <div class="card-body">
                        <div class="live-preview">
                            <form action="#" id="form_search" method="post">
                                <div>
                                    <div class="row g-3">
                                        <div class="col-lg-3">
                                            <label for="field1" class="form-label">Settlement Start Date</label>
                                            <input type="text" class="form-control" id="start_date" value="2025-04-26 00:00" >
                                        </div>
                                        <div class="col-lg-3">
                                            <label for="field1" class="form-label">Settlement End Date</label>
                                            <input type="text" class="form-control" id="end_date" value="2025-05-03 23:59">
                                        </div>
                                        <div class="col-lg-3">
                                            <label for="field1" class="form-label">Status</label>
                                            <select class="form-select"  name="status_search" id="status_search" required>
                                                <option value="ALL" selected>All</option>
                                                <option value="WAITING" >WAITING</option>
                                                <option value="APPROVED">APPROVED</option>
                                                <option value="TRANSFERRED">TRANSFERRED</option>
                                                <option value="FAILED">FAILED</option>
                                                <option value="CANCELLED">CANCELLED</option>
                                            </select>
                                        </div>
                                        <div class="col-lg-3">
                                            <label for="field1" class="form-label">Text in table</label>
                                            <input type="text" class="form-control" id="text_search" value="">
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <div class="row g-3 mt-3">
                                        <div class="col-12 text-center">
                                            <button type="button" class="btn btn-success  waves-effect waves-light" onclick="get_datatable_list();"><i class="mdi mdi-filter-plus-outline"></i> Search</button>
                                            <button type="reset" class="btn btn-soft-dark waves-effect waves-light" onclick="setTimeout(get_datatable_list,300)"><i class="mdi mdi-filter-remove-outline"></i> Clear</button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header align-items-center d-flex">
                        <h4 class="card-title mb-0 flex-grow-1">Result</h4>
                        <div class="flex-shrink-0">
                        </div>
                    </div><!-- end card header -->
                    <div class="card-body">
                        <!-- Datatable -->
                        <div class="table-responsive">
                            <table id="datatable-list" class="table nowrap dt-responsive align-middle table-hover table-bordered" style="width:100%">
                                <thead>
                                <tr>
                                    <th scope="col">Settlement Date</th>
                                    <th scope="col">Settlement No</th>
                                    <th scope="col">Pull Money Back</th>
                                    <th scope="col">Transfer Amount</th>
                                    <th scope="col">To Merchant Bank</th>
                                    <th scope="col">Status</th>
                                    <th scope="col">Transferred Date</th>
                                    <th scope="col">Download CSV</th>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!--end col-->
        </div>
        <!--end row-->

    </div>
    <!-- container-fluid -->
</div>
<!-- End Page-content -->


<div id="modalTransAdjs" class="modal fade" tabindex="-1" aria-labelledby="modalTransAdjsLabel" aria-hidden="true" style="display: none;">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTransAdjsLabel">List  Pull Money Back</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"> </button>
            </div>
            <div class="modal-body">

            <div class="table-responsive">

                <table id="datatable-list-tran-adj" class="table nowrap dt-responsive align-middle table-hover table-bordered" style="width:100%">
                    <thead>
                    <tr>
                        <th scope="col">Created Date</th>
                        <th scope="col">Txn Amount</th>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
               
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>
<footer class="footer">
    <div class="container-fluid">
        <div class="row">
            <div class="col-sm-6">
                Copyright <script>document.write(new Date().getFullYear())</script> © member.sugarpay66.io            </div>
            <div class="col-sm-6">
                
            </div>
        </div>
    </div>
</footer>
</div>
<!-- end main content-->

</div>
<!-- END layout-wrapper -->

<!--start back-to-top-->
<button onclick="topFunction()" class="btn btn-primary btn-icon" id="back-to-top">
    <i class="ri-arrow-up-line"></i>
</button>
<!--end back-to-top-->

<!--preloader-->
<div id="preloader">
    <div id="status">
        <div class="spinner-border text-primary avatar-sm" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
</div>

<div class="modal fade" id="modelPreviewImage" tabindex="-1" aria-labelledby="modelPreviewImage" aria-modal="true">
    <div class="modal-dialog ">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalgridLabel">Preview Image</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <img src="" id="imgPreviewImage" style="width: 100%;" alt="">
            </div>
        </div>
    </div>
</div>



<!-- JAVASCRIPT -->
<script src="public/assets/libs/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="public/assets/libs/simplebar/simplebar.min.js"></script>
<script src="public/assets/libs/node-waves/waves.min.js"></script>
<script src="public/assets/libs/feather-icons/feather.min.js"></script>
<script src="public/assets/js/pages/plugins/lord-icon-2.1.0.js"></script>
<script src="public/assets/js/plugins.js?date=202403042044"></script>

<script src="public/assets/js/pages/notifications.init.js"></script>

<script src="https://code.jquery.com/jquery-3.6.0.min.js" integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>

<!--datatable js-->
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<!-- <script src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script> -->
<script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>

<!-- Sweet Alerts js -->
<script src="public/assets/libs/sweetalert2/sweetalert2.min.js"></script>

<script src="public/assets/libs/flatpickr/flatpickr.min.js"></script>


<!-- Sweet alert init js-->
<script src="public/assets/js/pages/sweetalerts.init.js"></script>

<!-- Moment js -->
<script src="public/assets/libs/moment/moment.js"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.blockUI/2.70/jquery.blockUI.min.js" integrity="sha512-eYSzo+20ajZMRsjxB6L7eyqo5kuXuS2+wEbbOkpaur+sA2shQameiJiWEzCIDwJqaB0a4a6tCuEvCOBHUg3Skg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.blockUI/2.70/jquery.blockUI.js" integrity="sha512-QSb5le+VXUEVEQbfljCv8vPnfSbVoBF/iE+c6MqDDqvmzqnr4KL04qdQMCm0fJvC3gCWMpoYhmvKBFqm1Z4c9A==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<!-- <script src="public/assets/js/pages/datatables.init.js"></script> -->

<!-- dropzone min -->
<script src="public/assets/libs/dropzone/dropzone-min.js"></script>
<!-- filepond js -->
<script src="public/assets/libs/filepond/filepond.min.js"></script>
<script src="public/assets/libs/filepond-plugin-image-preview/filepond-plugin-image-preview.min.js"></script>
<script src="public/assets/libs/filepond-plugin-file-validate-size/filepond-plugin-file-validate-size.min.js"></script>
<script src="public/assets/libs/filepond-plugin-image-exif-orientation/filepond-plugin-image-exif-orientation.min.js"></script>
<script src="public/assets/libs/filepond-plugin-file-encode/filepond-plugin-file-encode.min.js"></script>

<!-- App js -->
<script src="public/assets/js/app.js?t=1746205200"></script>
<script>

 
    var tableDeposit =  { table : '',tableDrawStatus :true, tooltipTriggerList : '',tooltipList : ''};
    document.addEventListener("DOMContentLoaded",function(){
        new DataTable(".alternative-pagination",{
        })
        
    })
    $(document).ready(function () {
                        
                
    });

    function unshowAnnouncement(id){
        setCookie("cookie_announcement", id, 7); 
        $("#modalAnnouncement").modal('hide')
    }

    function setCookie(name, value, days) {
        var expires = "";
        if (days) {
            var date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + (value || "") + expires + "; path=/";
    }
    function getCookie(name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(';');
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    }

    $(document).on('click','img.previewImage',function(){
        let src = $(this).attr('src');
        $("#imgPreviewImage").attr('src', src);

        $("#modelPreviewImage").modal("show");
    });

    function formatNumber(value,digit = 2){
        var val = isNaN(value) ? 0 : value;
        var number = parseFloat(val).toFixed(digit).toLocaleString(undefined, {
            maximumFractionDigits: digit
        });
        return number.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
    function preview_image(event, obj) {
        var output = document.getElementById('show_' + obj.id);
        output.src = URL.createObjectURL(event.target.files[0]);
    }

   
    function copyButton(elm, copyText, afterTextButton){
        copyToClipboard(copyText);
        $(elm).html(afterTextButton);
    }
    function copyToClipboard(text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(
                function () { 
                    console.log('Text copied to clipboard'); 
                    Swal.fire({
                        position: 'top-end',
                        icon: 'success',
                        title: 'Text copied to clipboard',
                        showConfirmButton: false,
                        timer: 1500,
                        
                    })
                },
                function (err) { console.error('Could not copy text: ', err); }
            );
        } else {
            let input = document.createElement('textarea');
            input.style.position = 'fixed';
            input.style.zIndex = 9999;
            input.value = text;
            document.body.appendChild(input);
            input.select();
            input.focus();
            document.execCommand('copy');
            document.body.removeChild(input);
        }

    }


</script>

<!-- use the latest version -->
<script>
    var url_ajax = '/summary_transfer/';
    let start_date = moment().add(-7,"day").format('YYYY-MM-DD 00:00').toString();
    let end_date = moment().format('YYYY-MM-DD 23:59').toString();
    let table =  '';
    let tableDrawStatus =  true;
    let tooltipTriggerList ='';
    let tooltipList ='';
    $(document).ready(function(){
        $("#start_date").flatpickr({
            enableTime: true,
            dateFormat: "Y-m-d H:i",
            time_24hr: true,
            defaultDate: [start_date],
            onChange: function(selectedDates, dateStr, instance) {
                start_date = dateStr;
            },
        });
        $("#end_date").flatpickr({
            enableTime: true,
            dateFormat: "Y-m-d H:i",
            time_24hr: true,
            defaultDate: [end_date],
            onChange: function(selectedDates, dateStr, instance) {
                end_date = dateStr;
            },
        });
        get_datatable_list();
    });

    function get_datatable_list(){

        var text_search = $("#text_search").val().trim();
        var status_search = $("#status_search").val().trim();
        $.fn.dataTable.ext.errMode = 'none';
        table = $('#datatable-list').DataTable({
            // data: response.result,
            destroy: true,
            processing: true,
            serverSide: true,
            searchDelay: 500,
            ajax : {
                url : url_ajax + 'getDatatableList',
                type : "POST",
                data : {
                    text_search: text_search,
                    status_search: status_search,
                    start_date: start_date+":00",
                    end_date: end_date+":59"
                },
            },
            columnDefs: [
                {
                    targets: [3],
                    className: 'text-right'
                }
                ,{
                    targets: [7,2],
                    className: 'text-center'
                }
            ],
            columns : [
                {data: function(data, type, dataToSet) { return moment(data.settled_date).format("YYYY-MM-DD HH:mm"); }},
                {data: function(data, type, dataToSet) {
                        let admin_remark = ``;
                        if(data.admin_remark != null && data.admin_remark != ''){
                            admin_remark = `(${data.admin_remark}) `
                        }
                    return data.settled_no+' '+admin_remark;
                }},
                {data: function(data, type, dataToSet) { 
                    if(data.adjust_amount > 0){
                        return `<button type="button" onClick="modalTransAdjust('${data.settled_no}')" class="btn btn-info btn-sm">${formatNumber(data.adjust_amount,2)}</button>`
                    }else{
                        return `-`
                    }
                }},
                {data: function(data, type, dataToSet) { return formatNumber(data.net_amount,2); }},
                {data: function(data, type, dataToSet) { return data.to_bank_name+' '+data.to_acc_no; }},
                {data: function(data, type, dataToSet) {
                        if(data.status == 'APPROVED'){
                            return `<span class="badge text-bg-success">${data.status}</span>`;
                        }else if(data.status == 'CANCELLED'){
                            return `<span class="badge text-bg-danger">${data.status}</span>`;
                        }else if(data.status == 'TRANSFERRED'){
                            return `<span class="badge text-bg-primary">${data.status}</span>`;
                        }else if(data.status == 'SUCCESS'){
                            return `<span class="badge text-bg-primary">${data.status}</span>`;
                        }else{
                            return `<span class="badge text-bg-warning">${data.status}</span>`;
                        }
                    }},
                {data: function(data, type, dataToSet) {
                    if(data.transfer_date != null){
                        return moment(data.transfer_date).format("YYYY-MM-DD HH:mm");
                    }else{
                        return '';
                    }
                }},
                {data: function(data, type, dataToSet) {
                    return `<button type="button" onclick="exportCsv('${data.settled_no}');" class="btn btn-sm btn-warning"><i class="mdi mdi-file-download-outline"></i></button>`;
                }},

            ],
            order: [[0, 'desc']],
        });

        tableDraw();
    }

    function tableDraw(){
        if(tableDrawStatus){
            table.on( 'draw', function () {
                tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]')
                tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl))
            } );    
            tableDrawStatus = false;
        }
        
    }


    function exportCsv(settled_no){
        Swal.fire({
            title: 'Loading data for export csv',
            allowOutsideClick: false,
            showConfirmButton: false,
            onBeforeOpen: () => {
                Swal.showLoading()
            },
        });
        Swal.showLoading();

        setTimeout(function(){

            let check = true;
            let page = 1;
            let length = 3000;
            let result_csv = [];
            while(check){
                $.ajax({
                    url: url_ajax + 'getSettlementDetailList',
                    method: 'POST',
                    data: {
                        settled_no: settled_no,
                        page: page,
                        length: length,
                    },
                    async: false,
                    dataType: "json",
                    success: function (response) {
                        // console.log(response);
                        if(response.error == 0){
                            // console.log("OK");
                            if(response.recordsTotal > 0){
                                let output = [];
                                $.each(response.result,function(index,value){
                                    output.push({
                                        'Create Date': `${value.created_date}`,
                                        'Order Id': `${(value.order_id!=null)?value.order_id:''}`,
                                        'Type': `${value.transaction_type}`,
                                        'Amount': `${(value.amount!=null)?value.amount:0}`,
                                        'MDR': `${(value.mdr_amount!=null)?value.mdr_amount:0}`,
                                        'Net': `${(value.net_amount!=null)?value.net_amount:0}`,
                                        'Status': `${value.status}`,
                                        'Transaction Date': `${value.updated_date}`,
                                        'Bank': `${(value.bank_name!=null)?value.bank_name:''}`,
                                        'Account No': `${(value.bank_acc_no!=null)?value.bank_acc_no:''}`,
                                        'Cust Name': `${(value.bank_acc_name!=null)?value.bank_acc_name:''}`,
                                    });
                                })
                                result_csv = result_csv.concat(output);
                                // console.log("step 1");
                                check = true;
                            }else{
                                // console.log("step 2");
                                check = false;
                            }
                        }else{
                            // console.log("step 3");
                            check = false;
                        }
                    }
                })
                if(page >50){
                    check = false;
                }
                page++;
            }
            if(result_csv.length > 0){
                export_csv(result_csv,`settlement_${settled_no}_`+moment().format("YYYY-MM-DD_HH-mm-ss").toString())
            }else{
                alert('ไม่พบข้อมูล');
            }
            Swal.close()
        },300)

        return false;
    }

    function export_csv(data,filename){
        let csvContent = "data:text/csv;charset=utf-8,";
        csvContent += "\uFEFF"+CSV(data);

        var encodedUri = encodeURI(csvContent);
        var link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", filename+".csv");
        document.body.appendChild(link); // Required for FF
        link.click();
    }
    function CSV(array) {
        // Use first element to choose the keys and the order
        var keys = Object.keys(array[0]);

        // Build header
        var result = keys.join(",") + "\n";

        // Add the rows
        array.forEach(function(obj){
            result += '"'+keys.map(k => obj[k]).join('","').replaceAll("#","")+'"' + "\n";
        });

        return result;
    }


    function modalTransAdjust(settled_no){
        $("#modalTransAdjs").modal('show')
        setTimeout(function(){
            get_datatable_list_tran_adj(settled_no)
        },250)
    }

    function get_datatable_list_tran_adj(settled_no){
        $.fn.dataTable.ext.errMode = 'none';
        table = $('#datatable-list-tran-adj').DataTable({
            destroy: true,
            processing: true,
            serverSide: true,
            searchDelay: 500,
            ajax : {
                url : url_ajax + 'getDatatableListAdj',
                type : "POST",
                data : {
                    settled_no : settled_no
                },
            },
            columnDefs: [],
            columns : [
                {data: function(data, type, dataToSet) { return data.net_amount; }},
                {data: function(data, type, dataToSet) { return moment(data.created_date).format("YYYY-MM-DD HH:mm"); }},
            ],
            order: [[0, 'desc']],
        });

        tableDrawTableAdj();
    }

    function tableDrawTableAdj(){
        if(tableDrawStatus){
            table.on( 'draw', function () {
                tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]')
                tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl))
            } );    
            tableDrawStatus = false;
        }
    }

</script>
</body>

</html>

