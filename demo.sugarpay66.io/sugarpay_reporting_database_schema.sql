-- =====================================================
-- SugarPay Reporting Database Schema
-- ระบบฐานข้อมูลสำหรับระบบรายงาน
-- =====================================================

-- ตาราง daily_reports - รายงานรายวัน
CREATE TABLE daily_reports (
    report_id INT PRIMARY KEY AUTO_INCREMENT,
    merchant_id INT NOT NULL COMMENT 'รหัสร้านค้า',
    
    -- ข้อมูลรายงาน
    report_date DATE NOT NULL COMMENT 'วันที่รายงาน',
    report_type ENUM('TRANSACTION', 'REVENUE', 'BALANCE', 'SUMMARY') DEFAULT 'SUMMARY' COMMENT 'ประเภทรายงาน',
    
    -- ข้อมูลธุรกรรม
    total_transactions INT DEFAULT 0 COMMENT 'จำนวนธุรกรรมทั้งหมด',
    total_deposit_transactions INT DEFAULT 0 COMMENT 'จำนวนธุรกรรมฝาก',
    total_withdraw_transactions INT DEFAULT 0 COMMENT 'จำนวนธุรกรรมถอน',
    total_topup_transactions INT DEFAULT 0 COMMENT 'จำนวนธุรกรรม TopUp',
    total_transfer_transactions INT DEFAULT 0 COMMENT 'จำนวนธุรกรรมโอน',
    
    -- ยอดเงินธุรกรรม
    total_deposit_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดเงินฝากรวม',
    total_withdraw_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดเงินถอนรวม',
    total_topup_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดเงิน TopUp รวม',
    total_transfer_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดเงินโอนรวม',
    
    -- ค่าธรรมเนียม
    total_deposit_fees DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ค่าธรรมเนียมฝากรวม',
    total_withdraw_fees DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ค่าธรรมเนียมถอนรวม',
    total_topup_fees DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ค่าธรรมเนียม TopUp รวม',
    total_settlement_fees DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ค่าธรรมเนียม Settlement รวม',
    total_fees DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ค่าธรรมเนียมรวมทั้งหมด',
    
    -- ยอดคงเหลือ
    opening_deposit_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดฝากยกมา',
    closing_deposit_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดฝากยกไป',
    opening_withdraw_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดถอนยกมา',
    closing_withdraw_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดถอนยกไป',
    opening_frozen_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดระงับยกมา',
    closing_frozen_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดระงับยกไป',
    
    -- สถานะ
    status ENUM('GENERATING', 'COMPLETED', 'ERROR') DEFAULT 'GENERATING' COMMENT 'สถานะการสร้างรายงาน',
    generated_by INT COMMENT 'ผู้สร้างรายงาน',
    
    -- ข้อมูลเพิ่มเติม
    additional_data JSON COMMENT 'ข้อมูลเพิ่มเติม',
    notes TEXT COMMENT 'หมายเหตุ',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่อัปเดต',
    
    FOREIGN KEY (merchant_id) REFERENCES merchants(merchant_id) ON DELETE CASCADE,
    FOREIGN KEY (generated_by) REFERENCES users(user_id) ON DELETE SET NULL,
    UNIQUE KEY unique_merchant_date_type (merchant_id, report_date, report_type),
    INDEX idx_merchant_date (merchant_id, report_date),
    INDEX idx_report_date (report_date),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางรายงานรายวัน';

-- ตาราง monthly_reports - รายงานรายเดือน
CREATE TABLE monthly_reports (
    report_id INT PRIMARY KEY AUTO_INCREMENT,
    merchant_id INT NOT NULL COMMENT 'รหัสร้านค้า',
    
    -- ข้อมูลรายงาน
    report_year INT NOT NULL COMMENT 'ปี',
    report_month INT NOT NULL COMMENT 'เดือน',
    report_type ENUM('TRANSACTION', 'REVENUE', 'BALANCE', 'SUMMARY') DEFAULT 'SUMMARY' COMMENT 'ประเภทรายงาน',
    
    -- สรุปธุรกรรมรายเดือน
    total_transactions INT DEFAULT 0 COMMENT 'จำนวนธุรกรรมทั้งหมด',
    total_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดเงินรวมทั้งหมด',
    total_fees DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ค่าธรรมเนียมรวมทั้งหมด',
    net_revenue DECIMAL(15,2) DEFAULT 0.00 COMMENT 'รายได้สุทธิ',
    
    -- ข้อมูลรายวัน (JSON)
    daily_breakdown JSON COMMENT 'ข้อมูลแยกรายวัน',
    
    -- สถานะ
    status ENUM('GENERATING', 'COMPLETED', 'ERROR') DEFAULT 'GENERATING' COMMENT 'สถานะการสร้างรายงาน',
    generated_by INT COMMENT 'ผู้สร้างรายงาน',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่อัปเดต',
    
    FOREIGN KEY (merchant_id) REFERENCES merchants(merchant_id) ON DELETE CASCADE,
    FOREIGN KEY (generated_by) REFERENCES users(user_id) ON DELETE SET NULL,
    UNIQUE KEY unique_merchant_year_month_type (merchant_id, report_year, report_month, report_type),
    INDEX idx_merchant_year_month (merchant_id, report_year, report_month),
    INDEX idx_year_month (report_year, report_month),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางรายงานรายเดือน';

-- ตาราง revenue_reports - รายงานรายได้จากค่าธรรมเนียม
CREATE TABLE revenue_reports (
    revenue_report_id INT PRIMARY KEY AUTO_INCREMENT,
    merchant_id INT COMMENT 'รหัสร้านค้า (NULL = รายได้ระบบทั้งหมด)',
    
    -- ช่วงเวลารายงาน
    report_period_type ENUM('DAILY', 'WEEKLY', 'MONTHLY', 'QUARTERLY', 'YEARLY') NOT NULL COMMENT 'ประเภทช่วงเวลา',
    start_date DATE NOT NULL COMMENT 'วันที่เริ่มต้น',
    end_date DATE NOT NULL COMMENT 'วันที่สิ้นสุด',
    
    -- รายได้จากค่าธรรมเนียมแต่ละประเภท
    mdr_deposit_revenue DECIMAL(15,2) DEFAULT 0.00 COMMENT 'รายได้จาก MDR ฝาก',
    fee_withdraw_revenue DECIMAL(15,2) DEFAULT 0.00 COMMENT 'รายได้จากค่าธรรมเนียมถอน',
    fee_topup_revenue DECIMAL(15,2) DEFAULT 0.00 COMMENT 'รายได้จากค่าธรรมเนียม TopUp',
    fee_settlement_revenue DECIMAL(15,2) DEFAULT 0.00 COMMENT 'รายได้จากค่าธรรมเนียม Settlement',
    fee_transfer_revenue DECIMAL(15,2) DEFAULT 0.00 COMMENT 'รายได้จากค่าธรรมเนียมโอน',
    other_revenue DECIMAL(15,2) DEFAULT 0.00 COMMENT 'รายได้อื่นๆ',
    
    -- รวมรายได้
    gross_revenue DECIMAL(15,2) DEFAULT 0.00 COMMENT 'รายได้รวม',
    tax_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ภาษี',
    net_revenue DECIMAL(15,2) DEFAULT 0.00 COMMENT 'รายได้สุทธิ',
    
    -- จำนวนธุรกรรม
    total_transactions INT DEFAULT 0 COMMENT 'จำนวนธุรกรรมทั้งหมด',
    revenue_transactions INT DEFAULT 0 COMMENT 'จำนวนธุรกรรมที่มีรายได้',
    
    -- ข้อมูลเพิ่มเติม
    breakdown_data JSON COMMENT 'ข้อมูลแยกรายละเอียด',
    comparison_data JSON COMMENT 'ข้อมูลเปรียบเทียบ',
    
    -- สถานะ
    status ENUM('GENERATING', 'COMPLETED', 'ERROR') DEFAULT 'GENERATING' COMMENT 'สถานะการสร้างรายงาน',
    generated_by INT COMMENT 'ผู้สร้างรายงาน',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่อัปเดต',
    
    FOREIGN KEY (merchant_id) REFERENCES merchants(merchant_id) ON DELETE CASCADE,
    FOREIGN KEY (generated_by) REFERENCES users(user_id) ON DELETE SET NULL,
    INDEX idx_merchant_period (merchant_id, start_date, end_date),
    INDEX idx_period_type (report_period_type),
    INDEX idx_date_range (start_date, end_date),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางรายงานรายได้จากค่าธรรมเนียม';

-- ตาราง bank_channel_reports - รายงานช่องทางธนาคาร
CREATE TABLE bank_channel_reports (
    channel_report_id INT PRIMARY KEY AUTO_INCREMENT,
    merchant_id INT NOT NULL COMMENT 'รหัสร้านค้า',
    bank_account_id INT NOT NULL COMMENT 'รหัสบัญชีธนาคาร',
    
    -- ช่วงเวลารายงาน
    report_date DATE NOT NULL COMMENT 'วันที่รายงาน',
    
    -- ข้อมูลธุรกรรม
    total_transactions INT DEFAULT 0 COMMENT 'จำนวนธุรกรรมทั้งหมด',
    successful_transactions INT DEFAULT 0 COMMENT 'จำนวนธุรกรรมสำเร็จ',
    failed_transactions INT DEFAULT 0 COMMENT 'จำนวนธุรกรรมล้มเหลว',
    pending_transactions INT DEFAULT 0 COMMENT 'จำนวนธุรกรรมรอดำเนินการ',
    
    -- ยอดเงิน
    total_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดเงินรวม',
    successful_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดเงินสำเร็จ',
    failed_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดเงินล้มเหลว',
    
    -- อัตราความสำเร็จ
    success_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT 'อัตราความสำเร็จ (%)',
    average_response_time INT DEFAULT 0 COMMENT 'เวลาตอบสนองเฉลี่ย (วินาที)',
    
    -- ข้อมูลเพิ่มเติม
    hourly_breakdown JSON COMMENT 'ข้อมูลแยกรายชั่วโมง',
    error_breakdown JSON COMMENT 'ข้อมูลแยกประเภทข้อผิดพลาด',
    
    -- สถานะ
    status ENUM('GENERATING', 'COMPLETED', 'ERROR') DEFAULT 'GENERATING' COMMENT 'สถานะการสร้างรายงาน',
    generated_by INT COMMENT 'ผู้สร้างรายงาน',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่อัปเดต',
    
    FOREIGN KEY (merchant_id) REFERENCES merchants(merchant_id) ON DELETE CASCADE,
    FOREIGN KEY (bank_account_id) REFERENCES bank_accounts(bank_account_id) ON DELETE CASCADE,
    FOREIGN KEY (generated_by) REFERENCES users(user_id) ON DELETE SET NULL,
    UNIQUE KEY unique_merchant_bank_date (merchant_id, bank_account_id, report_date),
    INDEX idx_merchant_date (merchant_id, report_date),
    INDEX idx_bank_account_date (bank_account_id, report_date),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางรายงานช่องทางธนาคาร';

-- ตาราง report_schedules - ตารางการสร้างรายงานอัตโนมัติ
CREATE TABLE report_schedules (
    schedule_id INT PRIMARY KEY AUTO_INCREMENT,
    merchant_id INT COMMENT 'รหัสร้านค้า (NULL = ระดับระบบ)',

    -- ข้อมูลการตั้งค่า
    schedule_name VARCHAR(255) NOT NULL COMMENT 'ชื่อตารางการสร้างรายงาน',
    report_type ENUM('DAILY', 'MONTHLY', 'REVENUE', 'BANK_CHANNEL', 'CUSTOM') NOT NULL COMMENT 'ประเภทรายงาน',

    -- การตั้งค่าเวลา
    schedule_frequency ENUM('DAILY', 'WEEKLY', 'MONTHLY', 'QUARTERLY', 'YEARLY') NOT NULL COMMENT 'ความถี่การสร้าง',
    schedule_time TIME DEFAULT '06:00:00' COMMENT 'เวลาที่สร้างรายงาน',
    schedule_day_of_week INT COMMENT 'วันในสัปดาห์ (1=จันทร์, 7=อาทิตย์)',
    schedule_day_of_month INT COMMENT 'วันในเดือน (1-31)',

    -- ผู้รับรายงาน
    recipients JSON COMMENT 'รายชื่อผู้รับรายงาน (email)',
    delivery_method ENUM('EMAIL', 'SYSTEM', 'BOTH') DEFAULT 'SYSTEM' COMMENT 'วิธีการส่งรายงาน',

    -- การตั้งค่าเพิ่มเติม
    report_format ENUM('PDF', 'EXCEL', 'CSV', 'JSON') DEFAULT 'PDF' COMMENT 'รูปแบบรายงาน',
    report_parameters JSON COMMENT 'พารามิเตอร์รายงาน',

    -- สถานะ
    is_active TINYINT(1) DEFAULT 1 COMMENT 'เปิดใช้งานหรือไม่',

    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่อัปเดต',
    last_run_at TIMESTAMP NULL COMMENT 'วันที่รันล่าสุด',
    next_run_at TIMESTAMP NULL COMMENT 'วันที่รันครั้งถัดไป',

    FOREIGN KEY (merchant_id) REFERENCES merchants(merchant_id) ON DELETE CASCADE,
    INDEX idx_merchant_active (merchant_id, is_active),
    INDEX idx_next_run (next_run_at),
    INDEX idx_report_type (report_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางการสร้างรายงานอัตโนมัติ';

-- ตาราง report_exports - การส่งออกรายงาน
CREATE TABLE report_exports (
    export_id INT PRIMARY KEY AUTO_INCREMENT,
    merchant_id INT NOT NULL COMMENT 'รหัสร้านค้า',
    user_id INT COMMENT 'ผู้ขอส่งออกรายงาน',

    -- ข้อมูลการส่งออก
    export_type ENUM('DAILY_REPORT', 'MONTHLY_REPORT', 'REVENUE_REPORT', 'BANK_CHANNEL_REPORT', 'CUSTOM') NOT NULL COMMENT 'ประเภทการส่งออก',
    export_format ENUM('PDF', 'EXCEL', 'CSV', 'JSON') NOT NULL COMMENT 'รูปแบบการส่งออก',

    -- ช่วงเวลาข้อมูล
    start_date DATE NOT NULL COMMENT 'วันที่เริ่มต้น',
    end_date DATE NOT NULL COMMENT 'วันที่สิ้นสุด',

    -- ข้อมูลไฟล์
    file_name VARCHAR(255) COMMENT 'ชื่อไฟล์',
    file_path VARCHAR(500) COMMENT 'ที่อยู่ไฟล์',
    file_size INT COMMENT 'ขนาดไฟล์ (bytes)',

    -- สถานะ
    status ENUM('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'EXPIRED') DEFAULT 'PENDING' COMMENT 'สถานะการส่งออก',
    progress_percentage INT DEFAULT 0 COMMENT 'เปอร์เซ็นต์ความคืบหน้า',
    error_message TEXT COMMENT 'ข้อความข้อผิดพลาด',

    -- การตั้งค่าเพิ่มเติม
    export_parameters JSON COMMENT 'พารามิเตอร์การส่งออก',

    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่อัปเดต',
    completed_at TIMESTAMP NULL COMMENT 'วันที่เสร็จสิ้น',
    expires_at TIMESTAMP NULL COMMENT 'วันที่หมดอายุ',

    FOREIGN KEY (merchant_id) REFERENCES merchants(merchant_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL,
    INDEX idx_merchant_status (merchant_id, status),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางการส่งออกรายงาน';

-- ตาราง dashboard_widgets - วิดเจ็ตสำหรับ Dashboard
CREATE TABLE dashboard_widgets (
    widget_id INT PRIMARY KEY AUTO_INCREMENT,
    merchant_id INT COMMENT 'รหัสร้านค้า (NULL = ระดับระบบ)',
    user_id INT COMMENT 'รหัสผู้ใช้ (NULL = ทุกคน)',

    -- ข้อมูลวิดเจ็ต
    widget_type ENUM('BALANCE_SUMMARY', 'TRANSACTION_CHART', 'REVENUE_CHART', 'BANK_STATUS', 'RECENT_TRANSACTIONS', 'ALERTS') NOT NULL COMMENT 'ประเภทวิดเจ็ต',
    widget_title VARCHAR(255) NOT NULL COMMENT 'หัวข้อวิดเจ็ต',
    widget_position INT DEFAULT 1 COMMENT 'ตำแหน่งวิดเจ็ต',
    widget_size ENUM('SMALL', 'MEDIUM', 'LARGE', 'FULL') DEFAULT 'MEDIUM' COMMENT 'ขนาดวิดเจ็ต',

    -- การตั้งค่า
    widget_config JSON COMMENT 'การตั้งค่าวิดเจ็ต',
    refresh_interval INT DEFAULT 300 COMMENT 'ช่วงเวลาการรีเฟรช (วินาที)',

    -- สถานะ
    is_active TINYINT(1) DEFAULT 1 COMMENT 'เปิดใช้งานหรือไม่',
    is_visible TINYINT(1) DEFAULT 1 COMMENT 'แสดงหรือไม่',

    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่อัปเดต',

    FOREIGN KEY (merchant_id) REFERENCES merchants(merchant_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_merchant_user (merchant_id, user_id),
    INDEX idx_widget_type (widget_type),
    INDEX idx_position (widget_position),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางวิดเจ็ตสำหรับ Dashboard';
