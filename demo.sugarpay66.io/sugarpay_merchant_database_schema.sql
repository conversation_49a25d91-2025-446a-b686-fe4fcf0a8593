-- =====================================================
-- SugarPay Merchant Database Schema
-- ระบบฐานข้อมูลสำหรับ Merchant Payment System
-- =====================================================

-- ตาราง merchants - ข้อมูลร้านค้า
CREATE TABLE merchants (
    merchant_id INT PRIMARY KEY AUTO_INCREMENT,
    merchant_name VARCHAR(255) NOT NULL COMMENT 'ชื่อร้านค้า',
    merchant_code VARCHAR(50) UNIQUE NOT NULL COMMENT 'รหัสร้านค้า',
    
    -- ข้อมูลติดต่อ
    contact_name VARCHAR(255) COMMENT 'ชื่อผู้ติดต่อ',
    email VARCHAR(255) COMMENT 'อีเมล',
    phone VARCHAR(20) COMMENT 'เบอร์โทรศัพท์',
    address TEXT COMMENT 'ที่อยู่',
    
    -- ข้อมูลธนาคารของร้านค้า
    bank_name VARCHAR(100) COMMENT 'ชื่อธนาคาร',
    bank_account_no VARCHAR(20) COMMENT 'เลขที่บัญชี',
    bank_account_name VARCHAR(255) COMMENT 'ชื่อบัญชี',
    
    -- API Configuration
    api_key VARCHAR(255) UNIQUE NOT NULL COMMENT 'API Key สำหรับเชื่อมต่อ',
    secret_key VARCHAR(255) NOT NULL COMMENT 'Secret Key สำหรับเชื่อมต่อ',
    api_endpoint VARCHAR(500) COMMENT 'API Endpoint ของลูกค้า',
    callback_url VARCHAR(500) COMMENT 'Default Callback URL',
    ip_whitelist TEXT COMMENT 'IP Whitelist (JSON format)',
    
    -- MDR Rates
    deposit_mdr_rate DECIMAL(5,2) DEFAULT 1.50 COMMENT 'อัตราค่าธรรมเนียมฝาก (%)',
    withdraw_mdr_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT 'อัตราค่าธรรมเนียมถอน (%)',
    withdraw_fee_amount DECIMAL(10,2) DEFAULT 10.00 COMMENT 'ค่าธรรมเนียมถอนคงที่ (THB)',
    topup_mdr_rate DECIMAL(5,2) DEFAULT 1.50 COMMENT 'อัตราค่าธรรมเนียม TopUp (%)',
    settlement_fee_amount DECIMAL(10,2) DEFAULT 10.00 COMMENT 'ค่าธรรมเนียม Settlement (THB)',
    
    -- Settings
    is_auto_cancel_withdraw TINYINT(1) DEFAULT 0 COMMENT 'เปิด/ปิด การยกเลิกถอนอัตโนมัติ',
    withdraw_pin_code VARCHAR(6) COMMENT 'PIN Code สำหรับถอนเงิน',
    
    -- Status
    status ENUM('ACTIVE', 'INACTIVE', 'SUSPENDED') DEFAULT 'ACTIVE' COMMENT 'สถานะการใช้งาน',
    is_deleted TINYINT(1) DEFAULT 0 COMMENT 'ลบหรือไม่',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่อัปเดต',
    
    INDEX idx_merchant_code (merchant_code),
    INDEX idx_api_key (api_key),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางข้อมูลร้านค้า';

-- ตาราง merchant_balances - ยอดเงินของร้านค้า
CREATE TABLE merchant_balances (
    balance_id INT PRIMARY KEY AUTO_INCREMENT,
    merchant_id INT NOT NULL COMMENT 'รหัสร้านค้า',
    
    -- ยอดเงินแต่ละประเภท
    deposit_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดเงินฝาก (จากการรับชำระ)',
    withdraw_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดเงินถอน (พร้อมถอนได้)',
    frozen_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดเงินที่ถูกระงับ',
    wait_confirm_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดรอการยืนยัน',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่อัปเดต',
    
    FOREIGN KEY (merchant_id) REFERENCES merchants(merchant_id) ON DELETE CASCADE,
    UNIQUE KEY unique_merchant_balance (merchant_id),
    INDEX idx_merchant_id (merchant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางยอดเงินของร้านค้า';

-- ตาราง bank_accounts - ข้อมูลบัญชีธนาคาร
CREATE TABLE bank_accounts (
    bank_account_id INT PRIMARY KEY AUTO_INCREMENT,
    merchant_id INT NOT NULL COMMENT 'รหัสร้านค้า',
    
    -- ข้อมูลธนาคาร
    bank_type ENUM('DEPOSIT', 'WITHDRAW', 'SAVINGS') NOT NULL COMMENT 'ประเภทบัญชี',
    bank_name VARCHAR(100) NOT NULL COMMENT 'ชื่อธนาคาร',
    bank_code VARCHAR(10) NOT NULL COMMENT 'รหัสธนาคาร',
    bank_account_name VARCHAR(255) NOT NULL COMMENT 'ชื่อบัญชี',
    bank_account_no VARCHAR(20) NOT NULL COMMENT 'เลขที่บัญชี',
    bank_promtpay_no VARCHAR(20) COMMENT 'เลขพร้อมเพย์',
    
    -- API Configuration สำหรับเชื่อมต่อธนาคาร
    bank_api_endpoint VARCHAR(500) COMMENT 'API Endpoint ของธนาคาร',
    bank_api_token VARCHAR(255) COMMENT 'API Token ของธนาคาร',
    bank_api_key VARCHAR(255) COMMENT 'API Key ของธนาคาร',
    bank_ip_whitelist TEXT COMMENT 'IP Whitelist ของธนาคาร',
    
    -- ยอดเงินและการตั้งค่า
    current_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดเงินปัจจุบัน',
    minimum_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดเงินขั้นต่ำ',
    maximum_balance DECIMAL(15,2) DEFAULT *********.99 COMMENT 'ยอดเงินสูงสุด',
    daily_limit DECIMAL(15,2) DEFAULT *********.99 COMMENT 'วงเงินรายวัน',
    
    -- MDR Rates สำหรับบัญชีนี้
    mdr_rate_deposit DECIMAL(5,2) DEFAULT 1.50 COMMENT 'อัตราค่าธรรมเนียมฝาก',
    mdr_rate_withdraw DECIMAL(5,2) DEFAULT 0.00 COMMENT 'อัตราค่าธรรมเนียมถอน',
    mdr_rate_topup DECIMAL(5,2) DEFAULT 1.50 COMMENT 'อัตราค่าธรรมเนียม TopUp',
    
    -- Status และการตั้งค่า
    is_active TINYINT(1) DEFAULT 1 COMMENT 'เปิดใช้งานหรือไม่',
    is_primary TINYINT(1) DEFAULT 0 COMMENT 'บัญชีหลักหรือไม่',
    is_auto_settlement TINYINT(1) DEFAULT 0 COMMENT 'โอนเงินอัตโนมัติหรือไม่',
    priority_order INT DEFAULT 1 COMMENT 'ลำดับความสำคัญ (1 = สูงสุด)',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่อัปเดต',
    last_transaction_at TIMESTAMP NULL COMMENT 'วันที่ทำธุรกรรมล่าสุด',
    
    FOREIGN KEY (merchant_id) REFERENCES merchants(merchant_id) ON DELETE CASCADE,
    INDEX idx_merchant_bank_type (merchant_id, bank_type),
    INDEX idx_bank_code (bank_code),
    INDEX idx_is_active (is_active),
    INDEX idx_priority_order (priority_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางข้อมูลบัญชีธนาคาร';

-- ตาราง transactions - รายการธุรกรรม
CREATE TABLE transactions (
    transaction_id INT PRIMARY KEY AUTO_INCREMENT,
    merchant_id INT NOT NULL COMMENT 'รหัสร้านค้า',
    bank_account_id INT COMMENT 'รหัสบัญชีธนาคาร',
    
    -- ข้อมูลธุรกรรม
    order_id VARCHAR(100) UNIQUE NOT NULL COMMENT 'รหัสคำสั่งซื้อ',
    reference_id VARCHAR(100) COMMENT 'รหัสอ้างอิง',
    transaction_type ENUM('DEPOSIT', 'WITHDRAW', 'TOPUP', 'TRANSFER', 'SETTLEMENT') NOT NULL COMMENT 'ประเภทธุรกรรม',
    
    -- จำนวนเงิน
    amount DECIMAL(15,2) NOT NULL COMMENT 'จำนวนเงิน',
    mdr_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ค่าธรรมเนียม MDR',
    fee_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ค่าธรรมเนียมอื่นๆ',
    net_amount DECIMAL(15,2) NOT NULL COMMENT 'จำนวนเงินสุทธิ',
    
    -- ข้อมูลลูกค้า
    customer_name VARCHAR(255) COMMENT 'ชื่อลูกค้า',
    customer_bank_name VARCHAR(100) COMMENT 'ธนาคารลูกค้า',
    customer_bank_account_no VARCHAR(20) COMMENT 'เลขที่บัญชีลูกค้า',
    
    -- สถานะและข้อมูลเพิ่มเติม
    status ENUM('PENDING', 'SUCCESS', 'FAILED', 'CANCELLED', 'EXPIRED') DEFAULT 'PENDING' COMMENT 'สถานะธุรกรรม',
    callback_url VARCHAR(500) COMMENT 'Callback URL',
    callback_status ENUM('PENDING', 'SUCCESS', 'FAILED') DEFAULT 'PENDING' COMMENT 'สถานะ Callback',
    
    -- ข้อมูลสลิป/หลักฐาน
    slip_image_url VARCHAR(500) COMMENT 'URL รูปสลิป',
    qr_code_data TEXT COMMENT 'ข้อมูล QR Code',
    bank_reference VARCHAR(100) COMMENT 'รหัสอ้างอิงจากธนาคาร',
    
    -- หมายเหตุ
    description TEXT COMMENT 'รายละเอียด',
    admin_note TEXT COMMENT 'หมายเหตุจากแอดมิน',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่อัปเดต',
    completed_at TIMESTAMP NULL COMMENT 'วันที่เสร็จสิ้น',
    expired_at TIMESTAMP NULL COMMENT 'วันที่หมดอายุ',
    
    FOREIGN KEY (merchant_id) REFERENCES merchants(merchant_id) ON DELETE CASCADE,
    FOREIGN KEY (bank_account_id) REFERENCES bank_accounts(bank_account_id) ON DELETE SET NULL,
    INDEX idx_order_id (order_id),
    INDEX idx_merchant_status (merchant_id, status),
    INDEX idx_transaction_type (transaction_type),
    INDEX idx_created_at (created_at),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางรายการธุรกรรม';

-- ตาราง balance_logs - ประวัติการเปลี่ยนแปลงยอดเงิน
CREATE TABLE balance_logs (
    log_id INT PRIMARY KEY AUTO_INCREMENT,
    merchant_id INT NOT NULL COMMENT 'รหัสร้านค้า',
    transaction_id INT COMMENT 'รหัสธุรกรรมที่เกี่ยวข้อง',

    -- ประเภทการเปลี่ยนแปลง
    balance_type ENUM('DEPOSIT', 'WITHDRAW', 'FROZEN', 'WAIT_CONFIRM') NOT NULL COMMENT 'ประเภทยอดเงิน',
    action_type ENUM('INCREASE', 'DECREASE', 'TRANSFER_IN', 'TRANSFER_OUT') NOT NULL COMMENT 'ประเภทการเปลี่ยนแปลง',

    -- จำนวนเงิน
    amount DECIMAL(15,2) NOT NULL COMMENT 'จำนวนเงินที่เปลี่ยนแปลง',
    balance_before DECIMAL(15,2) NOT NULL COMMENT 'ยอดเงินก่อนเปลี่ยนแปลง',
    balance_after DECIMAL(15,2) NOT NULL COMMENT 'ยอดเงินหลังเปลี่ยนแปลง',

    -- รายละเอียด
    description TEXT COMMENT 'รายละเอียดการเปลี่ยนแปลง',
    reference_data JSON COMMENT 'ข้อมูลอ้างอิงเพิ่มเติม',

    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',

    FOREIGN KEY (merchant_id) REFERENCES merchants(merchant_id) ON DELETE CASCADE,
    FOREIGN KEY (transaction_id) REFERENCES transactions(transaction_id) ON DELETE SET NULL,
    INDEX idx_merchant_balance_type (merchant_id, balance_type),
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางประวัติการเปลี่ยนแปลงยอดเงิน';

-- ตาราง blacklist - รายการบัญชีที่ถูกบล็อก
CREATE TABLE blacklist (
    blacklist_id INT PRIMARY KEY AUTO_INCREMENT,
    merchant_id INT COMMENT 'รหัสร้านค้า (NULL = ทั้งระบบ)',

    -- ข้อมูลที่ถูกบล็อก
    blacklist_type ENUM('BANK_ACCOUNT', 'PHONE', 'IP_ADDRESS', 'EMAIL') NOT NULL COMMENT 'ประเภทการบล็อก',
    blacklist_value VARCHAR(255) NOT NULL COMMENT 'ค่าที่ถูกบล็อก',

    -- รายละเอียด
    reason TEXT COMMENT 'เหตุผลในการบล็อก',
    blocked_by VARCHAR(100) COMMENT 'ผู้ที่ทำการบล็อก',

    -- สถานะ
    is_active TINYINT(1) DEFAULT 1 COMMENT 'เปิดใช้งานหรือไม่',

    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่อัปเดต',
    expires_at TIMESTAMP NULL COMMENT 'วันที่หมดอายุ',

    FOREIGN KEY (merchant_id) REFERENCES merchants(merchant_id) ON DELETE CASCADE,
    INDEX idx_merchant_type (merchant_id, blacklist_type),
    INDEX idx_blacklist_value (blacklist_value),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางรายการบัญชีที่ถูกบล็อก';

-- ตาราง settlement_schedules - ตารางการโอนเงินอัตโนมัติ
CREATE TABLE settlement_schedules (
    schedule_id INT PRIMARY KEY AUTO_INCREMENT,
    merchant_id INT NOT NULL COMMENT 'รหัสร้านค้า',
    bank_account_id INT NOT NULL COMMENT 'บัญชีปลายทาง',

    -- การตั้งค่า
    schedule_type ENUM('DAILY', 'WEEKLY', 'MONTHLY', 'MANUAL') DEFAULT 'DAILY' COMMENT 'ประเภทการโอน',
    schedule_time TIME DEFAULT '09:00:00' COMMENT 'เวลาที่โอน',
    minimum_amount DECIMAL(15,2) DEFAULT 1000.00 COMMENT 'จำนวนเงินขั้นต่ำในการโอน',

    -- สถานะ
    is_active TINYINT(1) DEFAULT 1 COMMENT 'เปิดใช้งานหรือไม่',

    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่อัปเดต',
    last_settlement_at TIMESTAMP NULL COMMENT 'วันที่โอนล่าสุด',
    next_settlement_at TIMESTAMP NULL COMMENT 'วันที่โอนครั้งถัดไป',

    FOREIGN KEY (merchant_id) REFERENCES merchants(merchant_id) ON DELETE CASCADE,
    FOREIGN KEY (bank_account_id) REFERENCES bank_accounts(bank_account_id) ON DELETE CASCADE,
    INDEX idx_merchant_id (merchant_id),
    INDEX idx_next_settlement (next_settlement_at),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ตารางการโอนเงินอัตโนมัติ';
