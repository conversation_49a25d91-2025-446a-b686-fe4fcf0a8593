-- =====================================================
-- SugarPay Usage Examples
-- ตัวอย่างการใช้งานระบบ SugarPay
-- =====================================================

-- ตัวอย่างที่ 1: การสร้างร้านค้าใหม่
-- ===================================

-- 1.1 เพิ่มข้อมูลร้านค้า
INSERT INTO merchants (
    merchant_name, merchant_code, contact_name, email, phone, 
    bank_name, bank_account_no, bank_account_name,
    api_key, secret_key, callback_url,
    deposit_mdr_rate, withdraw_fee_amount, status
) VALUES (
    'ร้านค้าทดสอบ Tiger', 'TIGER001', 'คุณสมชาย ใจดี', 
    '<EMAIL>', '**********',
    'SCB', '**********', 'บริษัท ไทเกอร์ จำกัด',
    'api_key_tiger001_12345', 'secret_key_tiger001_67890', 
    'https://tiger001.example.com/callback',
    1.50, 10.00, 'ACTIVE'
);

-- 1.2 สร้างยอดเงินเริ่มต้นสำหรับร้านค้า
INSERT INTO merchant_balances (merchant_id, deposit_balance, withdraw_balance, frozen_balance, wait_confirm_amount) 
VALUES (1, 0.00, 0.00, 0.00, 0.00);

-- 1.3 เพิ่มบัญชีธนาคารสำหรับรับเงินฝาก
INSERT INTO bank_accounts (
    merchant_id, bank_type, bank_name, bank_code, 
    bank_account_name, bank_account_no, bank_promtpay_no,
    bank_api_endpoint, bank_api_token,
    current_balance, daily_limit, is_active, is_primary, priority_order
) VALUES (
    1, 'DEPOSIT', 'SCB', '014',
    'บริษัท ไทเกอร์ จำกัด', '**********', '**********',
    'https://api.scb.co.th/v1/', 'scb_api_token_12345',
    0.00, 1000000.00, 1, 1, 1
);

-- 1.4 เพิ่มบัญชีธนาคารสำหรับจ่ายเงินถอน
INSERT INTO bank_accounts (
    merchant_id, bank_type, bank_name, bank_code,
    bank_account_name, bank_account_no,
    bank_api_endpoint, bank_api_token,
    current_balance, daily_limit, is_active, is_primary, priority_order
) VALUES (
    1, 'WITHDRAW', 'KBANK', '004',
    'บริษัท ไทเกอร์ จำกัด', '**********',
    'https://api.kasikornbank.com/v1/', 'kbank_api_token_67890',
    0.00, 500000.00, 1, 1, 1
);

-- ตัวอย่างที่ 2: การสร้างผู้ใช้งานสำหรับร้านค้า
-- ===============================================

-- 2.1 สร้างผู้ใช้งาน Admin ของร้านค้า
INSERT INTO users (
    merchant_id, user_group_id, username, email, password_hash,
    first_name, last_name, phone, is_active
) VALUES (
    1, 3, 'tiger001_admin', '<EMAIL>', 
    '$2y$10$example_hashed_password_here',
    'สมชาย', 'ใจดี', '**********', 1
);

-- 2.2 สร้างผู้ใช้งานทั่วไปของร้านค้า
INSERT INTO users (
    merchant_id, user_group_id, username, email, password_hash,
    first_name, last_name, phone, is_active
) VALUES (
    1, 4, 'tiger001_user', '<EMAIL>',
    '$2y$10$example_hashed_password_here',
    'สมหญิง', 'รักดี', '**********', 1
);

-- ตัวอย่างที่ 3: การบันทึกธุรกรรมฝากเงิน
-- ======================================

-- 3.1 บันทึกธุรกรรมฝากเงิน 1,000 บาท
INSERT INTO transactions (
    merchant_id, bank_account_id, order_id, reference_id, transaction_type,
    amount, mdr_amount, fee_amount, net_amount,
    customer_name, customer_bank_name, customer_bank_account_no,
    status, description
) VALUES (
    1, 1, 'ORD20250121001', 'REF20250121001', 'DEPOSIT',
    1000.00, 15.00, 0.00, 985.00,
    'คุณลูกค้า ทดสอบ', 'SCB', '**********',
    'SUCCESS', 'ฝากเงินผ่านระบบ'
);

-- 3.2 อัปเดตยอดเงินฝากของร้านค้า
UPDATE merchant_balances 
SET deposit_balance = deposit_balance + 985.00,
    updated_at = CURRENT_TIMESTAMP
WHERE merchant_id = 1;

-- 3.3 บันทึกประวัติการเปลี่ยนแปลงยอดเงิน
INSERT INTO balance_logs (
    merchant_id, transaction_id, balance_type, action_type,
    amount, balance_before, balance_after, description
) VALUES (
    1, 1, 'DEPOSIT', 'INCREASE',
    985.00, 0.00, 985.00, 'เพิ่มยอดฝากจากธุรกรรม ORD20250121001'
);

-- 3.4 บันทึกรายการบัญชี (Double Entry)
-- สร้างรายการสมุดรายวันทั่วไป
INSERT INTO journal_entries (
    merchant_id, transaction_id, entry_number, entry_date, entry_type,
    description, total_debit, total_credit, status
) VALUES (
    1, 1, 'JE20250121001', '2025-01-21', 'AUTO_DEPOSIT',
    'รับเงินฝากจากลูกค้า ORD20250121001', 1000.00, 1000.00, 'POSTED'
);

-- รายการเดบิต: เงินฝากธนาคาร
INSERT INTO journal_entry_lines (
    journal_entry_id, account_id, line_number, description,
    debit_amount, credit_amount
) VALUES (
    1, 2, 1, 'รับเงินฝากจากลูกค้า',
    1000.00, 0.00
);

-- รายการเครดิต: เจ้าหนี้ร้านค้า (ยอดฝาก)
INSERT INTO journal_entry_lines (
    journal_entry_id, account_id, line_number, description,
    debit_amount, credit_amount
) VALUES (
    1, 8, 2, 'เจ้าหนี้ร้านค้า - ยอดฝาก',
    0.00, 985.00
);

-- รายการเครดิต: รายได้ค่าธรรมเนียม
INSERT INTO journal_entry_lines (
    journal_entry_id, account_id, line_number, description,
    debit_amount, credit_amount
) VALUES (
    1, 16, 3, 'รายได้ค่าธรรมเนียมฝาก',
    0.00, 15.00
);

-- ตัวอย่างที่ 4: การโอนเงินจากยอดฝากไปยอดถอน
-- ============================================

-- 4.1 บันทึกธุรกรรมโอนเงิน 500 บาท
INSERT INTO transactions (
    merchant_id, order_id, reference_id, transaction_type,
    amount, mdr_amount, fee_amount, net_amount,
    status, description
) VALUES (
    1, 'TRF20250121001', 'REF20250121002', 'TRANSFER',
    500.00, 0.00, 0.00, 500.00,
    'SUCCESS', 'โอนเงินจากยอดฝากไปยอดถอน'
);

-- 4.2 อัปเดตยอดเงินของร้านค้า
UPDATE merchant_balances 
SET deposit_balance = deposit_balance - 500.00,
    withdraw_balance = withdraw_balance + 500.00,
    updated_at = CURRENT_TIMESTAMP
WHERE merchant_id = 1;

-- 4.3 บันทึกประวัติการเปลี่ยนแปลงยอดเงิน
INSERT INTO balance_logs (
    merchant_id, transaction_id, balance_type, action_type,
    amount, balance_before, balance_after, description
) VALUES 
(1, 2, 'DEPOSIT', 'TRANSFER_OUT', 500.00, 985.00, 485.00, 'โอนออกจากยอดฝาก'),
(1, 2, 'WITHDRAW', 'TRANSFER_IN', 500.00, 0.00, 500.00, 'โอนเข้ายอดถอน');

-- ตัวอย่างที่ 5: การถอนเงิน
-- ========================

-- 5.1 บันทึกธุรกรรมถอนเงิน 300 บาท
INSERT INTO transactions (
    merchant_id, bank_account_id, order_id, reference_id, transaction_type,
    amount, mdr_amount, fee_amount, net_amount,
    customer_name, customer_bank_name, customer_bank_account_no,
    status, description
) VALUES (
    1, 2, 'WTH20250121001', 'REF20250121003', 'WITHDRAW',
    300.00, 0.00, 10.00, 300.00,
    'คุณลูกค้า ทดสอบ', 'KBANK', '**********',
    'SUCCESS', 'ถอนเงินผ่านระบบ'
);

-- 5.2 อัปเดตยอดเงินถอนของร้านค้า
UPDATE merchant_balances 
SET withdraw_balance = withdraw_balance - 300.00,
    updated_at = CURRENT_TIMESTAMP
WHERE merchant_id = 1;

-- 5.3 บันทึกประวัติการเปลี่ยนแปลงยอดเงิน
INSERT INTO balance_logs (
    merchant_id, transaction_id, balance_type, action_type,
    amount, balance_before, balance_after, description
) VALUES (
    1, 3, 'WITHDRAW', 'DECREASE',
    300.00, 500.00, 200.00, 'ลดยอดถอนจากธุรกรรม WTH20250121001'
);

-- ตัวอย่างที่ 6: การสร้างรายงานรายวัน
-- =================================

-- 6.1 สร้างรายงานรายวันสำหรับร้านค้า
INSERT INTO daily_reports (
    merchant_id, report_date, report_type,
    total_transactions, total_deposit_transactions, total_withdraw_transactions, total_transfer_transactions,
    total_deposit_amount, total_withdraw_amount, total_transfer_amount,
    total_deposit_fees, total_withdraw_fees, total_fees,
    opening_deposit_balance, closing_deposit_balance,
    opening_withdraw_balance, closing_withdraw_balance,
    status, generated_by
) VALUES (
    1, '2025-01-21', 'SUMMARY',
    3, 1, 1, 1,
    1000.00, 300.00, 500.00,
    15.00, 10.00, 25.00,
    0.00, 485.00,
    0.00, 200.00,
    'COMPLETED', 1
);

-- ตัวอย่างที่ 7: การกระทบยอด
-- =========================

-- 7.1 สร้างรายการกระทบยอดรายวัน
INSERT INTO balance_reconciliation (
    merchant_id, reconciliation_date, reconciliation_type,
    system_deposit_balance, system_withdraw_balance, system_frozen_balance,
    accounting_deposit_balance, accounting_withdraw_balance, accounting_frozen_balance,
    deposit_difference, withdraw_difference, frozen_difference,
    status, reconciled_by
) VALUES (
    1, '2025-01-21', 'DAILY',
    485.00, 200.00, 0.00,
    485.00, 200.00, 0.00,
    0.00, 0.00, 0.00,
    'BALANCED', 1
);

-- ตัวอย่างที่ 8: การค้นหาข้อมูล
-- ============================

-- 8.1 ดูยอดเงินปัจจุบันของร้านค้า
SELECT 
    m.merchant_name,
    mb.deposit_balance,
    mb.withdraw_balance,
    mb.frozen_balance,
    mb.wait_confirm_amount,
    mb.updated_at
FROM merchants m
JOIN merchant_balances mb ON m.merchant_id = mb.merchant_id
WHERE m.merchant_code = 'TIGER001';

-- 8.2 ดูธุรกรรมล่าสุด 10 รายการ
SELECT 
    t.order_id,
    t.transaction_type,
    t.amount,
    t.net_amount,
    t.status,
    t.created_at,
    m.merchant_name
FROM transactions t
JOIN merchants m ON t.merchant_id = m.merchant_id
WHERE m.merchant_code = 'TIGER001'
ORDER BY t.created_at DESC
LIMIT 10;

-- 8.3 ดูประวัติการเปลี่ยนแปลงยอดเงิน
SELECT 
    bl.balance_type,
    bl.action_type,
    bl.amount,
    bl.balance_before,
    bl.balance_after,
    bl.description,
    bl.created_at
FROM balance_logs bl
JOIN merchants m ON bl.merchant_id = m.merchant_id
WHERE m.merchant_code = 'TIGER001'
ORDER BY bl.created_at DESC;

-- 8.4 สรุปรายได้ค่าธรรมเนียมรายวัน
SELECT
    DATE(t.created_at) as transaction_date,
    SUM(CASE WHEN t.transaction_type = 'DEPOSIT' THEN t.mdr_amount ELSE 0 END) as deposit_fees,
    SUM(CASE WHEN t.transaction_type = 'WITHDRAW' THEN t.fee_amount ELSE 0 END) as withdraw_fees,
    SUM(t.mdr_amount + t.fee_amount) as total_fees
FROM transactions t
JOIN merchants m ON t.merchant_id = m.merchant_id
WHERE m.merchant_code = 'TIGER001'
    AND t.status = 'SUCCESS'
    AND t.created_at >= '2025-01-21 00:00:00'
    AND t.created_at <= '2025-01-21 23:59:59'
GROUP BY DATE(t.created_at);

-- ตัวอย่างที่ 9: การใช้งานระบบ Scan Slip
-- =======================================

-- 9.1 อัปโหลดสลิปธนาคาร
INSERT INTO slip_uploads (
    merchant_id, bank_account_id,
    slip_image_url, slip_image_path, slip_file_size, slip_file_type,
    scanned_amount, scanned_date, scanned_time,
    scanned_bank_name, scanned_account_from, scanned_account_to,
    scanned_reference, verification_status, upload_source, ip_address
) VALUES (
    1, 1,
    'https://storage.example.com/slips/slip_20250121_001.jpg',
    '/uploads/slips/2025/01/21/slip_20250121_001.jpg',
    245760, 'jpg',
    1000.00, '2025-01-21', '14:30:00',
    'SCB', '**********', '**********',
    'TXN20250121001', 'PENDING', 'WEB', '*************'
);

-- 9.2 บันทึกขั้นตอนการประมวลผลสลิป
INSERT INTO slip_processing_logs (
    slip_id, processing_step, processing_status,
    processing_details, processing_time_ms,
    processor_name, processor_version
) VALUES
(1, 'UPLOAD', 'SUCCESS', '{"file_uploaded": true, "file_validated": true}', 150, 'FileUploadProcessor', '1.0.0'),
(1, 'OCR', 'SUCCESS', '{"text_extracted": true, "confidence": 95.5}', 2500, 'OCRProcessor', '2.1.0'),
(1, 'ANALYSIS', 'SUCCESS', '{"amount_detected": 1000.00, "date_detected": "2025-01-21"}', 300, 'DataAnalyzer', '1.5.0'),
(1, 'VERIFICATION', 'SUCCESS', '{"rules_passed": 3, "rules_failed": 0}', 100, 'VerificationEngine', '1.2.0'),
(1, 'MATCHING', 'SUCCESS', '{"matched_transaction": 1, "confidence": 98.5}', 200, 'MatchingEngine', '1.3.0'),
(1, 'COMPLETION', 'SUCCESS', '{"slip_verified": true, "transaction_matched": true}', 50, 'CompletionProcessor', '1.0.0');

-- 9.3 อัปเดตสลิปหลังจากการตรวจสอบ
UPDATE slip_uploads
SET verification_status = 'VERIFIED',
    matched_transaction_id = 1,
    match_confidence = 98.50,
    verified_by = 1,
    verified_at = CURRENT_TIMESTAMP,
    processed_at = CURRENT_TIMESTAMP
WHERE slip_id = 1;

-- 9.4 สร้างกฎการตรวจสอบสลิป
INSERT INTO slip_verification_rules (
    merchant_id, rule_name, rule_description, rule_type,
    rule_conditions, rule_actions, priority_order, is_active
) VALUES (
    1, 'ตรวจสอบจำนวนเงินตรงกัน', 'ตรวจสอบว่าจำนวนเงินในสลิปตรงกับธุรกรรม',
    'AMOUNT_MATCH',
    '{"tolerance": 0.00, "required_fields": ["amount", "date"]}',
    '{"auto_verify": true, "auto_match": true}',
    1, 1
);

-- ตัวอย่างที่ 10: การใช้งานระบบการเทียบยอด
-- ==========================================

-- 10.1 นำเข้ารายการเดินบัญชีธนาคาร
INSERT INTO bank_statements (
    bank_account_id, merchant_id, statement_date, statement_time,
    transaction_type, amount, balance_after, description,
    reference_number, from_account, to_account,
    import_batch_id, import_source
) VALUES
(1, 1, '2025-01-21', '14:30:15', 'CREDIT', 1000.00, 125000.00,
 'รับโอนเงิน', 'TXN20250121001', '**********', '**********',
 'BATCH20250121001', 'API'),
(2, 1, '2025-01-21', '15:45:30', 'DEBIT', 300.00, 124700.00,
 'โอนเงิน', 'TXN20250121002', '**********', '**********',
 'BATCH20250121001', 'API');

-- 10.2 จับคู่รายการธนาคารกับธุรกรรม
UPDATE bank_statements
SET matched_transaction_id = 1,
    match_status = 'MATCHED',
    match_confidence = 100.00,
    matched_by = 1,
    matched_at = CURRENT_TIMESTAMP
WHERE statement_id = 1;

UPDATE bank_statements
SET matched_transaction_id = 3,
    match_status = 'MATCHED',
    match_confidence = 100.00,
    matched_by = 1,
    matched_at = CURRENT_TIMESTAMP
WHERE statement_id = 2;

-- 10.3 สร้างรายงานการกระทบยอด
INSERT INTO transaction_reconciliation (
    merchant_id, bank_account_id, reconciliation_date,
    start_date, end_date,
    system_total_in, system_total_out, system_transaction_count,
    bank_total_in, bank_total_out, bank_statement_count,
    difference_in, difference_out, unmatched_transactions, unmatched_statements,
    status, reconciled_by
) VALUES (
    1, 1, '2025-01-21', '2025-01-21', '2025-01-21',
    1000.00, 0.00, 1,
    1000.00, 0.00, 1,
    0.00, 0.00, 0, 0,
    'BALANCED', 1
);

-- 10.4 ค้นหารายการที่ไม่จับคู่
SELECT
    'TRANSACTION' as type,
    t.order_id as reference,
    t.amount,
    t.created_at,
    'ธุรกรรมที่ไม่มีรายการธนาคาร' as issue
FROM transactions t
LEFT JOIN bank_statements bs ON t.transaction_id = bs.matched_transaction_id
WHERE t.merchant_id = 1
    AND t.status = 'SUCCESS'
    AND t.created_at >= '2025-01-21 00:00:00'
    AND t.created_at <= '2025-01-21 23:59:59'
    AND bs.matched_transaction_id IS NULL

UNION ALL

SELECT
    'BANK_STATEMENT' as type,
    bs.reference_number as reference,
    bs.amount,
    TIMESTAMP(bs.statement_date, bs.statement_time) as created_at,
    'รายการธนาคารที่ไม่มีธุรกรรม' as issue
FROM bank_statements bs
WHERE bs.merchant_id = 1
    AND bs.statement_date = '2025-01-21'
    AND bs.match_status = 'UNMATCHED'
ORDER BY created_at;
